<?php

declare(strict_types=1);

use App\Enums\UserRole;
use App\Livewire\Admin\InvoiceApproval;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

require_once 'vendor/autoload.php';

// Bootstrap the Laravel application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Simple test to check if the component can be instantiated
try {
    echo "Testing basic component instantiation...\n";
    
    // Create basic test data
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
    
    echo "Test data created successfully\n";
    
    // Create invoices in different states (replicating the failing test)
    $draftInvoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    $submittedInvoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'submitted',
        'submitted_by' => $reviewer->id,
        'submitted_at' => now(),
    ]);

    $approvedInvoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'approved',
        'approved_by' => $reviewer->id,
        'approved_at' => now(),
    ]);

    echo "Test invoices created successfully\n";
    
    // Try to create the component
    $component = Livewire::actingAs($reviewer)->test(InvoiceApproval::class);
    echo "Component created successfully\n";
    
    // Try the exact same assertions as the failing test
    $component->assertSet('filter', 'pending');
    echo "Filter assertion passed\n";
    
    $component->assertSee('Invoice Approval');
    echo "Title assertion passed\n";
    
    $component->assertSee($draftInvoice->invoice_number);
    echo "Draft invoice assertion passed\n";
    
    $component->assertSee($submittedInvoice->invoice_number);
    echo "Submitted invoice assertion passed\n";
    
    $component->assertDontSee($approvedInvoice->invoice_number);
    echo "Approved invoice assertion passed\n";
    
    echo "All tests passed!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}