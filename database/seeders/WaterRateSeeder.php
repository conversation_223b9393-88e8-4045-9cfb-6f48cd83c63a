<?php

namespace Database\Seeders;

use App\Models\Estate;
use App\Models\WaterRate;
use Illuminate\Database\Seeder;

class WaterRateSeeder extends Seeder
{
    public function run(): void
    {
        $estates = Estate::all();

        foreach ($estates as $estate) {
            // Create a single water rate for each estate with minimum charge structure
            $baseRate = rand(20, 35); // Base rate per unit
            $minimumCharge = rand(100, 200); // Minimum charge
            $minimumUnits = rand(5, 10); // Minimum units before tiered pricing
            $fixedCharge = rand(150, 300); // Fixed monthly charge

            WaterRate::create([
                'estate_id' => $estate->id,
                'name' => 'Domestic Water Rate - '.$estate->name,
                'rate_per_unit' => $baseRate,
                'minimum_charge' => $minimumCharge,
                'minimum_units' => $minimumUnits,
                'fixed_charge' => $fixedCharge,
                'effective_from' => now()->subMonths(12),
                'effective_to' => null,
                'is_active' => true,
                'description' => 'Standard domestic water consumption rate for '.$estate->name,
            ]);
        }

        $this->command->info('Water rates seeded successfully!');
    }
}
