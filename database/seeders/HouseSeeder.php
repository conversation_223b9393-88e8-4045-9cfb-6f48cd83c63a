<?php

namespace Database\Seeders;

use App\Models\Estate;
use App\Models\House;
use Illuminate\Database\Seeder;

class HouseSeeder extends Seeder
{
    public function run(): void
    {
        $estates = Estate::all();

        foreach ($estates as $estate) {
            // Create varying numbers of houses per estate
            $houseCount = rand(6, 15);

            for ($i = 1; $i <= $houseCount; $i++) {
                $houseNumber = $this->generateHouseNumber($estate, $i);

                // Check if house already exists
                $existingHouse = House::where('estate_id', $estate->id)
                    ->where('house_number', $houseNumber)
                    ->first();

                if (! $existingHouse) {
                    House::factory()->create([
                        'estate_id' => $estate->id,
                        'house_number' => $houseNumber,
                    ]);
                }
            }
        }

        $this->command->info('Houses seeded successfully!');
    }

    private function generateHouseNumber($estate, $index)
    {
        $prefix = strtoupper(substr($estate->name, 0, 3));
        $estateCode = $estate->code;

        return $estateCode.'-'.$prefix.'-'.str_pad($index, 3, '0', STR_PAD_LEFT);
    }
}
