<?php

namespace Database\Seeders;

use App\Models\Contact;
use App\Models\House;
use Illuminate\Database\Seeder;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create contacts using existing houses
        Contact::factory()->count(200)->create()->each(function ($contact) {
            // Sync with additional houses (many-to-many relationship)
            $houses = House::inRandomOrder()->limit(rand(1, 3))->get();
            $contact->houses()->sync($houses->pluck('id')->toArray());
        });
    }
}
