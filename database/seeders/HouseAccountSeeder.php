<?php

namespace Database\Seeders;

use App\Models\House;
use App\Models\HouseAccount;
use App\Models\AccountTransaction;
use App\Models\Invoice;
use App\Models\User;
use Illuminate\Database\Seeder;

class HouseAccountSeeder extends Seeder
{
    public function run(): void
    {
        $houses = House::all();
        $adminUser = User::where('role', 'admin')->first() ?? User::first();

        foreach ($houses as $house) {
            $this->createHouseAccountWithTransactions($house, $adminUser);
        }

        $this->command->info('House accounts and transactions seeded successfully!');
    }

    private function createHouseAccountWithTransactions($house, $adminUser)
    {
        // Create house account
        $houseAccount = HouseAccount::firstOrCreate([
            'house_id' => $house->id,
        ], [
            'current_balance' => 0,
            'total_credit' => 0,
            'total_debit' => 0,
            'last_transaction_date' => now(),
        ]);

        // Only create transactions if none exist for this house account
        if ($houseAccount->transactions()->count() > 0) {
            return;
        }

        // Get invoices for this house
        $invoices = $house->invoices()->orderBy('billing_period_start')->get();
        
        $runningBalance = 0;

        foreach ($invoices as $invoice) {
            // Create invoice transaction
            $runningBalance += $invoice->total_amount;
            
            AccountTransaction::create([
                'house_account_id' => $houseAccount->id,
                'transaction_type' => 'invoice',
                'reference_type' => 'invoice',
                'reference_id' => $invoice->id,
                'amount' => $invoice->total_amount,
                'balance_before' => $runningBalance - $invoice->total_amount,
                'balance_after' => $runningBalance,
                'description' => "Invoice {$invoice->invoice_number} for {$invoice->billing_period_start->format('M Y')}",
                'user_id' => $adminUser->id,
                'created_at' => $invoice->billing_period_end,
                'updated_at' => $invoice->billing_period_end,
            ]);

            // Create payment transaction if invoice is paid
            if ($invoice->status === 'paid' && $invoice->paid_at) {
                $runningBalance -= $invoice->total_amount;
                
                AccountTransaction::create([
                    'house_account_id' => $houseAccount->id,
                    'transaction_type' => 'payment',
                    'reference_type' => 'payment',
                    'reference_id' => $invoice->id,
                    'amount' => -$invoice->total_amount,
                    'balance_before' => $runningBalance + $invoice->total_amount,
                    'balance_after' => $runningBalance,
                    'description' => "Payment for invoice {$invoice->invoice_number}",
                    'user_id' => $adminUser->id,
                    'created_at' => $invoice->paid_at,
                    'updated_at' => $invoice->paid_at,
                ]);
            }

            // Create late fee transaction for overdue invoices
            if ($invoice->status === 'overdue') {
                $lateFee = $invoice->total_amount * 0.10; // 10% late fee
                $runningBalance += $lateFee;
                
                AccountTransaction::create([
                    'house_account_id' => $houseAccount->id,
                    'transaction_type' => 'adjustment',
                    'reference_type' => 'late_fee',
                    'reference_id' => $invoice->id,
                    'amount' => $lateFee,
                    'balance_before' => $runningBalance - $lateFee,
                    'balance_after' => $runningBalance,
                    'description' => "Late fee for invoice {$invoice->invoice_number}",
                    'user_id' => $adminUser->id,
                    'created_at' => $invoice->due_date->addDays(5),
                    'updated_at' => $invoice->due_date->addDays(5),
                ]);
            }
        }

        // Update house account with final balance
        $houseAccount->update([
            'current_balance' => $runningBalance,
            'total_debit' => AccountTransaction::where('house_account_id', $houseAccount->id)
                ->where('amount', '>', 0)
                ->sum('amount'),
            'total_credit' => abs(AccountTransaction::where('house_account_id', $houseAccount->id)
                ->where('amount', '<', 0)
                ->sum('amount')),
            'last_transaction_date' => AccountTransaction::where('house_account_id', $houseAccount->id)
                ->max('created_at') ?? now(),
        ]);
    }
}