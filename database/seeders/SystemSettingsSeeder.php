<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SystemSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'app_name',
                'group' => 'general',
                'value' => 'Water Management System',
                'type' => 'string',
                'display_name' => 'Application Name',
                'description' => 'The name of your water management application',
                'is_public' => true,
                'is_required' => true,
            ],
            [
                'key' => 'app_description',
                'group' => 'general',
                'value' => 'Comprehensive water billing and management solution',
                'type' => 'string',
                'display_name' => 'Application Description',
                'description' => 'Brief description of your application',
                'is_public' => true,
                'is_required' => false,
            ],
            [
                'key' => 'timezone',
                'group' => 'general',
                'value' => 'UTC',
                'type' => 'string',
                'display_name' => 'Timezone',
                'description' => 'Default timezone for the application',
                'is_public' => false,
                'is_required' => true,
            ],
            [
                'key' => 'currency',
                'group' => 'general',
                'value' => 'USD',
                'type' => 'string',
                'display_name' => 'Currency Code',
                'description' => 'Default currency code (e.g., USD, EUR, GBP)',
                'is_public' => true,
                'is_required' => true,
            ],
            [
                'key' => 'currency_symbol',
                'group' => 'general',
                'value' => '$',
                'type' => 'string',
                'display_name' => 'Currency Symbol',
                'description' => 'Symbol to display for currency',
                'is_public' => true,
                'is_required' => true,
            ],

            // Water Rate Settings
            [
                'key' => 'default_water_rate',
                'group' => 'water_rates',
                'value' => '0.50',
                'type' => 'float',
                'display_name' => 'Default Water Rate',
                'description' => 'Default rate per unit of water consumption',
                'is_public' => false,
                'is_required' => true,
            ],
            [
                'key' => 'minimum_charge',
                'group' => 'water_rates',
                'value' => '10.00',
                'type' => 'float',
                'display_name' => 'Minimum Charge',
                'description' => 'Minimum charge regardless of consumption',
                'is_public' => false,
                'is_required' => true,
            ],
            [
                'key' => 'late_fee_percentage',
                'group' => 'billing',
                'value' => '5.0',
                'type' => 'float',
                'display_name' => 'Late Fee Percentage',
                'description' => 'Percentage charged as late fee on overdue invoices',
                'is_public' => false,
                'is_required' => true,
            ],
            [
                'key' => 'grace_period_days',
                'group' => 'billing',
                'value' => '30',
                'type' => 'integer',
                'display_name' => 'Grace Period (Days)',
                'description' => 'Number of days before late fees are applied',
                'is_public' => false,
                'is_required' => true,
            ],

            // Billing Settings
            [
                'key' => 'billing_cycle',
                'group' => 'billing',
                'value' => 'monthly',
                'type' => 'string',
                'display_name' => 'Billing Cycle',
                'description' => 'How often invoices are generated',
                'is_public' => false,
                'is_required' => true,
                'options' => json_encode(['monthly', 'quarterly', 'annually']),
            ],
            [
                'key' => 'invoice_due_days',
                'group' => 'billing',
                'value' => '30',
                'type' => 'integer',
                'display_name' => 'Invoice Due Days',
                'description' => 'Number of days from invoice date until due',
                'is_public' => false,
                'is_required' => true,
            ],
            [
                'key' => 'auto_generate_invoices',
                'group' => 'billing',
                'value' => 'true',
                'type' => 'boolean',
                'display_name' => 'Auto Generate Invoices',
                'description' => 'Automatically generate invoices based on billing cycle',
                'is_public' => false,
                'is_required' => false,
            ],

            // WhatsApp Settings
            [
                'key' => 'whatsapp_enabled',
                'group' => 'whatsapp',
                'value' => 'false',
                'type' => 'boolean',
                'display_name' => 'Enable WhatsApp',
                'description' => 'Enable WhatsApp messaging functionality',
                'is_public' => false,
                'is_required' => false,
            ],
            [
                'key' => 'whatsapp_api_url',
                'group' => 'whatsapp',
                'value' => '',
                'type' => 'string',
                'display_name' => 'WhatsApp API URL',
                'description' => 'URL for WhatsApp API endpoint',
                'is_public' => false,
                'is_required' => false,
            ],
            [
                'key' => 'whatsapp_token',
                'group' => 'whatsapp',
                'value' => '',
                'type' => 'string',
                'display_name' => 'WhatsApp API Token',
                'description' => 'Authentication token for WhatsApp API',
                'is_public' => false,
                'is_required' => false,
            ],

            // Email Settings
            [
                'key' => 'email_notifications',
                'group' => 'email',
                'value' => 'true',
                'type' => 'boolean',
                'display_name' => 'Enable Email Notifications',
                'description' => 'Send email notifications for various events',
                'is_public' => false,
                'is_required' => false,
            ],
            [
                'key' => 'admin_email',
                'group' => 'email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'display_name' => 'Admin Email',
                'description' => 'Email address for system administrator',
                'is_public' => false,
                'is_required' => true,
            ],
            [
                'key' => 'from_email',
                'group' => 'email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'display_name' => 'From Email',
                'description' => 'Email address used as sender for system emails',
                'is_public' => false,
                'is_required' => true,
            ],

            // System Settings
            [
                'key' => 'maintenance_mode',
                'group' => 'system',
                'value' => 'false',
                'type' => 'boolean',
                'display_name' => 'Maintenance Mode',
                'description' => 'Put the system in maintenance mode',
                'is_public' => false,
                'is_required' => false,
            ],
            [
                'key' => 'session_timeout',
                'group' => 'system',
                'value' => '120',
                'type' => 'integer',
                'display_name' => 'Session Timeout (Minutes)',
                'description' => 'How long user sessions remain active',
                'is_public' => false,
                'is_required' => true,
            ],
        ];

        foreach ($settings as $setting) {
            \App\Models\SystemSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
