<?php

namespace Database\Seeders;

use App\Models\Estate;
use App\Models\House;
use App\Models\User;
use App\Models\WhatsAppMessage;
use Illuminate\Database\Seeder;

class WhatsAppMessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting WhatsApp message seeding...');

        // Get estates with houses and contacts
        $estates = Estate::with(['houses.contacts'])->limit(3)->get();

        if ($estates->isEmpty()) {
            $this->command->info('No estates found. Creating sample estates...');
            $estates = Estate::factory()->count(3)->create();
        }

        // Get users to use as senders
        $users = User::limit(5)->get();
        if ($users->isEmpty()) {
            $users = User::factory()->count(5)->create();
        }

        $messageCount = 0;

        foreach ($estates as $estate) {
            $this->command->info("Processing estate: {$estate->name}");

            // Ensure estate has houses
            if ($estate->houses->isEmpty()) {
                $this->command->info("Estate {$estate->name} has no houses, skipping...");

                continue;
            }

            foreach ($estate->houses as $house) {
                // Ensure house has contacts with WhatsApp numbers
                if ($house->contacts->isEmpty()) {
                    $this->command->info("House {$house->house_number} has no contacts, skipping...");

                    continue;
                }

                // Filter contacts with WhatsApp numbers and active status
                $whatsappContacts = $house->contacts->filter(function ($contact) {
                    return ! empty($contact->whatsapp_number) &&
                           $contact->is_active &&
                           $contact->receive_notifications;
                });

                if ($whatsappContacts->isEmpty()) {
                    $this->command->info("House {$house->house_number} has no WhatsApp contacts, skipping...");

                    continue;
                }

                // Get existing invoices for this house
                $invoices = $house->invoices;
                if ($invoices->isEmpty()) {
                    $this->command->info("House {$house->house_number} has no invoices, skipping invoice messages...");
                    $invoices = collect();
                }

                foreach ($invoices as $invoice) {
                    // Create outgoing messages (system to resident)
                    $messageTypes = [
                        'invoice_delivery',
                        'payment_reminder',
                        'payment_confirmation',
                    ];

                    foreach ($messageTypes as $type) {
                        if (rand(1, 100) <= 70) { // 70% chance to create message
                            $contact = $whatsappContacts->random();
                            $sender = $users->random();

                            $message = WhatsAppMessage::factory()
                                ->forInvoice($invoice)
                                ->outgoing()
                                ->create([
                                    'sender_id' => $sender->id,
                                    'recipient_contact_id' => $contact->id,
                                    'recipient' => $contact->whatsapp_number,
                                    'template_name' => $type,
                                ]);

                            $messageCount++;
                        }
                    }

                    // Create some incoming messages (resident to system) - inquiries about invoices
                    if (rand(1, 100) <= 30) { // 30% chance
                        $contact = $whatsappContacts->random();
                        $sender = $users->random();

                        WhatsAppMessage::factory()
                            ->forInvoice($invoice)
                            ->incoming()
                            ->create([
                                'sender_id' => $sender->id,
                                'recipient_contact_id' => $contact->id,
                                'recipient' => $contact->whatsapp_number,
                                'content' => fake()->randomElement([
                                    'I have a question about this invoice',
                                    'Can you explain the charges on this bill?',
                                    'When is this payment due?',
                                    'Is there a payment plan available?',
                                ]),
                            ]);

                        $messageCount++;
                    }
                }

                // Create some direct house messages (announcements, etc.)
                if (rand(1, 100) <= 30) { // 30% chance
                    $contact = $whatsappContacts->random();
                    $sender = $users->random();

                    WhatsAppMessage::factory()
                        ->forHouse($house)
                        ->outgoing()
                        ->create([
                            'sender_id' => $sender->id,
                            'recipient_contact_id' => $contact->id,
                            'recipient' => $contact->whatsapp_number,
                            'template_name' => 'estate_announcement',
                            'content' => fake()->randomElement([
                                'Water maintenance scheduled for tomorrow',
                                'Community meeting this weekend',
                                'Payment office hours updated',
                                'New water rates effective next month',
                            ]),
                        ]);

                    $messageCount++;
                }

                // Create some resident inquiries (incoming messages)
                if (rand(1, 100) <= 40) { // 40% chance
                    $contact = $whatsappContacts->random();
                    $sender = $users->random();

                    WhatsAppMessage::factory()
                        ->forHouse($house)
                        ->incoming()
                        ->create([
                            'sender_id' => $sender->id,
                            'recipient_contact_id' => $contact->id,
                            'recipient' => $contact->whatsapp_number,
                            'content' => fake()->randomElement([
                                'When is the next meter reading?',
                                'I need to update my contact information',
                                'There seems to be a water leak in my area',
                                'Can I get a copy of my last bill?',
                                'What are the payment methods available?',
                            ]),
                        ]);

                    $messageCount++;
                }
            }
        }

        // Create some estate-wide messages (outgoing broadcasts)
        foreach ($estates as $estate) {
            if (rand(1, 100) <= 50) { // 50% chance
                $houses = $estate->houses;
                foreach ($houses as $house) {
                    $contacts = $house->contacts->filter(function ($contact) {
                        return ! empty($contact->whatsapp_number) &&
                               $contact->is_active &&
                               $contact->receive_notifications;
                    });

                    if ($contacts->isNotEmpty()) {
                        $contact = $contacts->random();
                        $sender = $users->random();

                        WhatsAppMessage::factory()
                            ->forEstate($estate)
                            ->outgoing()
                            ->create([
                                'sender_id' => $sender->id,
                                'recipient_contact_id' => $contact->id,
                                'recipient' => $contact->whatsapp_number,
                                'template_name' => 'estate_broadcast',
                                'content' => fake()->randomElement([
                                    'Estate-wide water shutdown scheduled for maintenance',
                                    'New community center now open to all residents',
                                    'Annual general meeting next Tuesday at 6 PM',
                                    'Water quality report available for review',
                                ]),
                            ]);

                        $messageCount++;
                    }
                }
            }
        }

        $this->command->info("WhatsApp messages seeded successfully. Created {$messageCount} messages.");
    }
}
