<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class Rbac<PERSON>eeder extends Seeder
{
    public function run()
    {
        // Seed user roles (ignore duplicates)
        $roles = [
            ['value' => 'admin', 'label' => 'System Administrator', 'description' => 'System-wide administrator with full access to all features and settings', 'created_at' => now(), 'updated_at' => now()],
            ['value' => 'manager', 'label' => 'Estate Manager', 'description' => 'Estate manager with oversight of assigned estates and team management', 'created_at' => now(), 'updated_at' => now()],
            ['value' => 'reviewer', 'label' => 'Reviewer/Accountant', 'description' => 'Accountant who reviews readings and manages billing for assigned estates', 'created_at' => now(), 'updated_at' => now()],
            ['value' => 'caretaker', 'label' => 'Caretaker Staff', 'description' => 'Field staff who enters meter readings and updates contact information', 'created_at' => now(), 'updated_at' => now()],
            ['value' => 'resident', 'label' => 'Resident/Tenant', 'description' => 'Tenant or house owner with access to personal billing and usage information', 'created_at' => now(), 'updated_at' => now()],
        ];

        foreach ($roles as $role) {
            DB::table('user_roles')->updateOrInsert(
                ['value' => $role['value']],
                array_diff_key($role, ['value' => ''])
            );
        }

        // Seed permissions
        $permissions = [
            // Dashboard permissions
            ['name' => 'view-admin-dashboard', 'display_name' => 'View Admin Dashboard', 'description' => 'Access to administrator dashboard', 'category' => 'dashboard', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'view-manager-dashboard', 'display_name' => 'View Manager Dashboard', 'description' => 'Access to estate manager dashboard', 'category' => 'dashboard', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'view-reviewer-dashboard', 'display_name' => 'View Reviewer Dashboard', 'description' => 'Access to reviewer/accountant dashboard', 'category' => 'dashboard', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'view-caretaker-dashboard', 'display_name' => 'View Caretaker Dashboard', 'description' => 'Access to caretaker staff dashboard', 'category' => 'dashboard', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'view-resident-dashboard', 'display_name' => 'View Resident Dashboard', 'description' => 'Access to resident self-service dashboard', 'category' => 'dashboard', 'requires_estate_assignment' => false, 'system_level_only' => false],

            // Estate permissions
            ['name' => 'estates.view_all', 'display_name' => 'View All Estates', 'description' => 'Access to view all estates in the system', 'category' => 'estate', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'estates.view_assigned', 'display_name' => 'View Assigned Estates', 'description' => 'Access to view estates assigned to user', 'category' => 'estate', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'estates.manage_all', 'display_name' => 'Manage All Estates', 'description' => 'Full management access to all estates', 'category' => 'estate', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'estates.manage_assigned', 'display_name' => 'Manage Assigned Estates', 'description' => 'Full management access to assigned estates', 'category' => 'estate', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'estates.create', 'display_name' => 'Create Estates', 'description' => 'Create new estates in the system', 'category' => 'estate', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'estates.edit_all', 'display_name' => 'Edit All Estates', 'description' => 'Edit any estate in the system', 'category' => 'estate', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'estates.edit_assigned', 'display_name' => 'Edit Assigned Estates', 'description' => 'Edit estates assigned to user', 'category' => 'estate', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'estates.delete', 'display_name' => 'Delete Estates', 'description' => 'Delete estates from the system', 'category' => 'estate', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'estates.analytics', 'display_name' => 'View Estate Analytics', 'description' => 'Access to estate analytics and reports', 'category' => 'estate', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // House permissions
            ['name' => 'houses.view_all', 'display_name' => 'View All Houses', 'description' => 'Access to view all houses in the system', 'category' => 'house', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'houses.view_assigned', 'display_name' => 'View Assigned Houses', 'description' => 'Access to view houses in assigned estates', 'category' => 'house', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'houses.view_own', 'display_name' => 'View Own House', 'description' => 'Access to view own house details', 'category' => 'house', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'houses.manage_all', 'display_name' => 'Manage All Houses', 'description' => 'Full management access to all houses', 'category' => 'house', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'houses.manage_assigned', 'display_name' => 'Manage Assigned Houses', 'description' => 'Full management access to houses in assigned estates', 'category' => 'house', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'houses.create', 'display_name' => 'Create Houses', 'description' => 'Create new houses in estates', 'category' => 'house', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'houses.edit_all', 'display_name' => 'Edit All Houses', 'description' => 'Edit any house in the system', 'category' => 'house', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'houses.edit_assigned', 'display_name' => 'Edit Assigned Houses', 'description' => 'Edit houses in assigned estates', 'category' => 'house', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'houses.delete', 'display_name' => 'Delete Houses', 'description' => 'Delete houses from the system', 'category' => 'house', 'requires_estate_assignment' => false, 'system_level_only' => true],

            // Contact permissions
            ['name' => 'contacts.view_all', 'display_name' => 'View All Contacts', 'description' => 'Access to view all contacts in the system', 'category' => 'contact', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'contacts.view_assigned', 'display_name' => 'View Assigned Contacts', 'description' => 'Access to view contacts in assigned estates', 'category' => 'contact', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'contacts.view_own', 'display_name' => 'View Own Contacts', 'description' => 'Access to view own contact details', 'category' => 'contact', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'contacts.manage_all', 'display_name' => 'Manage All Contacts', 'description' => 'Full management access to all contacts', 'category' => 'contact', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'contacts.manage_assigned', 'display_name' => 'Manage Assigned Contacts', 'description' => 'Full management access to contacts in assigned estates', 'category' => 'contact', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'contacts.create', 'display_name' => 'Create Contacts', 'description' => 'Create new contacts for houses', 'category' => 'contact', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'contacts.delete', 'display_name' => 'Delete Contacts', 'description' => 'Delete contacts from the system', 'category' => 'contact', 'requires_estate_assignment' => false, 'system_level_only' => true],

            // Reading permissions
            ['name' => 'readings.view_all', 'display_name' => 'View All Readings', 'description' => 'Access to view all meter readings', 'category' => 'reading', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'readings.view_assigned', 'display_name' => 'View Assigned Readings', 'description' => 'Access to view meter readings in assigned estates', 'category' => 'reading', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'readings.view_own', 'display_name' => 'View Own Readings', 'description' => 'Access to view own meter readings', 'category' => 'reading', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'readings.create_all', 'display_name' => 'Create All Readings', 'description' => 'Create meter readings for any estate', 'category' => 'reading', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'readings.create_assigned', 'display_name' => 'Create Assigned Readings', 'description' => 'Create meter readings for assigned estates', 'category' => 'reading', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'readings.edit_all', 'display_name' => 'Edit All Readings', 'description' => 'Edit any meter reading in the system', 'category' => 'reading', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'readings.edit_assigned', 'display_name' => 'Edit Assigned Readings', 'description' => 'Edit meter readings in assigned estates', 'category' => 'reading', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'readings.delete', 'display_name' => 'Delete Readings', 'description' => 'Delete meter readings from the system', 'category' => 'reading', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'readings.review_all', 'display_name' => 'Review All Readings', 'description' => 'Review meter readings for all estates', 'category' => 'reading', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'readings.review_assigned', 'display_name' => 'Review Assigned Readings', 'description' => 'Review meter readings for assigned estates', 'category' => 'reading', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'readings.approve_all', 'display_name' => 'Approve All Readings', 'description' => 'Approve meter readings for all estates', 'category' => 'reading', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'readings.approve_assigned', 'display_name' => 'Approve Assigned Readings', 'description' => 'Approve meter readings for assigned estates', 'category' => 'reading', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'readings.validate', 'display_name' => 'Validate Readings', 'description' => 'Validate meter readings for accuracy', 'category' => 'reading', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Invoice permissions
            ['name' => 'invoices.view_all', 'display_name' => 'View All Invoices', 'description' => 'Access to view all invoices in the system', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'invoices.view_assigned', 'display_name' => 'View Assigned Invoices', 'description' => 'Access to view invoices for assigned estates', 'category' => 'billing', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'invoices.view_own', 'display_name' => 'View Own Invoices', 'description' => 'Access to view own invoices', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'invoices.generate_all', 'display_name' => 'Generate All Invoices', 'description' => 'Generate invoices for all estates', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'invoices.generate_assigned', 'display_name' => 'Generate Assigned Invoices', 'description' => 'Generate invoices for assigned estates', 'category' => 'billing', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'invoices.create_manual', 'display_name' => 'Create Manual Invoices', 'description' => 'Create manual invoices for any estate', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'invoices.edit_all', 'display_name' => 'Edit All Invoices', 'description' => 'Edit any invoice in the system', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'invoices.edit_assigned', 'display_name' => 'Edit Assigned Invoices', 'description' => 'Edit invoices for assigned estates', 'category' => 'billing', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'invoices.delete', 'display_name' => 'Delete Invoices', 'description' => 'Delete invoices from the system', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'invoices.send_all', 'display_name' => 'Send All Invoices', 'description' => 'Send invoices to all customers', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'invoices.send_assigned', 'display_name' => 'Send Assigned Invoices', 'description' => 'Send invoices to customers in assigned estates', 'category' => 'billing', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'invoices.adjust_all', 'display_name' => 'Adjust All Invoices', 'description' => 'Adjust any invoice in the system', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'invoices.adjust_assigned', 'display_name' => 'Adjust Assigned Invoices', 'description' => 'Adjust invoices for assigned estates', 'category' => 'billing', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'invoices.export_all', 'display_name' => 'Export All Invoices', 'description' => 'Export invoice data for all estates', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'invoices.export_assigned', 'display_name' => 'Export Assigned Invoices', 'description' => 'Export invoice data for assigned estates', 'category' => 'billing', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Rate permissions
            ['name' => 'rates.view_all', 'display_name' => 'View All Rates', 'description' => 'Access to view all water rates', 'category' => 'rates', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'rates.view_assigned', 'display_name' => 'View Assigned Rates', 'description' => 'Access to view water rates for assigned estates', 'category' => 'rates', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'rates.manage_all', 'display_name' => 'Manage All Rates', 'description' => 'Full management access to all water rates', 'category' => 'rates', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'rates.create', 'display_name' => 'Create Rates', 'description' => 'Create new water rates', 'category' => 'rates', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'rates.edit_all', 'display_name' => 'Edit All Rates', 'description' => 'Edit any water rate in the system', 'category' => 'rates', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'rates.delete', 'display_name' => 'Delete Rates', 'description' => 'Delete water rates from the system', 'category' => 'rates', 'requires_estate_assignment' => false, 'system_level_only' => true],

            // Report permissions
            ['name' => 'reports.view_all', 'display_name' => 'View All Reports', 'description' => 'Access to view all system reports', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'reports.view_assigned', 'display_name' => 'View Assigned Reports', 'description' => 'Access to view reports for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'reports.view_own', 'display_name' => 'View Own Reports', 'description' => 'Access to view personal reports', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'reports.generate_all', 'display_name' => 'Generate All Reports', 'description' => 'Generate reports for all estates', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'reports.generate_assigned', 'display_name' => 'Generate Assigned Reports', 'description' => 'Generate reports for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Analytics permissions
            ['name' => 'analytics.view_all', 'display_name' => 'View All Analytics', 'description' => 'Access to view analytics for all estates', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'analytics.view_assigned', 'display_name' => 'View Assigned Analytics', 'description' => 'Access to view analytics for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'analytics.view_own', 'display_name' => 'View Own Analytics', 'description' => 'Access to view personal analytics', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => false],

            // Export permissions
            ['name' => 'export.data_all', 'display_name' => 'Export All Data', 'description' => 'Export data for all estates', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'export.data_assigned', 'display_name' => 'Export Assigned Data', 'description' => 'Export data for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'export.data_own', 'display_name' => 'Export Own Data', 'description' => 'Export personal data', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => false],

            // User permissions
            ['name' => 'users.view_all', 'display_name' => 'View All Users', 'description' => 'Access to view all users in the system', 'category' => 'users', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'users.view_assigned', 'display_name' => 'View Assigned Users', 'description' => 'Access to view users in assigned estates', 'category' => 'users', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'users.create_all', 'display_name' => 'Create All Users', 'description' => 'Create users for any role and estate', 'category' => 'users', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'users.create_assigned', 'display_name' => 'Create Assigned Users', 'description' => 'Create users for assigned estates', 'category' => 'users', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'users.edit_all', 'display_name' => 'Edit All Users', 'description' => 'Edit any user in the system', 'category' => 'users', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'users.edit_assigned', 'display_name' => 'Edit Assigned Users', 'description' => 'Edit users in assigned estates', 'category' => 'users', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'users.delete_all', 'display_name' => 'Delete All Users', 'description' => 'Delete any user from the system', 'category' => 'users', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'users.assign_estates', 'display_name' => 'Assign Estates to Users', 'description' => 'Assign estates to users', 'category' => 'users', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'users.assign_roles', 'display_name' => 'Assign Roles to Users', 'description' => 'Assign roles to users', 'category' => 'users', 'requires_estate_assignment' => false, 'system_level_only' => true],

            // System permissions
            ['name' => 'system.settings.view', 'display_name' => 'View System Settings', 'description' => 'Access to view system settings', 'category' => 'system', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'system.settings.manage', 'display_name' => 'Manage System Settings', 'description' => 'Full management access to system settings', 'category' => 'system', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'audit.logs.view', 'display_name' => 'View Audit Logs', 'description' => 'Access to view system audit logs', 'category' => 'system', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'audit.logs.export', 'display_name' => 'Export Audit Logs', 'description' => 'Export audit log data', 'category' => 'system', 'requires_estate_assignment' => false, 'system_level_only' => true],

            // WhatsApp permissions
            ['name' => 'whatsapp.settings', 'display_name' => 'Manage WhatsApp Settings', 'description' => 'Manage WhatsApp integration settings', 'category' => 'system', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'whatsapp.send_all', 'display_name' => 'Send All WhatsApp Messages', 'description' => 'Send WhatsApp messages to all customers', 'category' => 'system', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'whatsapp.send_assigned', 'display_name' => 'Send Assigned WhatsApp Messages', 'description' => 'Send WhatsApp messages to customers in assigned estates', 'category' => 'system', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'whatsapp.logs.view', 'display_name' => 'View WhatsApp Logs', 'description' => 'Access to view WhatsApp message logs', 'category' => 'system', 'requires_estate_assignment' => false, 'system_level_only' => true],

            // Account permissions
            ['name' => 'accounts.view_all', 'display_name' => 'View All Accounts', 'description' => 'Access to view all house accounts', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'accounts.view_assigned', 'display_name' => 'View Assigned Accounts', 'description' => 'Access to view accounts in assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'accounts.view_own', 'display_name' => 'View Own Account', 'description' => 'Access to view own house account', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'accounts.manage_all', 'display_name' => 'Manage All Accounts', 'description' => 'Full management access to all house accounts', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'accounts.manage_assigned', 'display_name' => 'Manage Assigned Accounts', 'description' => 'Full management access to accounts in assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'accounts.view_balance_all', 'display_name' => 'View All Balances', 'description' => 'Access to view balances for all accounts', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'accounts.view_balance_assigned', 'display_name' => 'View Assigned Balances', 'description' => 'Access to view balances for assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'accounts.view_balance_own', 'display_name' => 'View Own Balance', 'description' => 'Access to view own account balance', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'accounts.view_balance_list_assigned', 'display_name' => 'View Balance List Assigned', 'description' => 'Access to view balance list for assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'accounts.view_transactions_all', 'display_name' => 'View All Transactions', 'description' => 'Access to view transactions for all accounts', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'accounts.view_transactions_assigned', 'display_name' => 'View Assigned Transactions', 'description' => 'Access to view transactions for assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'accounts.view_transactions_own', 'display_name' => 'View Own Transactions', 'description' => 'Access to view own account transactions', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'accounts.create_transaction_assigned', 'display_name' => 'Create Assigned Transactions', 'description' => 'Create transactions for accounts in assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'accounts.edit_transaction_assigned', 'display_name' => 'Edit Assigned Transactions', 'description' => 'Edit transactions for accounts in assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'accounts.view_statement_all', 'display_name' => 'View All Statements', 'description' => 'Access to view statements for all accounts', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'accounts.view_statement_assigned', 'display_name' => 'View Assigned Statements', 'description' => 'Access to view statements for assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'accounts.view_statement_own', 'display_name' => 'View Own Statement', 'description' => 'Access to view own account statement', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'accounts.export_statement_all', 'display_name' => 'Export All Statements', 'description' => 'Export statements for all accounts', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'accounts.export_statement_assigned', 'display_name' => 'Export Assigned Statements', 'description' => 'Export statements for assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'accounts.export_statement_own', 'display_name' => 'Export Own Statement', 'description' => 'Export own account statement', 'category' => 'accounts', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'accounts.adjust_balance_assigned', 'display_name' => 'Adjust Assigned Balances', 'description' => 'Adjust balances for accounts in assigned estates', 'category' => 'accounts', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Payment permissions
            ['name' => 'payments.view_all', 'display_name' => 'View All Payments', 'description' => 'Access to view payments for all accounts', 'category' => 'payments', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'payments.view_assigned', 'display_name' => 'View Assigned Payments', 'description' => 'Access to view payments for assigned estates', 'category' => 'payments', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'payments.view_own', 'display_name' => 'View Own Payments', 'description' => 'Access to view own payments', 'category' => 'payments', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'payments.view_history_own', 'display_name' => 'View Own Payment History', 'description' => 'Access to view own payment history', 'category' => 'payments', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'payments.approve_all', 'display_name' => 'Approve All Payments', 'description' => 'Approve payments for all accounts', 'category' => 'payments', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'payments.approve_assigned', 'display_name' => 'Approve Assigned Payments', 'description' => 'Approve payments for assigned estates', 'category' => 'payments', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'payments.create_assigned', 'display_name' => 'Create Assigned Payments', 'description' => 'Create payments for assigned estates', 'category' => 'payments', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'payments.edit_assigned', 'display_name' => 'Edit Assigned Payments', 'description' => 'Edit payments for assigned estates', 'category' => 'payments', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'payments.export_all', 'display_name' => 'Export All Payments', 'description' => 'Export payment data for all accounts', 'category' => 'payments', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'payments.export_assigned', 'display_name' => 'Export Assigned Payments', 'description' => 'Export payment data for assigned estates', 'category' => 'payments', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'payments.reconcile_assigned', 'display_name' => 'Reconcile Assigned Payments', 'description' => 'Reconcile payments for assigned estates', 'category' => 'payments', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Enhanced Report permissions
            ['name' => 'reports.aging_all', 'display_name' => 'View All Aging Reports', 'description' => 'Access to aging reports for all estates', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'reports.aging_assigned', 'display_name' => 'View Assigned Aging Reports', 'description' => 'Access to aging reports for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'reports.revenue_all', 'display_name' => 'View All Revenue Reports', 'description' => 'Access to revenue reports for all estates', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'reports.revenue_assigned', 'display_name' => 'View Assigned Revenue Reports', 'description' => 'Access to revenue reports for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'reports.billing_all', 'display_name' => 'View All Billing Reports', 'description' => 'Access to billing reports for all estates', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'reports.billing_assigned', 'display_name' => 'View Assigned Billing Reports', 'description' => 'Access to billing reports for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'reports.customer_statements_all', 'display_name' => 'View All Customer Statements', 'description' => 'Access to customer statements for all estates', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'reports.customer_statements_assigned', 'display_name' => 'View Assigned Customer Statements', 'description' => 'Access to customer statements for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'reports.financial_all', 'display_name' => 'View All Financial Reports', 'description' => 'Access to financial reports for all estates', 'category' => 'reports', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'reports.financial_assigned', 'display_name' => 'View Assigned Financial Reports', 'description' => 'Access to financial reports for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'reports.balance_list_assigned', 'display_name' => 'View Assigned Balance List', 'description' => 'Access to balance list for assigned estates', 'category' => 'reports', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Enhanced Invoice permissions
            ['name' => 'invoices.approve_all', 'display_name' => 'Approve All Invoices', 'description' => 'Approve invoices for all estates', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => true],
            ['name' => 'invoices.approve_assigned', 'display_name' => 'Approve Assigned Invoices', 'description' => 'Approve invoices for assigned estates', 'category' => 'billing', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'invoices.download_own', 'display_name' => 'Download Own Invoices', 'description' => 'Download own invoices as PDF', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'invoices.view_payments_own', 'display_name' => 'View Own Invoice Payments', 'description' => 'View payments for own invoices', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'invoices.view_adjustments_own', 'display_name' => 'View Own Invoice Adjustments', 'description' => 'View adjustments for own invoices', 'category' => 'billing', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'invoices.view_status_assigned', 'display_name' => 'View Assigned Invoice Status', 'description' => 'View status of invoices in assigned estates', 'category' => 'billing', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'invoices.delete_assigned', 'display_name' => 'Delete Assigned Invoices', 'description' => 'Delete invoices in assigned estates', 'category' => 'billing', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Enhanced Rate permissions
            ['name' => 'rates.edit_assigned', 'display_name' => 'Edit Assigned Rates', 'description' => 'Edit water rates for assigned estates', 'category' => 'rates', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Enhanced WhatsApp permissions
            ['name' => 'whatsapp.send_invoices_assigned', 'display_name' => 'Send Assigned Invoices via WhatsApp', 'description' => 'Send invoices to customers in assigned estates via WhatsApp', 'category' => 'system', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Enhanced Resident permissions
            ['name' => 'resident.payments.create', 'display_name' => 'Create Resident Payments', 'description' => 'Create payments as a resident', 'category' => 'resident', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'resident.invoices.download', 'display_name' => 'Download Resident Invoices', 'description' => 'Download invoices as a resident', 'category' => 'resident', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'resident.inquiries.view_assigned', 'display_name' => 'View Assigned Resident Inquiries', 'description' => 'View resident inquiries for assigned estates', 'category' => 'resident', 'requires_estate_assignment' => true, 'system_level_only' => false],

            // Original Resident permissions
            ['name' => 'resident.portal.access', 'display_name' => 'Access Resident Portal', 'description' => 'Access to resident self-service portal', 'category' => 'resident', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'resident.inquiries.create', 'display_name' => 'Create Resident Inquiries', 'description' => 'Create inquiries as a resident', 'category' => 'resident', 'requires_estate_assignment' => false, 'system_level_only' => false],
            ['name' => 'resident.inquiries.view', 'display_name' => 'View Resident Inquiries', 'description' => 'View resident inquiries', 'category' => 'resident', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'resident.inquiries.respond', 'display_name' => 'Respond to Resident Inquiries', 'description' => 'Respond to resident inquiries', 'category' => 'resident', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'resident.messages.view', 'display_name' => 'View Resident Messages', 'description' => 'View messages from residents', 'category' => 'resident', 'requires_estate_assignment' => true, 'system_level_only' => false],
            ['name' => 'resident.messages.send', 'display_name' => 'Send Messages to Residents', 'description' => 'Send messages to residents', 'category' => 'resident', 'requires_estate_assignment' => true, 'system_level_only' => false],
        ];

        foreach ($permissions as $permission) {
            DB::table('permissions')->updateOrInsert(
                ['name' => $permission['name']],
                array_merge($permission, [
                    'allow_user_overrides' => true,
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ])
            );
        }
    }
}
