<?php

namespace Database\Seeders;

use App\Models\House;
use App\Models\Invoice;
use App\Models\InvoiceLineItem;
use App\Models\MeterReading;
use App\Models\WaterRate;
use Illuminate\Database\Seeder;

class InvoiceSeeder extends Seeder
{
    public function run(): void
    {
        $houses = House::all();

        foreach ($houses as $house) {
            $this->createInvoicesForHouse($house);
        }

        $this->command->info('Invoices seeded successfully!');
    }

    private function createInvoicesForHouse($house)
    {
        // Get approved readings for this house, ordered by date
        $readings = MeterReading::where('house_id', $house->id)
            ->where('status', 'approved')
            ->orderBy('reading_date')
            ->get();

        if ($readings->count() < 2) {
            return; // Need at least 2 readings to create an invoice
        }

        // Create invoices for the last 6 months
        for ($i = 6; $i >= 1; $i--) {
            $periodStart = now()->subMonths($i)->startOfMonth();
            $periodEnd = now()->subMonths($i)->endOfMonth();

            // Find reading for this period
            $reading = $readings->where('reading_date', '>=', $periodStart)
                ->where('reading_date', '<=', $periodEnd)
                ->first();

            if (! $reading) {
                continue;
            }

            // Get previous reading
            $previousReading = $readings->where('reading_date', '<', $periodStart)->last();

            if (! $previousReading) {
                continue;
            }

            // Get water rate for this estate
            $waterRate = $this->getApplicableWaterRate($house->estate_id, $reading->consumption);

            // Calculate invoice amounts using water rate calculation method
            $consumption = $reading->consumption;
            $amount = $waterRate->calculateAmount($consumption);

            // Create invoice
            $invoice = Invoice::create([
                'house_id' => $house->id,
                'water_rate_id' => $waterRate->id,
                'meter_reading_id' => $reading->id,
                'invoice_number' => $this->generateInvoiceNumber(),
                'billing_period_start' => $periodStart,
                'billing_period_end' => $periodEnd,
                'previous_reading' => $previousReading->current_reading,
                'current_reading' => $reading->current_reading,
                'consumption' => $consumption,
                'rate_per_unit' => $waterRate->rate_per_unit,
                'amount' => $amount,
                'fixed_charge' => $waterRate->fixed_charge,
                'total_amount' => $amount,
                'due_date' => $periodEnd->copy()->addDays(15),
                'status' => $this->getRandomInvoiceStatus(),
                'notes' => $this->generateRandomInvoiceNotes(),
            ]);

            // Create line item
            InvoiceLineItem::create([
                'invoice_id' => $invoice->id,
                'type' => 'water_consumption',
                'description' => "Water consumption for {$periodStart->format('M Y')}",
                'quantity' => $consumption,
                'rate' => $waterRate->rate_per_unit,
                'amount' => $amount - $waterRate->fixed_charge, // Variable amount only
                'tier' => 'standard',
            ]);

            // Add fixed charge as separate line item
            InvoiceLineItem::create([
                'invoice_id' => $invoice->id,
                'type' => 'fixed_charge',
                'description' => "Monthly fixed charge for {$periodStart->format('M Y')}",
                'quantity' => 1,
                'rate' => $waterRate->fixed_charge,
                'amount' => $waterRate->fixed_charge,
                'tier' => 'fixed',
            ]);

            // Add payment if status is paid
            if ($invoice->status === 'paid') {
                $paymentDate = $invoice->due_date->copy()->addDays(rand(-5, 10));
                $invoice->update([
                    'paid_at' => $paymentDate,
                    'payment_reference' => $this->generatePaymentReference(),
                ]);
            }
        }
    }

    private function getApplicableWaterRate($estateId, $consumption)
    {
        return WaterRate::where('estate_id', $estateId)
            ->where('is_active', true)
            ->where('effective_from', '<=', now())
            ->where(function ($query) {
                $query->whereNull('effective_to')
                    ->orWhere('effective_to', '>=', now());
            })
            ->first();
    }

    private function generateInvoiceNumber()
    {
        // Use a more robust approach with database check to ensure uniqueness
        $maxAttempts = 10;
        $attempt = 0;
        
        while ($attempt < $maxAttempts) {
            // Generate invoice number with date and sequence
            $date = now()->format('Ymd');
            $sequence = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $invoiceNumber = 'INV-'.$date.'-'.$sequence;
            
            // Check if invoice number already exists
            if (!Invoice::where('invoice_number', $invoiceNumber)->exists()) {
                return $invoiceNumber;
            }
            
            $attempt++;
        }
        
        // Fallback to UUID-based approach if random generation fails
        return 'INV-'.$date.'-'.substr(uniqid(), -6);
    }

    private function getRandomInvoiceStatus()
    {
        $statuses = ['draft', 'sent', 'paid', 'overdue'];
        $weights = [10, 30, 50, 10]; // Higher chance of being paid

        return $this->weightedRandomChoice($statuses, $weights);
    }

    private function weightedRandomChoice($items, $weights)
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);

        foreach ($items as $index => $item) {
            $random -= $weights[$index];
            if ($random <= 0) {
                return $item;
            }
        }

        return $items[array_rand($items)];
    }

    private function getTierName($consumption)
    {
        return 'standard';
    }

    private function generateRandomInvoiceNotes()
    {
        $notes = [
            'Regular monthly billing',
            'Meter reading taken on schedule',
            'No irregularities detected',
            'Customer account in good standing',
            'Billing as per consumption',
            'Standard water charges',
            null, // No notes
            null, // No notes
        ];

        return $notes[array_rand($notes)];
    }

    private function generatePaymentReference()
    {
        return 'PAY-'.date('Ym').'-'.str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
}
