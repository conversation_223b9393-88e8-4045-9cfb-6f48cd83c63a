<?php

namespace Database\Seeders;

use App\Models\House;
use App\Models\MeterReading;
use App\Models\User;
use Illuminate\Database\Seeder;

class MeterReadingSeeder extends Seeder
{
    public function run(): void
    {
        $houses = House::all();
        $users = User::where('role', 'caretaker')->get();

        if ($users->isEmpty()) {
            // Create a caretaker user if none exists
            $users = collect([User::factory()->create(['role' => 'caretaker'])]);
        }

        $caretaker = $users->first();

        foreach ($houses as $house) {
            $this->createReadingsForHouse($house, $caretaker);
        }

        $this->command->info('Meter readings seeded successfully!');
    }

    private function createReadingsForHouse($house, $caretaker)
    {
        // Create readings for the last 12 months
        $currentReading = rand(1000, 5000); // Starting meter reading

        for ($i = 12; $i >= 0; $i--) {
            $readingDate = now()->subMonths($i);

            // Skip some months randomly to make it more realistic
            if (rand(0, 10) > 7) {
                continue;
            }

            // Generate realistic consumption
            $consumption = rand(8, 45);
            $newReading = $currentReading + $consumption;

            // Create reading with appropriate status
            $status = 'approved';
            $reviewedAt = $readingDate->copy()->addDays(rand(1, 5));

            MeterReading::create([
                'house_id' => $house->id,
                'user_id' => $caretaker->id,
                'reading_date' => $readingDate,
                'previous_reading' => $currentReading,
                'current_reading' => $newReading,
                'consumption' => $consumption,
                'status' => $status,
                'reviewed_at' => $reviewedAt,
                'reviewed_by' => $caretaker->id,
                'review_notes' => $this->generateRandomNotes(),
                'latitude' => rand(-90, 90) + (rand(0, 999999) / 1000000),
                'longitude' => rand(-180, 180) + (rand(0, 999999) / 1000000),
                'accuracy' => rand(1, 10) + (rand(0, 99) / 100),
            ]);

            $currentReading = $newReading;
        }
    }

    private function generateRandomNotes()
    {
        $notes = [
            'Regular monthly reading',
            'Meter accessed without issues',
            'Customer present during reading',
            'Meter functioning normally',
            'Reading taken as scheduled',
            'No irregularities observed',
            'Meter seal intact',
            'Clear access to meter',
        ];

        return rand(0, 10) > 3 ? $notes[array_rand($notes)] : null;
    }
}
