<?php

namespace Database\Factories;

use App\Models\Estate;
use Illuminate\Database\Eloquent\Factories\Factory;

class EstateFactory extends Factory
{
    protected $model = Estate::class;

    public function definition(): array
    {
        return [
            'code' => fake()->unique()->bothify('???###'),
            'name' => fake()->company().' Estate',
            'address' => fake()->address(),
            'city' => fake()->city(),
            'state' => fake()->state(),
            'postal_code' => fake()->postcode(),
            'country' => fake()->country(),
            'settings' => [
                'billing_day' => 1,
                'due_days' => 15,
                'late_fee_percentage' => 5,
                'water_source' => 'municipal',
                'meter_reading_frequency' => 'monthly',
            ],
        ];
    }
}
