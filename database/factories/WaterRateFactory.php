<?php

namespace Database\Factories;

use App\Models\Estate;
use App\Models\WaterRate;
use Illuminate\Database\Eloquent\Factories\Factory;

class WaterRateFactory extends Factory
{
    protected $model = WaterRate::class;

    public function definition(): array
    {
        return [
            'estate_id' => Estate::factory(),
            'name' => fake()->unique()->word().' Rate',
            'description' => fake()->sentence(),
            'rate_per_unit' => fake()->randomFloat(4, 0.0010, 0.0500),
            'minimum_charge' => fake()->randomFloat(2, 100, 500),
            'minimum_units' => fake()->numberBetween(0, 10),
            'fixed_charge' => fake()->randomFloat(2, 50, 200),
            'effective_from' => fake()->dateTimeBetween('-1 month', '+1 month'),
            'effective_to' => fake()->optional()->dateTimeBetween('+6 months', '+2 years'),
            'is_active' => true,
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'effective_from' => now()->subDay(),
            'effective_to' => null,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
