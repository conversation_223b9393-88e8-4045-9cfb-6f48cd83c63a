<?php

namespace Database\Factories;

use App\Models\House;
use App\Models\MeterReading;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class MeterReadingFactory extends Factory
{
    protected $model = MeterReading::class;

    public function definition(): array
    {
        $previousReading = fake()->numberBetween(1000, 5000);
        $currentReading = $previousReading + fake()->numberBetween(5, 50);
        $consumption = $currentReading - $previousReading;

        return [
            'house_id' => House::factory(),
            'user_id' => User::factory(),
            'reading_date' => fake()->dateTimeBetween('-3 months', 'now'),
            'previous_reading' => $previousReading,
            'current_reading' => $currentReading,
            'consumption' => $consumption,
            'photo_path' => fake()->optional()->imageUrl(),
            'notes' => fake()->optional()->sentence(),
            'status' => 'draft',
            'latitude' => fake()->latitude(),
            'longitude' => fake()->longitude(),
            'accuracy' => fake()->randomFloat(2, 1, 10),
        ];
    }

    public function submitted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'submitted',
            'submitted_at' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    public function reviewed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'reviewed',
            'submitted_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'reviewed_at' => fake()->dateTimeBetween('-3 days', 'now'),
            'reviewed_by' => User::factory(),
        ]);
    }

    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'submitted_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'reviewed_at' => fake()->dateTimeBetween('-3 days', 'now'),
            'reviewed_by' => User::factory(),
            'approved_at' => fake()->dateTimeBetween('-1 day', 'now'),
            'approved_by' => User::factory(),
        ]);
    }

    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'submitted_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'reviewed_at' => fake()->dateTimeBetween('-3 days', 'now'),
            'reviewed_by' => User::factory(),
            'rejection_reason' => fake()->sentence(),
        ]);
    }
}
