<?php

namespace Database\Factories;

use App\Models\Estate;
use App\Models\House;
use Illuminate\Database\Eloquent\Factories\Factory;

class HouseFactory extends Factory
{
    protected $model = House::class;

    public function definition(): array
    {
        return [
            'estate_id' => Estate::factory(), // This will be overridden when used with hasMany
            'house_number' => fake()->unique()->buildingNumber(),
            'meter_number' => fake()->unique()->numerify('WM-####-####'),
            'initial_reading' => fake()->numberBetween(0, 1000),
            'is_active' => true,
            'notes' => fake()->optional()->sentence(),
        ];
    }
}
