<?php

namespace Database\Factories;

use App\Models\House;
use App\Models\HouseAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\HouseAccount>
 */
class HouseAccountFactory extends Factory
{
    protected $model = HouseAccount::class;

    public function definition(): array
    {
        return [
            'house_id' => House::factory(),
            'current_balance' => $this->faker->randomFloat(2, 0, 10000),
            'total_credit' => $this->faker->randomFloat(2, 0, 50000),
            'total_debit' => $this->faker->randomFloat(2, 0, 50000),
            'last_transaction_date' => null,
        ];
    }

    public function withZeroBalance(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_balance' => 0,
            'total_credit' => 0,
            'total_debit' => 0,
            'last_transaction_date' => null,
        ]);
    }

    public function withBalance(float $balance): static
    {
        return $this->state(fn (array $attributes) => [
            'current_balance' => $balance,
            'total_credit' => $balance > 0 ? $balance : 0,
            'total_debit' => $balance < 0 ? abs($balance) : 0,
        ]);
    }
}
