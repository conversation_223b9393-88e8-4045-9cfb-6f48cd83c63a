<?php

namespace Database\Factories;

use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\WaterRate;
use Illuminate\Database\Eloquent\Factories\Factory;

class InvoiceFactory extends Factory
{
    protected $model = Invoice::class;

    public function definition(): array
    {
        $previousReading = fake()->numberBetween(1000, 5000);
        $currentReading = $previousReading + fake()->numberBetween(5, 100);
        $consumption = $currentReading - $previousReading;
        $ratePerUnit = fake()->randomFloat(4, 15, 40);
        $amount = $consumption * $ratePerUnit;
        $fixedCharge = fake()->randomFloat(2, 100, 300);
        $totalAmount = $amount + $fixedCharge;

        return [
            'house_id' => House::factory(),
            'water_rate_id' => WaterRate::factory(),
            'meter_reading_id' => MeterReading::factory(),
            'invoice_number' => fake()->unique()->numerify('INV-####-######'),
            'billing_period_start' => fake()->dateTimeBetween('-2 months', '-1 month'),
            'billing_period_end' => fake()->dateTimeBetween('-1 month', 'now'),
            'previous_reading' => $previousReading,
            'current_reading' => $currentReading,
            'consumption' => $consumption,
            'rate_per_unit' => $ratePerUnit,
            'amount' => $amount,
            'fixed_charge' => $fixedCharge,
            'total_amount' => $totalAmount,
            'due_date' => fake()->dateTimeBetween('+7 days', '+15 days'),
            'status' => 'draft',
            'notes' => fake()->optional()->sentence(),
            'pdf_path' => null,
        ];
    }

    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paid',
            'paid_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'payment_reference' => fake()->numerify('PAY-####-######'),
        ]);
    }

    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'overdue',
            'due_date' => fake()->dateTimeBetween('-2 weeks', '-1 day'),
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'cancelled_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'cancellation_reason' => fake()->sentence(),
        ]);
    }

    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'sent_at' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }
}
