<?php

namespace Database\Factories;

use App\Models\Contact;
use Illuminate\Database\Eloquent\Factories\Factory;

class ContactFactory extends Factory
{
    protected $model = Contact::class;

    public function definition(): array
    {
        return [
            'user_id' => null, // Will be overridden when creating resident contacts
            'house_id' => 1, // Will be overridden by seeder
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'phone' => fake()->phoneNumber(),
            'whatsapp_number' => fake()->phoneNumber(),
            'type' => fake()->randomElement(['owner', 'tenant', 'caretaker']),
            'is_primary' => true,
            'is_active' => true,
            'receive_invoices' => true,
            'receive_notifications' => true,
        ];
    }

    public function secondary(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_primary' => false,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function noNotifications(): static
    {
        return $this->state(fn (array $attributes) => [
            'receive_notifications' => false,
        ]);
    }

    public function noInvoices(): static
    {
        return $this->state(fn (array $attributes) => [
            'receive_invoices' => false,
        ]);
    }
}
