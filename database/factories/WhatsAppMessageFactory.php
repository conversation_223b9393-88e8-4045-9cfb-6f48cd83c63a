<?php

namespace Database\Factories;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WhatsAppMessage;
use Illuminate\Database\Eloquent\Factories\Factory;

class WhatsAppMessageFactory extends Factory
{
    protected $model = WhatsAppMessage::class;

    public function definition(): array
    {
        return [
            'sender_id' => User::factory(),
            'recipient_contact_id' => Contact::factory(),
            'house_id' => House::factory(),
            'estate_id' => Estate::factory(),
            'recipient' => fake()->phoneNumber(),
            'message_type' => fake()->randomElement(['template', 'text', 'interactive', 'media']),
            'content' => fake()->sentence(),
            'status' => fake()->randomElement(['pending', 'sent', 'delivered', 'read', 'failed']),
            'direction' => fake()->randomElement(['incoming', 'outgoing']),
            'template_name' => fake()->randomElement([
                'invoice_delivery',
                'payment_reminder',
                'payment_confirmation',
                'overdue_notice',
                'welcome_message',
            ]),
            'parameters' => [
                'customer_name' => fake()->name(),
                'invoice_number' => fake()->numerify('INV-####-######'),
                'amount' => fake()->randomFloat(2, 100, 1000),
                'due_date' => fake()->date(),
                'house_number' => fake()->buildingNumber(),
            ],
            'interactive_data' => fake()->optional()->randomElement([
                null,
                [
                    'type' => 'button',
                    'buttons' => [
                        ['type' => 'reply', 'reply' => ['id' => 'pay_now', 'title' => 'Pay Now']],
                        ['type' => 'reply', 'reply' => ['id' => 'view_invoice', 'title' => 'View Invoice']],
                    ],
                ],
            ]),
            'media_url' => fake()->optional()->url(),
            'conversation_id' => fake()->optional()->uuid(),
            'retry_count' => fake()->numberBetween(0, 3),
            'sent_at' => fake()->optional()->dateTimeBetween('-1 week', 'now'),
            'delivered_at' => fake()->optional()->dateTimeBetween('-1 week', 'now'),
            'read_at' => fake()->optional()->dateTimeBetween('-1 week', 'now'),
            'failed_reason' => fake()->optional()->sentence(),
            'response_data' => fake()->optional()->randomElement([null, ['message_id' => fake()->uuid()]]),
        ];
    }

    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'sent_at' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'delivered',
            'sent_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'delivered_at' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    public function read(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'read',
            'sent_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'delivered_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'read_at' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'failed_reason' => fake()->sentence(),
        ]);
    }

    public function forInvoice(Invoice $invoice): static
    {
        return $this->state(function (array $attributes) use ($invoice) {
            return [
                'messageable_type' => Invoice::class,
                'messageable_id' => $invoice->id,
                'house_id' => $invoice->house_id,
                'estate_id' => $invoice->house->estate_id,
            ];
        });
    }

    public function forHouse(House $house): static
    {
        return $this->state(function (array $attributes) use ($house) {
            return [
                'house_id' => $house->id,
                'estate_id' => $house->estate_id,
            ];
        });
    }

    public function forEstate(Estate $estate): static
    {
        return $this->state(function (array $attributes) use ($estate) {
            return [
                'estate_id' => $estate->id,
            ];
        });
    }

    public function withMedia(): static
    {
        return $this->state(fn (array $attributes) => [
            'message_type' => 'media',
            'media_url' => fake()->url(),
            'content' => 'Invoice PDF attached',
        ]);
    }

    public function incoming(): static
    {
        return $this->state(fn (array $attributes) => [
            'direction' => 'incoming',
            'message_type' => 'text',
            'template_name' => null,
            'status' => fake()->randomElement(['delivered', 'read']),
        ]);
    }

    public function outgoing(): static
    {
        return $this->state(fn (array $attributes) => [
            'direction' => 'outgoing',
            'message_type' => fake()->randomElement(['template', 'text']),
            'template_name' => fake()->randomElement([
                'invoice_delivery',
                'payment_reminder',
                'payment_confirmation',
                'overdue_notice',
                'welcome_message',
            ]),
        ]);
    }

    public function interactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'message_type' => 'interactive',
            'interactive_data' => [
                'type' => 'button',
                'body' => ['text' => 'Please select an option:'],
                'action' => [
                    'buttons' => [
                        ['type' => 'reply', 'reply' => ['id' => 'pay_now', 'title' => 'Pay Now']],
                        ['type' => 'reply', 'reply' => ['id' => 'view_invoice', 'title' => 'View Invoice']],
                        ['type' => 'reply', 'reply' => ['id' => 'contact_support', 'title' => 'Contact Support']],
                    ],
                ],
            ],
        ]);
    }
}
