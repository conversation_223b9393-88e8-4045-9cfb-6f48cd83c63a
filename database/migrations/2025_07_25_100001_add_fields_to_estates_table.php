<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('estates', function (Blueprint $table) {
            $table->string('code')->unique()->after('name');
            $table->string('manager_name')->nullable()->after('description');
            $table->string('manager_phone')->nullable()->after('manager_name');
            $table->string('manager_email')->nullable()->after('manager_phone');
            $table->integer('total_houses')->default(0)->after('is_active');
            $table->integer('occupied_houses')->default(0)->after('total_houses');
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        // For SQLite, we need to drop the unique index first
        if (DB::connection()->getDriverName() === 'sqlite') {
            Schema::table('estates', function (Blueprint $table) {
                $table->dropUnique('estates_code_unique');
            });
        }

        Schema::table('estates', function (Blueprint $table) {
            $table->dropColumn(['code', 'manager_name', 'manager_phone', 'manager_email', 'total_houses', 'occupied_houses']);
            $table->dropSoftDeletes();
        });
    }
};
