<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_line_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->string('type', 30); // consumption, base_charge, tax, late_fee, adjustment, previous_balance
            $table->string('description');
            $table->decimal('quantity', 10, 2)->nullable();
            $table->decimal('rate', 10, 4)->nullable();
            $table->decimal('amount', 10, 2);
            $table->integer('tier')->nullable(); // For tiered pricing
            $table->json('metadata')->nullable(); // Additional data like tier details
            $table->timestamps();

            $table->index(['invoice_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_line_items');
    }
};
