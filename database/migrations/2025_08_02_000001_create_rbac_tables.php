<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Drop existing tables that conflict with our new RBAC structure
        Schema::dropIfExists('user_roles');
        Schema::dropIfExists('user_estate_assignments');

        // Create the new RBAC tables
        Schema::create('user_roles', function (Blueprint $table) {
            $table->string('value')->primary();
            $table->string('label')->notNullable();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->string('category');
            $table->boolean('requires_estate_assignment')->default(true);
            $table->boolean('system_level_only')->default(false);
            $table->boolean('allow_user_overrides')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index('name');
        });

        Schema::create('role_permissions', function (Blueprint $table) {
            $table->id();
            $table->string('role');
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->foreignId('assigned_by')->constrained('users');
            $table->timestamp('assigned_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamps();

            $table->unique(['role', 'permission_id']);
            $table->foreign('role')->references('value')->on('user_roles');
        });

        Schema::create('user_permission_overrides', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->enum('action', ['grant', 'revoke'])->default('grant');
            $table->foreignId('granted_by')->constrained('users');
            $table->timestamp('expires_at')->nullable();
            $table->text('reason')->nullable();
            $table->timestamps();

            $table->unique(['user_id', 'permission_id']);
            $table->index(['user_id', 'expires_at']);
            $table->index('permission_id');
        });

        Schema::create('permission_audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('action');
            $table->json('details');
            $table->string('target_type');
            $table->string('target_id');
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'created_at']);
            $table->index(['target_type', 'target_id']);
            $table->index('action');
        });

        Schema::create('user_estate_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->foreignId('assigned_by')->constrained('users');
            $table->timestamp('assigned_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamps();

            $table->unique(['user_id', 'estate_id']);
            $table->index(['user_id', 'estate_id']);
            $table->index('estate_id');
        });

        Schema::create('user_management_hierarchy', function (Blueprint $table) {
            $table->id();
            $table->foreignId('manager_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('subordinate_id')->constrained('users')->onDelete('cascade');
            $table->enum('relationship', ['manages', 'oversees']);
            $table->timestamps();

            $table->unique(['manager_id', 'subordinate_id']);
            $table->index(['manager_id', 'relationship']);
            $table->index('subordinate_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_management_hierarchy');
        Schema::dropIfExists('user_estate_assignments');
        Schema::dropIfExists('permission_audit_logs');
        Schema::dropIfExists('user_permission_overrides');
        Schema::dropIfExists('role_permissions');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('user_roles');
    }
};
