<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all permissions
        $allPermissions = Permission::all()->pluck('id')->toArray();

        // Admin role - gets all permissions
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->syncPermissions($allPermissions);
        }

        // Manager role permissions
        $managerRole = Role::where('name', 'manager')->first();
        if ($managerRole) {
            $managerPermissions = [
                'view-manager-dashboard', 'view-reviewer-dashboard', 'view-caretaker-dashboard', 'view-resident-dashboard',
                'estates.view_assigned', 'estates.manage_assigned', 'estates.edit_assigned', 'estates.analytics',
                'houses.view_assigned', 'houses.manage_assigned', 'houses.edit_assigned',
                'contacts.view_assigned', 'contacts.manage_assigned',
                'readings.view_assigned', 'readings.review_assigned', 'readings.validate',
                'invoices.view_assigned', 'invoices.adjust_assigned', 'invoices.export_assigned',
                'invoices.approve_assigned', 'invoices.send_assigned', 'invoices.generate_assigned',
                'accounts.view_assigned', 'accounts.view_balance_assigned', 'accounts.view_transactions_assigned',
                'accounts.view_statement_assigned', 'accounts.export_statement_assigned',
                'rates.view_assigned',
                'reports.view_assigned', 'analytics.view_assigned', 'export.data_assigned',
                'reports.generate_assigned', 'reports.aging_assigned', 'reports.revenue_assigned',
                'reports.billing_assigned', 'reports.customer_statements_assigned',
                'payments.view_assigned', 'payments.approve_assigned', 'payments.export_assigned',
                'users.view_assigned', 'users.create_assigned', 'users.edit_assigned', 'users.assign_estates',
                'whatsapp.send_assigned', 'whatsapp.logs.view',
                'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
                'resident.messages.view', 'resident.messages.send',
            ];
            
            $managerPermissionIds = Permission::whereIn('name', $managerPermissions)->pluck('id')->toArray();
            $managerRole->syncPermissions($managerPermissionIds);
        }

        // Reviewer role permissions
        $reviewerRole = Role::where('name', 'reviewer')->first();
        if ($reviewerRole) {
            $reviewerPermissions = [
                'view-reviewer-dashboard', 'view-resident-dashboard',
                'estates.view_assigned',
                'houses.view_assigned', 'houses.edit_assigned',
                'contacts.view_assigned', 'contacts.edit_assigned',
                'readings.view_assigned', 'readings.approve_assigned', 'readings.validate',
                'readings.edit_assigned', 'readings.create_assigned',
                'invoices.view_assigned', 'invoices.generate_assigned', 'invoices.edit_assigned',
                'invoices.send_assigned', 'invoices.export_assigned', 'invoices.approve_assigned',
                'invoices.adjust_assigned', 'invoices.create_manual', 'invoices.delete_assigned',
                'accounts.view_assigned', 'accounts.manage_assigned', 'accounts.view_balance_assigned',
                'accounts.view_transactions_assigned', 'accounts.create_transaction_assigned',
                'accounts.edit_transaction_assigned', 'accounts.view_statement_assigned',
                'accounts.export_statement_assigned', 'accounts.adjust_balance_assigned',
                'rates.view_assigned', 'rates.edit_assigned',
                'reports.view_assigned', 'export.data_assigned', 'reports.generate_assigned',
                'reports.aging_assigned', 'reports.revenue_assigned', 'reports.billing_assigned',
                'reports.customer_statements_assigned', 'reports.financial_assigned',
                'payments.view_assigned', 'payments.approve_assigned', 'payments.create_assigned',
                'payments.edit_assigned', 'payments.export_assigned', 'payments.reconcile_assigned',
                'whatsapp.send_assigned', 'whatsapp.logs.view', 'whatsapp.send_invoices_assigned',
                'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
                'resident.messages.view', 'resident.messages.send',
            ];
            
            $reviewerPermissionIds = Permission::whereIn('name', $reviewerPermissions)->pluck('id')->toArray();
            $reviewerRole->syncPermissions($reviewerPermissionIds);
        }

        // Caretaker role permissions
        $caretakerRole = Role::where('name', 'caretaker')->first();
        if ($caretakerRole) {
            $caretakerPermissions = [
                'view-caretaker-dashboard',
                'estates.view_assigned',
                'houses.view_assigned', 'houses.edit_assigned',
                'contacts.view_assigned', 'contacts.manage_assigned', 'contacts.create_assigned',
                'contacts.edit_assigned',
                'readings.view_assigned', 'readings.create_assigned', 'readings.edit_assigned', 'readings.validate',
                'accounts.view_balance_assigned', 'accounts.view_balance_list_assigned',
                'invoices.view_assigned', 'invoices.view_status_assigned',
                'reports.view_assigned', 'reports.balance_list_assigned',
                'resident.portal.access', 'resident.inquiries.view_assigned',
            ];
            
            $caretakerPermissionIds = Permission::whereIn('name', $caretakerPermissions)->pluck('id')->toArray();
            $caretakerRole->syncPermissions($caretakerPermissionIds);
        }

        // Resident role permissions
        $residentRole = Role::where('name', 'resident')->first();
        if ($residentRole) {
            $residentPermissions = [
                'view-resident-dashboard',
                'houses.view_own', 'contacts.view_own', 'readings.view_own', 'invoices.view_own',
                'accounts.view_own', 'accounts.view_balance_own', 'accounts.view_transactions_own',
                'accounts.view_statement_own', 'accounts.export_statement_own',
                'invoices.view_own', 'invoices.download_own', 'invoices.view_payments_own',
                'invoices.view_adjustments_own',
                'reports.view_own', 'analytics.view_own', 'export.data_own',
                'payments.view_own', 'payments.view_history_own',
                'resident.portal.access', 'resident.inquiries.create', 'resident.messages.view',
                'resident.payments.create', 'resident.invoices.download',
            ];
            
            $residentPermissionIds = Permission::whereIn('name', $residentPermissions)->pluck('id')->toArray();
            $residentRole->syncPermissions($residentPermissionIds);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove all role-permission assignments
        DB::table('spatie_role_has_permissions')->delete();
    }
};