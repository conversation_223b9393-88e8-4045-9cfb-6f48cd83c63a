<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_roles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->enum('role', ['admin', 'manager', 'reviewer', 'caretaker', 'resident']);
            $table->boolean('is_active')->default(true);
            $table->json('permissions')->nullable(); // Additional granular permissions
            $table->timestamps();

            $table->unique(['user_id', 'estate_id', 'role']);
            $table->index(['estate_id', 'role']);
        });

        // Add role column to users table
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['admin', 'manager', 'reviewer', 'caretaker', 'resident'])->default('admin');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('role');
        });

        Schema::dropIfExists('user_roles');
    }
};
