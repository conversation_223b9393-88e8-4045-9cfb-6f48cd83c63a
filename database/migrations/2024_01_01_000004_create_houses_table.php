<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('houses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->string('house_number')->unique();
            $table->string('block')->nullable();
            $table->string('floor')->nullable();
            $table->string('type')->default('residential'); // residential, commercial
            $table->string('meter_number')->unique();
            $table->decimal('initial_reading', 10, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['estate_id', 'house_number']);
            $table->index('meter_number');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('houses');
    }
};
