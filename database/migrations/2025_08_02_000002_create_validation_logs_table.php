<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('validation_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('meter_reading_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('rule_type');
            $table->string('severity');
            $table->string('message');
            $table->json('details')->nullable();
            $table->timestamps();

            $table->index(['meter_reading_id', 'rule_type']);
            $table->index(['severity']);
            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('validation_logs');
    }
};
