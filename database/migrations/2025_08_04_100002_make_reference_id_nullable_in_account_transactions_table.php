<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to drop and recreate the table
        Schema::dropIfExists('account_transactions_new');

        Schema::create('account_transactions_new', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_account_id')->constrained()->onDelete('cascade');
            $table->string('transaction_type'); // invoice, payment, adjustment, credit_note
            $table->string('reference_type'); // Invoice, Payment, Adjustment
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->decimal('amount', 10, 2);
            $table->decimal('balance_before', 10, 2);
            $table->decimal('balance_after', 10, 2);
            $table->text('description')->nullable();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();

            $table->index(['house_account_id', 'created_at']);
            $table->index('transaction_type');
            $table->index('reference_type');
        });

        // Copy data from old table
        DB::statement('INSERT INTO account_transactions_new SELECT * FROM account_transactions');

        // Drop old table and rename new one
        Schema::dropIfExists('account_transactions');
        DB::statement('ALTER TABLE account_transactions_new RENAME TO account_transactions');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the original table structure
        Schema::dropIfExists('account_transactions_new');

        Schema::create('account_transactions_new', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_account_id')->constrained()->onDelete('cascade');
            $table->string('transaction_type'); // invoice, payment, adjustment, credit_note
            $table->string('reference_type'); // Invoice, Payment, Adjustment
            $table->unsignedBigInteger('reference_id');
            $table->decimal('amount', 10, 2);
            $table->decimal('balance_before', 10, 2);
            $table->decimal('balance_after', 10, 2);
            $table->text('description')->nullable();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();

            $table->index(['house_account_id', 'created_at']);
            $table->index('transaction_type');
            $table->index('reference_type');
        });

        // Copy data from old table
        DB::statement('INSERT INTO account_transactions_new SELECT * FROM account_transactions');

        // Drop old table and rename new one
        Schema::dropIfExists('account_transactions');
        DB::statement('ALTER TABLE account_transactions_new RENAME TO account_transactions');
    }
};
