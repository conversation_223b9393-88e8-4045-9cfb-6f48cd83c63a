<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            $table->boolean('is_active')->default(true)->after('receive_notifications');
            $table->index(['is_active', 'receive_notifications']);
            $table->index(['is_active', 'receive_invoices']);
        });
    }

    public function down(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            $table->dropIndex(['is_active', 'receive_notifications']);
            $table->dropIndex(['is_active', 'receive_invoices']);
            $table->dropColumn('is_active');
        });
    }
};
