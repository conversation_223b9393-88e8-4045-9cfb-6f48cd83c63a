<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('meter_readings', function (Blueprint $table) {
            $table->json('validation_results')->nullable()->after('review_notes');
            $table->string('validation_status')->default('pending')->after('validation_results');
            $table->integer('confidence_score')->default(0)->after('validation_status');
            $table->string('risk_level')->default('low')->after('confidence_score');
            $table->timestamp('validated_at')->nullable()->after('validated_at');
            $table->foreignId('validated_by')->nullable()->constrained('users')->onDelete('set null');

            $table->index(['validation_status', 'risk_level']);
            $table->index(['confidence_score']);
        });
    }

    public function down(): void
    {
        Schema::table('meter_readings', function (Blueprint $table) {
            $table->dropIndex(['validation_status', 'risk_level']);
            $table->dropIndex(['confidence_score']);
            $table->dropColumn(['validation_results', 'validation_status', 'confidence_score', 'risk_level', 'validated_at', 'validated_by']);
        });
    }
};
