<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('houses', function (Blueprint $table) {
            $table->integer('bedrooms')->nullable()->after('type');
            $table->decimal('size_sqft', 8, 2)->nullable()->after('bedrooms');
            $table->decimal('monthly_rent', 10, 2)->nullable()->after('size_sqft');
            $table->date('occupancy_date')->nullable()->after('is_active');
            $table->string('gps_coordinates')->nullable()->after('notes');
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::table('houses', function (Blueprint $table) {
            $table->dropColumn(['bedrooms', 'size_sqft', 'monthly_rent', 'occupancy_date', 'gps_coordinates']);
            $table->dropSoftDeletes();
        });
    }
};
