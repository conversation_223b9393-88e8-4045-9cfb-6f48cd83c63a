<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Comprehensive permissions structure based on UserRole enum analysis
        $permissions = [
            // Dashboard permissions
            ['name' => 'view-admin-dashboard', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'view-manager-dashboard', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'view-reviewer-dashboard', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'view-caretaker-dashboard', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'view-resident-dashboard', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // Estate permissions
            ['name' => 'estates.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'estates.manage_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'estates.create', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'estates.edit_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'estates.delete', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'estates.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'estates.manage_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'estates.edit_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'estates.analytics', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // House permissions
            ['name' => 'houses.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'houses.manage_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'houses.create', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'houses.edit_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'houses.delete', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'houses.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'houses.manage_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'houses.edit_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'houses.view_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // Contact permissions
            ['name' => 'contacts.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'contacts.manage_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'contacts.create', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'contacts.delete', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'contacts.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'contacts.manage_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'contacts.edit_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'contacts.create_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'contacts.view_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // Reading permissions
            ['name' => 'readings.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.create_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.edit_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.delete', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.review_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.approve_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.validate', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.review_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.approve_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.edit_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.create_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'readings.view_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // Invoice permissions
            ['name' => 'invoices.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.generate_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.create_manual', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.edit_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.delete', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.send_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.adjust_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.export_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.approve_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.adjust_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.export_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.approve_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.send_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.generate_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.view_status_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.view_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.download_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.view_payments_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'invoices.view_adjustments_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // Account permissions
            ['name' => 'accounts.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.manage_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_balance_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_transactions_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_statement_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.export_statement_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_balance_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_transactions_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_statement_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.export_statement_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.manage_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.create_transaction_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.edit_transaction_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.adjust_balance_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_balance_list_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_balance_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_transactions_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.view_statement_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'accounts.export_statement_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // Payment permissions
            ['name' => 'payments.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.approve_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.export_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.approve_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.export_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.create_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.edit_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.reconcile_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.view_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'payments.view_history_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // Rate permissions
            ['name' => 'rates.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'rates.manage_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'rates.create', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'rates.edit_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'rates.delete', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'rates.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'rates.edit_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // Report permissions
            ['name' => 'reports.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.generate_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'analytics.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'export.data_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.aging_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.revenue_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.billing_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.customer_statements_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.financial_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'analytics.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'export.data_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.generate_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.aging_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.revenue_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.billing_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.customer_statements_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.financial_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.balance_list_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports.view_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'analytics.view_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'export.data_own', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // User permissions
            ['name' => 'users.view_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'users.create_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'users.edit_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'users.delete_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'users.assign_estates', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'users.assign_roles', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'users.manage_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'users.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'users.create_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'users.edit_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // System permissions
            ['name' => 'system.settings.view', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'system.settings.manage', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'audit.logs.view', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'audit.logs.export', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'whatsapp.settings', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'whatsapp.send_all', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'whatsapp.logs.view', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'whatsapp.send_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'whatsapp.send_invoices_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],

            // Resident permissions
            ['name' => 'resident.portal.access', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'resident.inquiries.view', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'resident.inquiries.respond', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'resident.messages.view', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'resident.messages.send', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'resident.inquiries.create', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'resident.inquiries.view_assigned', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'resident.payments.create', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'resident.invoices.download', 'guard_name' => 'web', 'created_at' => now(), 'updated_at' => now()],
        ];

        DB::table('spatie_permissions')->insert($permissions);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('spatie_permissions')->delete();
    }
};