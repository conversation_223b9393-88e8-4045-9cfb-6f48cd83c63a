<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('validation_baselines', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->foreignId('house_id')->constrained()->onDelete('cascade');
            $table->string('baseline_type');
            $table->decimal('average_consumption', 10, 2)->default(0);
            $table->decimal('median_consumption', 10, 2)->default(0);
            $table->decimal('std_deviation', 10, 2)->default(0);
            $table->decimal('q1_consumption', 10, 2)->default(0);
            $table->decimal('q3_consumption', 10, 2)->default(0);
            $table->integer('sample_size')->default(0);
            $table->date('baseline_date');
            $table->json('monthly_averages')->nullable();
            $table->timestamps();

            $table->unique(['estate_id', 'house_id', 'baseline_type', 'baseline_date']);
            $table->index(['estate_id', 'baseline_type']);
            $table->index(['house_id', 'baseline_type']);
            $table->index(['baseline_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('validation_baselines');
    }
};
