<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            $table->string('first_name')->nullable()->after('name');
            $table->string('last_name')->nullable()->after('first_name');
            $table->string('middle_name')->nullable()->after('last_name');
            $table->string('id_number')->nullable()->after('whatsapp_number');
            $table->date('date_of_birth')->nullable()->after('id_number');
            $table->string('occupation')->nullable()->after('date_of_birth');
            $table->string('company')->nullable()->after('occupation');
            $table->string('postal_address')->nullable()->after('company');
            $table->string('emergency_contact_name')->nullable()->after('postal_address');
            $table->string('emergency_contact_phone')->nullable()->after('emergency_contact_name');
            $table->softDeletes();

            // Update existing name field to be nullable since we have first/last name
            $table->string('name')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            $table->dropColumn([
                'first_name', 'last_name', 'middle_name', 'id_number',
                'date_of_birth', 'occupation', 'company', 'postal_address',
                'emergency_contact_name', 'emergency_contact_phone',
            ]);
            $table->dropSoftDeletes();
            $table->string('name')->nullable(false)->change();
        });
    }
};
