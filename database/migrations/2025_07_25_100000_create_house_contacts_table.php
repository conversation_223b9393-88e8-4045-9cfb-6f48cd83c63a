<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('house_contacts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_id')->constrained()->onDelete('cascade');
            $table->foreignId('contact_id')->constrained()->onDelete('cascade');
            $table->enum('relationship_type', ['owner', 'tenant', 'caretaker', 'emergency', 'family', 'other'])->default('other');
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['house_id', 'contact_id', 'relationship_type']);
            $table->index(['house_id', 'is_active']);
            $table->index(['contact_id', 'is_active']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('house_contacts');
    }
};
