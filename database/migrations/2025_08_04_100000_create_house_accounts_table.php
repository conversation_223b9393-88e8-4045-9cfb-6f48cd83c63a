<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('house_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_id')->unique()->constrained()->onDelete('cascade');
            $table->decimal('current_balance', 10, 2)->default(0);
            $table->decimal('total_credit', 10, 2)->default(0);
            $table->decimal('total_debit', 10, 2)->default(0);
            $table->timestamp('last_transaction_date')->nullable();
            $table->timestamps();

            $table->index('house_id');
            $table->index('current_balance');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('house_accounts');
    }
};
