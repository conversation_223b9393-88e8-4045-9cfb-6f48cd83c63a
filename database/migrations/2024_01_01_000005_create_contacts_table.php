<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('whatsapp_number')->nullable();
            $table->enum('type', ['owner', 'tenant', 'caretaker', 'emergency'])->default('owner');
            $table->boolean('is_primary')->default(false);
            $table->boolean('receive_invoices')->default(true);
            $table->boolean('receive_notifications')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['house_id', 'type']);
            $table->index('phone');
            $table->index('whatsapp_number');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
