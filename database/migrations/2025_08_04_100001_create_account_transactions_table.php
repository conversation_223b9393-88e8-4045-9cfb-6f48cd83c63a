<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('account_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_account_id')->constrained()->onDelete('cascade');
            $table->string('transaction_type'); // invoice, payment, adjustment, credit_note
            $table->string('reference_type'); // Invoice, Payment, Adjustment
            $table->unsignedBigInteger('reference_id'); // ID of referenced model
            $table->decimal('amount', 10, 2);
            $table->decimal('balance_before', 10, 2);
            $table->decimal('balance_after', 10, 2);
            $table->text('description')->nullable();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();

            $table->index(['house_account_id', 'created_at']);
            $table->index('transaction_type');
            $table->index('reference_type');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('account_transactions');
    }
};
