<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->decimal('previous_balance', 10, 2)->default(0)->after('total_amount');
            $table->decimal('late_fee', 10, 2)->default(0)->after('previous_balance');
            $table->decimal('tax_amount', 10, 2)->default(0)->after('late_fee');
            $table->decimal('total_due', 10, 2)->default(0)->after('tax_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn([
                'previous_balance',
                'late_fee',
                'tax_amount',
                'total_due',
            ]);
        });
    }
};
