<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Role mapping from current system to Spatie
        $roleMapping = [
            'admin' => 'admin',
            'manager' => 'manager',
            'reviewer' => 'reviewer',
            'caretaker' => 'caretaker',
            'resident' => 'resident',
        ];

        // Get all users with their current roles
        $users = User::all();

        foreach ($users as $user) {
            if ($user->role) {
                $spatieRoleName = $roleMapping[$user->role->value] ?? null;
                
                if ($spatieRoleName) {
                    $spatieRole = Role::where('name', $spatieRoleName)->first();
                    
                    if ($spatieRole) {
                        // Assign Spatie role to user
                        $user->assignRole($spatieRole);
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove all user role assignments from Spatie
        DB::table('spatie_model_has_roles')->delete();
    }
};