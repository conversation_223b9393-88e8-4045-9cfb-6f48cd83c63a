<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('whatsapp_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('recipient_contact_id')->constrained('contacts')->onDelete('cascade');
            $table->foreignId('house_id')->constrained()->onDelete('cascade');
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->string('recipient');
            $table->string('message_type')->default('template');
            $table->text('content');
            $table->string('message_id')->nullable();
            $table->enum('status', ['pending', 'sent', 'delivered', 'read', 'failed'])->default('pending');
            $table->text('failed_reason')->nullable();
            $table->string('template_name')->nullable();
            $table->json('parameters')->nullable();
            $table->json('response_data')->nullable();
            $table->json('interactive_data')->nullable();
            $table->string('media_url')->nullable();
            $table->string('conversation_id')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->string('messageable_type')->nullable();
            $table->unsignedBigInteger('messageable_id')->nullable();
            $table->timestamps();

            $table->index(['estate_id', 'status', 'created_at']);
            $table->index(['house_id', 'status']);
            $table->index(['sender_id', 'status']);
            $table->index('recipient_contact_id');
            $table->index('recipient');
            $table->index('message_id');
            $table->index(['messageable_type', 'messageable_id']);
            $table->index('conversation_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('whatsapp_messages');
    }
};
