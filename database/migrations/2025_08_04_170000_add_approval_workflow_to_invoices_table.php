<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the table to modify enum constraints
        DB::statement('DROP INDEX IF EXISTS invoices_status_index');

        Schema::table('invoices', function (Blueprint $table) {
            // Add approval workflow fields first
            $table->foreignId('submitted_by')->nullable()->constrained('users')->onDelete('set null')->after('status');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null')->after('submitted_by');
            $table->timestamp('submitted_at')->nullable()->after('approved_by');
            $table->timestamp('approved_at')->nullable()->after('submitted_at');

            // Add reminder tracking fields
            $table->integer('reminder_count')->default(0)->after('approved_at');
            $table->timestamp('last_reminder_at')->nullable()->after('reminder_count');

            // Add disconnection field
            $table->date('disconnection_scheduled')->nullable()->after('last_reminder_at');

            // Add previous balance brought forward field
            $table->decimal('previous_balance_brought_forward', 10, 2)->default(0)->after('disconnection_scheduled');
        });

        // Note: SQLite doesn't support modifying enum constraints easily.
        // We'll handle the status enum in the model methods instead.

        // Add indexes for performance
        DB::statement('CREATE INDEX IF NOT EXISTS invoices_status_submitted_at_index ON invoices (status, submitted_at)');
        DB::statement('CREATE INDEX IF NOT EXISTS invoices_status_approved_at_index ON invoices (status, approved_at)');
        DB::statement('CREATE INDEX IF NOT EXISTS invoices_reminder_count_index ON invoices (reminder_count)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes
        DB::statement('DROP INDEX IF EXISTS invoices_status_submitted_at_index');
        DB::statement('DROP INDEX IF EXISTS invoices_status_approved_at_index');
        DB::statement('DROP INDEX IF EXISTS invoices_reminder_count_index');

        Schema::table('invoices', function (Blueprint $table) {
            // Drop columns
            $table->dropColumn([
                'submitted_by',
                'approved_by',
                'submitted_at',
                'approved_at',
                'reminder_count',
                'last_reminder_at',
                'disconnection_scheduled',
                'previous_balance_brought_forward',
            ]);
        });
    }
};
