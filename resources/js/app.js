/**
 * Water Management System - Main JavaScript Entry Point
 * Modern UI with Chart.js integration and enhanced UX
 */

import './bootstrap';
import { chartConfigs, chartUtils, applyDarkTheme } from './chart-config';
import { modalData, openModal, closeModal } from './components/modal';

// Global chart instances storage
window.chartInstances = new Map();

// Expose modal functions globally
window.modalData = modalData;
window.openModal = openModal;
window.closeModal = closeModal;

// Theme management
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupThemeToggle();
    }

    applyTheme(theme) {
        const html = document.documentElement;
        
        if (theme === 'dark') {
            html.classList.add('dark');
        } else {
            html.classList.remove('dark');
        }
        
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
        
        // Update existing charts for theme change
        this.updateChartsForTheme();
    }

    toggle() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
    }

    setupThemeToggle() {
        const toggleButtons = document.querySelectorAll('[data-theme-toggle]');
        toggleButtons.forEach(button => {
            button.addEventListener('click', () => this.toggle());
        });
    }

    updateChartsForTheme() {
        window.chartInstances.forEach((chart, id) => {
            if (chart && chart.config) {
                const isDark = this.currentTheme === 'dark';
                const config = isDark ? applyDarkTheme(chart.config) : chart.config;
                chart.options = config.options;
                chart.update('none');
            }
        });
    }
}

// Chart management utilities
class ChartManager {
    static createChart(canvasId, type, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.warn(`Canvas with id "${canvasId}" not found`);
            return null;
        }

        // Destroy existing chart if it exists
        this.destroyChart(canvasId);

        const ctx = canvas.getContext('2d');
        const baseConfig = chartConfigs[type] || chartConfigs.consumption;
        
        const config = {
            ...baseConfig,
            data: {
                ...baseConfig.data,
                ...data
            },
            options: {
                ...baseConfig.options,
                ...options
            }
        };

        // Apply dark theme if active
        const isDark = document.documentElement.classList.contains('dark');
        const finalConfig = isDark ? applyDarkTheme(config) : config;

        const chart = chartUtils.createChart(ctx, finalConfig);
        
        if (chart) {
            window.chartInstances.set(canvasId, chart);
        }

        return chart;
    }

    static updateChart(canvasId, newData) {
        const chart = window.chartInstances.get(canvasId);
        if (chart) {
            chartUtils.updateChart(chart, newData);
        }
    }

    static destroyChart(canvasId) {
        const chart = window.chartInstances.get(canvasId);
        if (chart) {
            chartUtils.destroyChart(chart);
            window.chartInstances.delete(canvasId);
        }
    }

    static destroyAllCharts() {
        window.chartInstances.forEach((chart, id) => {
            chartUtils.destroyChart(chart);
        });
        window.chartInstances.clear();
    }
}

// Enhanced UI interactions
class UIEnhancer {
    static init() {
        this.setupAnimations();
        this.setupTooltips();
        this.setupModals();
        this.setupTouchTargets();
        this.setupLoadingStates();
    }

    static setupAnimations() {
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements with animation classes
        document.querySelectorAll('.kpi-card, .chart-container').forEach(el => {
            observer.observe(el);
        });
    }

    static setupTooltips() {
        // Simple tooltip implementation
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                const tooltip = document.createElement('div');
                tooltip.className = 'absolute z-50 px-2 py-1 text-xs text-white bg-zinc-900 rounded shadow-lg pointer-events-none';
                tooltip.textContent = e.target.dataset.tooltip;
                
                document.body.appendChild(tooltip);
                
                const rect = e.target.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
                
                e.target._tooltip = tooltip;
            });

            element.addEventListener('mouseleave', (e) => {
                if (e.target._tooltip) {
                    e.target._tooltip.remove();
                    delete e.target._tooltip;
                }
            });
        });
    }

    static setupModals() {
        // Enhanced modal handling with better accessibility
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal-open]')) {
                const modalId = e.target.dataset.modalOpen;
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.remove('hidden');
                    modal.classList.add('animate-fade-in');
                    
                    // Focus management
                    const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                    if (firstFocusable) {
                        firstFocusable.focus();
                    }
                    
                    // Trap focus within modal
                    this.trapFocus(modal);
                }
            }

            if (e.target.matches('[data-modal-close]') || e.target.closest('[data-modal-close]')) {
                const modal = e.target.closest('.modal') || document.querySelector('.modal:not(.hidden)');
                if (modal) {
                    modal.classList.add('hidden');
                    modal.classList.remove('animate-fade-in');
                }
            }
        });

        // Close modal on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal:not(.hidden)');
                if (openModal) {
                    openModal.classList.add('hidden');
                    openModal.classList.remove('animate-fade-in');
                }
            }
        });
    }

    static trapFocus(element) {
        const focusableElements = element.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];

        element.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstFocusable) {
                        lastFocusable.focus();
                        e.preventDefault();
                    }
                } else {
                    if (document.activeElement === lastFocusable) {
                        firstFocusable.focus();
                        e.preventDefault();
                    }
                }
            }
        });
    }

    static setupTouchTargets() {
        // Ensure touch targets are at least 44px for mobile accessibility
        const touchElements = document.querySelectorAll('button, a, input[type="checkbox"], input[type="radio"]');
        touchElements.forEach(element => {
            if (!element.classList.contains('touch-target')) {
                element.classList.add('touch-target');
            }
        });
    }

    static setupLoadingStates() {
        // Enhanced loading states for better UX
        document.addEventListener('livewire:init', () => {
            Livewire.hook('message.sent', () => {
                document.body.classList.add('loading');
            });

            Livewire.hook('message.processed', () => {
                document.body.classList.remove('loading');
            });
        });
    }
}

// Performance monitoring
class PerformanceMonitor {
    static init() {
        this.measurePageLoad();
        this.setupLazyLoading();
    }

    static measurePageLoad() {
        window.addEventListener('load', () => {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                console.log('Page Load Performance:', {
                    domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                    loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                    totalTime: perfData.loadEventEnd - perfData.fetchStart
                });
            }
        });
    }

    static setupLazyLoading() {
        // Lazy load charts that are not immediately visible
        const chartObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const canvas = entry.target;
                    const chartType = canvas.dataset.chartType;
                    const chartData = canvas.dataset.chartData;
                    
                    if (chartType && chartData) {
                        try {
                            const data = JSON.parse(chartData);
                            ChartManager.createChart(canvas.id, chartType, data);
                        } catch (error) {
                            console.error('Error parsing chart data:', error);
                        }
                    }
                    
                    chartObserver.unobserve(canvas);
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('canvas[data-lazy-chart]').forEach(canvas => {
            chartObserver.observe(canvas);
        });
    }
}

// Make global search available globally
window.openGlobalSearch = async () => {
    const { GlobalSearch } = await import('./global-search.js');
    GlobalSearch.openSearch();
};

// Initialize everything when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Initialize theme management
    window.themeManager = new ThemeManager();
    
    // Initialize UI enhancements
    UIEnhancer.init();
    
    // Initialize performance monitoring
    PerformanceMonitor.init();
    
    // Make chart manager globally available
    window.ChartManager = ChartManager;
    
    console.log('Water Management System UI initialized');
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    ChartManager.destroyAllCharts();
});

// Export for use in other modules
export { ChartManager, ThemeManager, UIEnhancer };
