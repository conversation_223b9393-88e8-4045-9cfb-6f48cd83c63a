/**
 * Global Search functionality
 */
class GlobalSearch {
    static init() {
        this.setupKeyboardShortcuts();
        this.createSearchModal();
    }

    static setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K to open search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.openSearch();
            }
            
            // Escape to close search
            if (e.key === 'Escape') {
                this.closeSearch();
            }
        });
    }

    static openSearch() {
        let searchModal = document.getElementById('global-search-modal');
        if (!searchModal) {
            searchModal = this.createSearchModal();
            document.body.appendChild(searchModal);
        }
        
        searchModal.classList.remove('hidden');
        const searchInput = searchModal.querySelector('#global-search-input');
        if (searchInput) {
            setTimeout(() => searchInput.focus(), 100);
        }
    }

    static closeSearch() {
        const modal = document.getElementById('global-search-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    static createSearchModal() {
        const modal = document.createElement('div');
        modal.id = 'global-search-modal';
        modal.className = 'fixed inset-0 z-50 flex items-start justify-center pt-16 px-4 bg-black/50 backdrop-blur-sm hidden';
        
        modal.innerHTML = `
            <div class="w-full max-w-2xl bg-white dark:bg-zinc-800 rounded-xl shadow-2xl border border-zinc-200 dark:border-zinc-700 overflow-hidden">
                <div class="p-4 border-b border-zinc-200 dark:border-zinc-700">
                    <div class="flex items-center space-x-3">
                        <svg class="w-5 h-5 text-water-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <input
                            id="global-search-input"
                            type="text"
                            placeholder="Search estates, houses, readings, contacts..."
                            class="flex-1 bg-transparent border-none outline-none text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400"
                        />
                        <button onclick="GlobalSearch.closeSearch()" class="text-zinc-400 hover:text-zinc-600 dark:hover:text-zinc-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <div id="search-results" class="max-h-96 overflow-y-auto">
                    <div class="p-4 text-center text-zinc-500 dark:text-zinc-400">
                        <p>Start typing to search... <kbd class="px-1 py-0.5 bg-zinc-200 dark:bg-zinc-700 rounded text-xs">Ctrl+K</kbd></p>
                    </div>
                </div>
                
                <div class="p-3 bg-zinc-50 dark:bg-zinc-900 border-t border-zinc-200 dark:border-zinc-700">
                    <div class="flex items-center justify-between text-xs text-zinc-500 dark:text-zinc-400">
                        <span>Press <kbd class="px-1 py-0.5 bg-zinc-200 dark:bg-zinc-700 rounded">Enter</kbd> to select</span>
                        <span>Press <kbd class="px-1 py-0.5 bg-zinc-200 dark:bg-zinc-700 rounded">Esc</kbd> to close</span>
                    </div>
                </div>
            </div>
        `;
        
        // Add event listeners
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeSearch();
            }
        });
        
        const searchInput = modal.querySelector('#global-search-input');
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSearch();
            } else if (e.key === 'Enter') {
                this.performSearch(this.value);
            }
        });
        
        searchInput.addEventListener('input', (e) => {
            this.debounceSearch(e.target.value);
        });
        
        return modal;
    }

    static debounceSearch(query) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            if (query.length >= 2) {
                this.performSearch(query);
            } else {
                this.showSearchPlaceholder();
            }
        }, 300);
    }

    static performSearch(query) {
        const resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) return;
        
        // Show loading state
        resultsContainer.innerHTML = `
            <div class="p-4 text-center">
                <div class="animate-spin w-6 h-6 border-2 border-water-500 border-t-transparent rounded-full mx-auto"></div>
                <p class="mt-2 text-zinc-500 dark:text-zinc-400">Searching...</p>
            </div>
        `;
        
        // Simulate search results (replace with actual API call)
        setTimeout(() => {
            const mockResults = [
                { type: 'estate', title: 'Riverside Estate', subtitle: 'Estate Management', url: '/estates/1' },
                { type: 'house', title: 'House #A-101', subtitle: 'Riverside Estate', url: '/houses/1' },
                { type: 'contact', title: 'John Doe', subtitle: 'Estate Manager', url: '/contacts/1' },
                { type: 'reading', title: 'Recent Reading', subtitle: 'House #A-101 - 150 units', url: '/readings/1' }
            ].filter(item =>
                item.title.toLowerCase().includes(query.toLowerCase()) ||
                item.subtitle.toLowerCase().includes(query.toLowerCase())
            );
            
            if (mockResults.length > 0) {
                resultsContainer.innerHTML = mockResults.map(result => `
                    <a href="${result.url}" wire:navigate class="block p-3 hover:bg-zinc-50 dark:hover:bg-zinc-700 border-b border-zinc-100 dark:border-zinc-800 last:border-b-0" onclick="GlobalSearch.closeSearch()">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 rounded-lg bg-water-100 dark:bg-water-900 flex items-center justify-center">
                                ${this.getSearchIcon(result.type)}
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-zinc-900 dark:text-zinc-100">${result.title}</p>
                                <p class="text-sm text-zinc-600 dark:text-zinc-400">${result.subtitle}</p>
                            </div>
                        </div>
                    </a>
                `).join('');
            } else {
                resultsContainer.innerHTML = `
                    <div class="p-4 text-center text-zinc-500 dark:text-zinc-400">
                        <p>No results found for "${query}"</p>
                    </div>
                `;
            }
        }, 500);
    }

    static getSearchIcon(type) {
        const icons = {
            estate: '<svg class="w-4 h-4 text-water-600" fill="currentColor" viewBox="0 0 20 20"><path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9z"></path></svg>',
            house: '<svg class="w-4 h-4 text-water-600" fill="currentColor" viewBox="0 0 20 20"><path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path></svg>',
            contact: '<svg class="w-4 h-4 text-water-600" fill="currentColor" viewBox="0 0 20 20"><path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"></path></svg>',
            reading: '<svg class="w-4 h-4 text-water-600" fill="currentColor" viewBox="0 0 20 20"><path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path><path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a2 2 0 002 2h6a2 2 0 002-2V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path></svg>'
        };
        return icons[type] || icons.estate;
    }

    static showSearchPlaceholder() {
        const resultsContainer = document.getElementById('search-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="p-4 text-center text-zinc-500 dark:text-zinc-400">
                    <p>Start typing to search...</p>
                </div>
            `;
        }
    }
}

export { GlobalSearch };