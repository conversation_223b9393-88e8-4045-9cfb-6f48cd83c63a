/**
 * Chart.js Configuration for Water Management System
 * Modern, accessible charts with water management theme
 */

import { Chart, registerables } from 'chart.js';

// Register all Chart.js components
Chart.register(...registerables);

// Water Management Theme Colors
const colors = {
    water: {
        50: '#f0f9ff',
        100: '#e0f2fe',
        200: '#bae6fd',
        300: '#7dd3fc',
        400: '#38bdf8',
        500: '#0ea5e9',
        600: '#0284c7',
        700: '#0369a1',
        800: '#075985',
        900: '#0c4a6e',
    },
    teal: {
        50: '#f0fdfa',
        100: '#ccfbf1',
        200: '#99f6e4',
        300: '#5eead4',
        400: '#2dd4bf',
        500: '#14b8a6',
        600: '#0d9488',
        700: '#0f766e',
        800: '#115e59',
        900: '#134e4a',
    },
    success: '#22c55e',
    warning: '#f59e0b',
    danger: '#ef4444',
    zinc: {
        100: '#f4f4f5',
        200: '#e4e4e7',
        300: '#d4d4d8',
        400: '#a1a1aa',
        500: '#71717a',
        600: '#52525b',
        700: '#3f3f46',
        800: '#27272a',
        900: '#18181b',
    }
};

// Default Chart.js configuration
const defaultConfig = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
        intersect: false,
        mode: 'index',
    },
    plugins: {
        legend: {
            display: true,
            position: 'top',
            labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                    family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                    size: 12,
                    weight: '500',
                },
                color: colors.zinc[600],
            },
        },
        tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: colors.water[600],
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: true,
            titleFont: {
                family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                size: 13,
                weight: '600',
            },
            bodyFont: {
                family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                size: 12,
                weight: '400',
            },
            padding: 12,
        },
    },
    scales: {
        x: {
            grid: {
                display: true,
                color: colors.zinc[200],
                drawBorder: false,
            },
            ticks: {
                font: {
                    family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                    size: 11,
                },
                color: colors.zinc[500],
                padding: 8,
            },
        },
        y: {
            grid: {
                display: true,
                color: colors.zinc[200],
                drawBorder: false,
            },
            ticks: {
                font: {
                    family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                    size: 11,
                },
                color: colors.zinc[500],
                padding: 8,
                callback: function(value) {
                    return value.toLocaleString();
                }
            },
            beginAtZero: true,
        },
    },
    elements: {
        point: {
            radius: 4,
            hoverRadius: 6,
            borderWidth: 2,
        },
        line: {
            borderWidth: 3,
            tension: 0.1,
        },
        bar: {
            borderRadius: 4,
            borderSkipped: false,
        },
    },
    animation: {
        duration: 750,
        easing: 'easeInOutQuart',
    },
};

// Chart type configurations
export const chartConfigs = {
    // Line chart for consumption trends
    consumption: {
        type: 'line',
        options: {
            ...defaultConfig,
            plugins: {
                ...defaultConfig.plugins,
                title: {
                    display: true,
                    text: 'Water Consumption Trends',
                    font: {
                        family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                        size: 16,
                        weight: '600',
                    },
                    color: colors.zinc[900],
                    padding: 20,
                },
            },
            scales: {
                ...defaultConfig.scales,
                y: {
                    ...defaultConfig.scales.y,
                    title: {
                        display: true,
                        text: 'Consumption (Liters)',
                        font: {
                            family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                            size: 12,
                            weight: '500',
                        },
                        color: colors.zinc[600],
                    },
                },
            },
        },
        data: {
            datasets: [{
                label: 'Daily Consumption',
                borderColor: colors.water[600],
                backgroundColor: colors.water[100],
                fill: true,
                pointBackgroundColor: colors.water[600],
                pointBorderColor: '#ffffff',
            }]
        }
    },

    // Area chart for revenue
    revenue: {
        type: 'line',
        options: {
            ...defaultConfig,
            plugins: {
                ...defaultConfig.plugins,
                title: {
                    display: true,
                    text: 'Revenue Trends',
                    font: {
                        family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                        size: 16,
                        weight: '600',
                    },
                    color: colors.zinc[900],
                    padding: 20,
                },
            },
            scales: {
                ...defaultConfig.scales,
                y: {
                    ...defaultConfig.scales.y,
                    title: {
                        display: true,
                        text: 'Revenue (KES)',
                        font: {
                            family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                            size: 12,
                            weight: '500',
                        },
                        color: colors.zinc[600],
                    },
                    ticks: {
                        ...defaultConfig.scales.y.ticks,
                        callback: function(value) {
                            return 'KES ' + value.toLocaleString();
                        }
                    },
                },
            },
        },
        data: {
            datasets: [{
                label: 'Daily Revenue',
                borderColor: colors.teal[600],
                backgroundColor: colors.teal[100],
                fill: true,
                pointBackgroundColor: colors.teal[600],
                pointBorderColor: '#ffffff',
            }]
        }
    },

    // Bar chart for estate comparison
    estateComparison: {
        type: 'bar',
        options: {
            ...defaultConfig,
            plugins: {
                ...defaultConfig.plugins,
                title: {
                    display: true,
                    text: 'Estate Consumption Comparison',
                    font: {
                        family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                        size: 16,
                        weight: '600',
                    },
                    color: colors.zinc[900],
                    padding: 20,
                },
            },
            scales: {
                ...defaultConfig.scales,
                y: {
                    ...defaultConfig.scales.y,
                    title: {
                        display: true,
                        text: 'Total Consumption (Liters)',
                        font: {
                            family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                            size: 12,
                            weight: '500',
                        },
                        color: colors.zinc[600],
                    },
                },
            },
        },
        data: {
            datasets: [{
                label: 'Estate Consumption',
                backgroundColor: [
                    colors.water[600],
                    colors.teal[600],
                    colors.water[500],
                    colors.teal[500],
                    colors.water[700],
                    colors.teal[700],
                ],
                borderColor: [
                    colors.water[700],
                    colors.teal[700],
                    colors.water[600],
                    colors.teal[600],
                    colors.water[800],
                    colors.teal[800],
                ],
                borderWidth: 1,
            }]
        }
    },

    // Doughnut chart for status distribution
    statusDistribution: {
        type: 'doughnut',
        options: {
            ...defaultConfig,
            plugins: {
                ...defaultConfig.plugins,
                title: {
                    display: true,
                    text: 'Reading Status Distribution',
                    font: {
                        family: 'Instrument Sans, ui-sans-serif, system-ui, sans-serif',
                        size: 16,
                        weight: '600',
                    },
                    color: colors.zinc[900],
                    padding: 20,
                },
            },
            cutout: '60%',
        },
        data: {
            datasets: [{
                backgroundColor: [
                    colors.success,
                    colors.warning,
                    colors.danger,
                    colors.zinc[400],
                ],
                borderColor: '#ffffff',
                borderWidth: 2,
            }]
        }
    },
};

// Utility functions
export const chartUtils = {
    // Create chart with error handling
    createChart: (ctx, config) => {
        try {
            return new Chart(ctx, config);
        } catch (error) {
            console.error('Error creating chart:', error);
            return null;
        }
    },

    // Update chart data safely
    updateChart: (chart, newData) => {
        if (!chart) return;
        
        try {
            chart.data = newData;
            chart.update('active');
        } catch (error) {
            console.error('Error updating chart:', error);
        }
    },

    // Destroy chart safely
    destroyChart: (chart) => {
        if (chart && typeof chart.destroy === 'function') {
            chart.destroy();
        }
    },

    // Format numbers for display
    formatNumber: (value, type = 'number') => {
        switch (type) {
            case 'currency':
                return 'KES ' + value.toLocaleString();
            case 'percentage':
                return value.toFixed(1) + '%';
            case 'liters':
                return value.toLocaleString() + ' L';
            default:
                return value.toLocaleString();
        }
    },

    // Generate gradient backgrounds
    createGradient: (ctx, color1, color2) => {
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, color1);
        gradient.addColorStop(1, color2);
        return gradient;
    },
};

// Dark theme adjustments
export const applyDarkTheme = (config) => {
    const darkConfig = JSON.parse(JSON.stringify(config));
    
    // Update colors for dark theme
    if (darkConfig.options?.plugins?.legend?.labels) {
        darkConfig.options.plugins.legend.labels.color = colors.zinc[400];
    }
    
    if (darkConfig.options?.plugins?.title) {
        darkConfig.options.plugins.title.color = colors.zinc[100];
    }
    
    if (darkConfig.options?.scales) {
        Object.keys(darkConfig.options.scales).forEach(scaleKey => {
            const scale = darkConfig.options.scales[scaleKey];
            if (scale.grid) {
                scale.grid.color = colors.zinc[700];
            }
            if (scale.ticks) {
                scale.ticks.color = colors.zinc[400];
            }
            if (scale.title) {
                scale.title.color = colors.zinc[400];
            }
        });
    }
    
    return darkConfig;
};

export { colors, defaultConfig };