@import "tailwindcss";

/* Custom base styles */
body {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100;
  font-family: 'Inter', system-ui, sans-serif;
}

/* Enhanced form input styling */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
input[type="search"],
select,
textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm 
         placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 
         focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed
         dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100 
         dark:placeholder-gray-500 dark:focus:ring-blue-400 dark:focus:border-blue-400
         dark:disabled:bg-gray-700 transition-colors duration-200;
}

/* Input group styling */
.input-group {
  @apply relative;
}

.input-group input,
.input-group select,
.input-group textarea {
  @apply pr-10;
}

.input-group-icon {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none dark:text-gray-500;
}

/* Search input with icon styling */
.search-input-group {
  @apply relative;
}

.search-input-group input {
  @apply pl-10 pr-4;
}

.search-input-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none dark:text-gray-500;
}

/* Select dropdown styling */
.select-group {
  @apply relative;
}

.select-group select {
  @apply appearance-none pr-10;
}

.select-chevron {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none dark:text-gray-500;
}

/* Ensure proper spacing for inputs with icons */
input[pl-10] {
  @apply pl-10;
}

select[pr-10] {
  @apply pr-10;
}

/* Form field styling */
.form-field {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300;
}

.form-label.required::after {
  content: ' *';
  @apply text-red-500;
}

.form-description {
  @apply mt-1 text-sm text-gray-500 dark:text-gray-400;
}

.form-error {
  @apply mt-1 text-sm text-red-600 dark:text-red-400;
}

.form-hint {
  @apply mt-1 text-sm text-gray-500 dark:text-gray-400;
}

/* Checkbox and radio styling */
input[type="checkbox"],
input[type="radio"] {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 
         rounded focus:ring-2 dark:bg-gray-800 dark:border-gray-600 
         dark:focus:ring-blue-400 dark:focus:ring-offset-gray-900;
}

/* File input styling */
input[type="file"] {
  @apply block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4
         file:rounded-md file:border-0 file:text-sm file:font-medium
         file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100
         dark:file:bg-blue-900/50 dark:file:text-blue-300 dark:hover:file:bg-blue-900;
}

/* Select styling enhancements */
select {
  @apply appearance-none bg-no-repeat bg-center;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMSAxLjVMMTYgNi41TDIxIDEuNSIgc3Ryb2tlPSIjNkI3MjgwIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
  background-position: right 0.5rem center;
  background-size: 1rem 1rem;
  padding-right: 2.5rem;
}

select.dark {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMSAxLjVMMTYgNi41TDIxIDEuNSIgc3Ryb2tlPSIjOENCOTk2IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
}

/* Range input styling */
input[type="range"] {
  @apply w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer
         dark:bg-gray-700 [&::-webkit-slider-thumb]:appearance-none 
         [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 
         [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-600 
         [&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:hover:bg-blue-700
         dark:[&::-webkit-slider-thumb]:bg-blue-500 dark:[&::-webkit-slider-thumb]:hover:bg-blue-400;
}

/* Color input styling */
input[type="color"] {
  @apply h-10 w-16 border border-gray-300 rounded-md cursor-pointer
         dark:border-gray-600 dark:bg-gray-800;
}

/* Enhanced utility classes */
@layer utilities {
  /* Responsive table utilities */
  .responsive-table {
    @apply block overflow-x-auto whitespace-nowrap;
  }

  .responsive-table table {
    @apply min-w-full;
  }

  .responsive-table th,
  .responsive-table td {
    @apply px-4 py-3 text-left;
  }

  .responsive-table th {
    @apply text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 dark:bg-gray-800 dark:text-gray-400;
  }

  .responsive-table td {
    @apply text-sm text-gray-900 dark:text-gray-100;
  }

  /* Mobile card view for tables */
  .mobile-card {
    @apply block bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-4;
  }

  .mobile-card-header {
    @apply flex items-center justify-between mb-3 pb-2 border-b border-gray-200 dark:border-gray-700;
  }

  .mobile-card-title {
    @apply text-sm font-medium text-gray-900 dark:text-white;
  }

  .mobile-card-subtitle {
    @apply text-xs text-gray-500 dark:text-gray-400;
  }

  .mobile-card-content {
    @apply space-y-2;
  }

  .mobile-card-row {
    @apply flex justify-between items-center py-1;
  }

  .mobile-card-label {
    @apply text-xs font-medium text-gray-500 dark:text-gray-400;
  }

  .mobile-card-value {
    @apply text-sm text-gray-900 dark:text-gray-100 text-right;
  }

  /* Responsive grid utilities */
  .grid-responsive {
    @apply grid grid-cols-1 gap-4;
  }

  @media (min-width: 640px) {
    .grid-responsive {
      @apply grid-cols-2;
    }
  }

  @media (min-width: 768px) {
    .grid-responsive {
      @apply grid-cols-3;
    }
  }

  @media (min-width: 1024px) {
    .grid-responsive {
      @apply grid-cols-4;
    }
  }

  /* Responsive form utilities */
  .form-grid {
    @apply space-y-4;
  }

  @media (min-width: 768px) {
    .form-grid {
      @apply grid grid-cols-2 gap-4 space-y-0;
    }
  }

  @media (min-width: 1024px) {
    .form-grid {
      @apply grid-cols-3;
    }
  }

  @media (min-width: 1280px) {
    .form-grid {
      @apply grid-cols-4;
    }
  }

  /* Responsive header utilities */
  .header-responsive {
    @apply space-y-4;
  }

  @media (min-width: 768px) {
    .header-responsive {
      @apply flex items-center justify-between space-y-0;
    }
  }

  /* Responsive action buttons */
  .action-buttons {
    @apply flex flex-col space-y-2 space-x-0;
  }

  @media (min-width: 640px) {
    .action-buttons {
      @apply flex-row space-y-0 space-x-2;
    }
  }

  /* Responsive statistics cards */
  .stats-grid {
    @apply grid grid-cols-1 gap-4;
  }

  @media (min-width: 640px) {
    .stats-grid {
      @apply grid-cols-2;
    }
  }

  @media (min-width: 1024px) {
    .stats-grid {
      @apply grid-cols-4;
    }
  }
  /* Card utilities */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50;
  }

  /* Badge utilities */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

.badge-primary {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300;
}
  .badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300;
  }

  .badge-secondary {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
  }

  /* Table utilities */
  .table-container {
    @apply overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700;
  }

  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }

  .table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 dark:bg-gray-800 dark:text-gray-400;
  }

  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
  }

  .table-striped tbody tr:nth-child(even) {
    @apply bg-gray-50 dark:bg-gray-800/50;
  }

  .table-hover tbody tr:hover {
    @apply bg-gray-50 dark:bg-gray-800/70;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
  }

  .skeleton-text {
    @apply h-4 bg-gray-200 dark:bg-gray-700 rounded;
  }

  .skeleton-avatar {
    @apply h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full;
  }

  .skeleton-button {
    @apply h-10 bg-gray-200 dark:bg-gray-700 rounded;
  }

/* Focus visible improvements */
.focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-blue-400 dark:ring-offset-gray-900;
}
  /* Transition utilities */
  .transition-all {
    @apply transition-all duration-200 ease-in-out;
  }

  .transition-colors {
    @apply transition-colors duration-200 ease-in-out;
  }

  .transition-transform {
    @apply transition-transform duration-200 ease-in-out;
  }

  /* Spacing utilities */
  .space-y-1 > * + * {
    @apply mt-1;
  }

  .space-y-2 > * + * {
    @apply mt-2;
  }

  .space-y-3 > * + * {
    @apply mt-3;
  }

  .space-y-4 > * + * {
    @apply mt-4;
  }

  .space-x-1 > * + * {
    @apply ml-1;
  }

  .space-x-2 > * + * {
    @apply ml-2;
  }

  .space-x-3 > * + * {
    @apply ml-3;
  }

  .space-x-4 > * + * {
    @apply ml-4;
  }
  /* For Remove Date Icon */
  input[type="date"]::-webkit-inner-spin-button,
  input[type="time"]::-webkit-inner-spin-button,
  input[type="date"]::-webkit-calendar-picker-indicator,
  input[type="time"]::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
  }

  /* Scrollbar utilities */
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .custom-scrollbar {
    &::-webkit-scrollbar {
      @apply w-1.5 h-1.5;
    }

    &::-webkit-scrollbar-track {
      @apply bg-gray-200 dark:bg-gray-800 rounded-full;
    }

    &::-webkit-scrollbar-thumb {
      @apply bg-gray-400 dark:bg-gray-600 rounded-full;
    }
  }

  /* Sidebar utilities */
  .sidebar {
    transition: all 0.3s ease-in-out;
  }

  .menu-item {
    position: relative;
    transition: all 0.3s ease-in-out;
  }

.menu-item-active {
  @apply bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400;
}
  .menu-item-inactive {
    @apply text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800;
  }

.menu-item-icon-active {
  @apply text-blue-700 dark:text-blue-400;
}
  .menu-item-icon-inactive {
    @apply text-gray-500 dark:text-gray-400;
  }

  .menu-item-text {
    transition: all 0.3s ease-in-out;
  }

  .menu-group-title {
    transition: all 0.3s ease-in-out;
  }

  .menu-group-icon {
    transition: all 0.3s ease-in-out;
  }

  .logo {
    transition: all 0.3s ease-in-out;
  }

  .logo-icon {
    transition: all 0.3s ease-in-out;
  }

  .sidebar-header {
    transition: all 0.3s ease-in-out;
  }
}

/* Third-party library overrides */
.apexcharts-legend-text {
  @apply !text-gray-700 dark:!text-gray-400;
}

.apexcharts-text {
  @apply !fill-gray-700 dark:!fill-gray-400;
}

.apexcharts-tooltip.apexcharts-theme-light {
  @apply !shadow-md !gap-1 !rounded-lg !border-gray-200 !p-3 dark:!border-gray-800 dark:!bg-gray-900;
}

/* Flatpickr overrides */
.flatpickr-calendar {
  @apply dark:!bg-gray-800 dark:!shadow-xl;
}

.flatpickr-day {
  @apply dark:!text-gray-100 dark:hover:!bg-gray-700;
}

/* FullCalendar overrides */
.fc .fc-toolbar-title {
  @apply !text-gray-900 dark:!text-gray-100;
}

.fc .fc-daygrid-day-number {
  @apply !text-gray-700 dark:!text-gray-400;
}
