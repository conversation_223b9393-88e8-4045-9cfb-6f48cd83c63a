<div>
    <div class="p-6 space-y-6">
        <!-- <PERSON> Header -->
        <div class="mb-4">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Estate Analytics</h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">Detailed performance analysis for all estates</p>
                </div>
                <div class="flex items-center space-x-3">
                    <x-button href="{{ route('dashboard') }}" variant="ghost" size="sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Dashboard
                    </x-button>
                </div>
            </div>
            <hr class="border-gray-200 dark:border-gray-700">
        </div>
    </div>

    <!-- Filters -->
    <div class="p-6 space-y-6">
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Filters</h2>
            </div>

            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="form-field">
                        <label class="form-label">Estate</label>
                        <select wire:model.live="selectedEstate" class="w-full">
                            <option value="">All Estates</option>
                            @foreach($estates as $estate)
                            <option value="{{ $estate->id }}">{{ $estate->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-field">
                        <label class="form-label">Date Range</label>
                        <select wire:model.live="dateRange" class="w-full">
                            <option value="7">Last 7 days</option>
                            <option value="30">Last 30 days</option>
                            <option value="90">Last 90 days</option>
                            <option value="365">Last year</option>
                        </select>
                    </div>

                    <div class="form-field">
                        <label class="form-label">Search</label>
                        <div class="input-group">
                            <input
                                type="text"
                                wire:model.live.debounce.300ms="search"
                                placeholder="Search estates..."
                                class="w-full"
                            >
                            <svg class="input-group-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
            <div class="card">
                <div class="card-header pb-2">
                    <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Estates</h3>
                </div>
                <div class="card-body pt-2">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ number_format($summaryStats['total_estates']) }}
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header pb-2">
                    <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Houses</h3>
                </div>
                <div class="card-body pt-2">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ number_format($summaryStats['total_houses']) }}
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header pb-2">
                    <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Consumption</h3>
                </div>
                <div class="card-body pt-2">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {{ number_format($summaryStats['total_consumption']) }} L
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header pb-2">
                    <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</h3>
                </div>
                <div class="card-body pt-2">
                    <div class="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                        KES {{ number_format($summaryStats['total_revenue']) }}
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header pb-2">
                    <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Occupancy</h3>
                </div>
                <div class="card-body pt-2">
                    <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {{ number_format($summaryStats['avg_occupancy'], 1) }}%
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header pb-2">
                    <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Efficiency</h3>
                </div>
                <div class="card-body pt-2">
                    <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                        {{ number_format($summaryStats['avg_efficiency'], 2) }} KES/L
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performers -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Top Performing Estates</h2>
            </div>

            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($topPerformers as $index => $analytics)
                    <div class="p-4 rounded-lg border {{ $index === 0 ? 'border-yellow-200 bg-yellow-50 dark:border-yellow-700 dark:bg-yellow-900/20' : ($index === 1 ? 'border-gray-200 bg-gray-50 dark:border-gray-600 dark:bg-gray-700/20' : 'border-orange-200 bg-orange-50 dark:border-orange-700 dark:bg-orange-900/20') }}">
                        <div class="flex items-center justify-between mb-3">
                            <span class="badge {{ $index === 0 ? 'badge-warning' : ($index === 1 ? 'badge-secondary' : 'badge-danger') }}">
                                #{{ $index + 1 }}
                            </span>
                            <span class="badge {{ $analytics['growth_rate'] >= 0 ? 'badge-success' : 'badge-danger' }}">
                                {{ $analytics['growth_rate'] >= 0 ? '+' : '' }}{{ number_format($analytics['growth_rate'], 1) }}%
                            </span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">{{ $analytics['estate']->name }}</h3>
                        <div class="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>Houses:</span>
                                <span class="font-medium">{{ number_format($analytics['total_houses']) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Consumption:</span>
                                <span class="font-medium">{{ number_format($analytics['total_consumption']) }} L</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Revenue:</span>
                                <span class="font-medium">KES {{ number_format($analytics['total_revenue']) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Occupancy:</span>
                                <span class="font-medium">{{ number_format($analytics['occupancy_rate'], 1) }}%</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="card">
                <div class="card-header">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Consumption by Estate</h2>
                </div>
                <div class="card-body">
                    <canvas id="consumptionChart" height="300"></canvas>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Revenue by Estate</h2>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="300"></canvas>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Efficiency Score</h2>
                </div>
                <div class="card-body">
                    <canvas id="efficiencyChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Detailed Analytics Table -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Detailed Estate Analytics</h2>
            </div>

            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th class="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800" wire:click="sortBy('estate.name')">
                                    <div class="flex items-center space-x-1">
                                        <span>Estate Name</span>
                                        @if($sortBy === 'estate.name')
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7' }}"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </th>
                                <th class="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800" wire:click="sortBy('total_houses')">
                                    <div class="flex items-center space-x-1">
                                        <span>Houses</span>
                                        @if($sortBy === 'total_houses')
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7' }}"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </th>
                                <th class="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800" wire:click="sortBy('occupancy_rate')">
                                    <div class="flex items-center space-x-1">
                                        <span>Occupancy</span>
                                        @if($sortBy === 'occupancy_rate')
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7' }}"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </th>
                                <th class="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800" wire:click="sortBy('total_consumption')">
                                    <div class="flex items-center space-x-1">
                                        <span>Consumption</span>
                                        @if($sortBy === 'total_consumption')
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7' }}"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </th>
                                <th class="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800" wire:click="sortBy('total_revenue')">
                                    <div class="flex items-center space-x-1">
                                        <span>Revenue</span>
                                        @if($sortBy === 'total_revenue')
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7' }}"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </th>
                                <th class="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800" wire:click="sortBy('efficiency_score')">
                                    <div class="flex items-center space-x-1">
                                        <span>Efficiency</span>
                                        @if($sortBy === 'efficiency_score')
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7' }}"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </th>
                                <th class="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800" wire:click="sortBy('growth_rate')">
                                    <div class="flex items-center space-x-1">
                                        <span>Growth</span>
                                        @if($sortBy === 'growth_rate')
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7' }}"></path>
                                            </svg>
                                        @endif
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($estateAnalytics as $analytics)
                            <tr>
                                <td>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">{{ $analytics['estate']->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $analytics['estate']->location ?? 'No location' }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>{{ number_format($analytics['total_houses']) }}</div>
                                    <div class="text-sm text-gray-500">{{ number_format($analytics['occupied_houses']) }} occupied</div>
                                </td>
                                <td>
                                    <div class="flex items-center space-x-2">
                                        <span>{{ number_format($analytics['occupancy_rate'], 1) }}%</span>
                                        <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ min(100, $analytics['occupancy_rate']) }}%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>{{ number_format($analytics['total_consumption']) }} L</div>
                                    <div class="text-sm text-gray-500">{{ number_format($analytics['avg_consumption_per_house']) }} L/house</div>
                                </td>
                                <td>
                                    <div>KES {{ number_format($analytics['total_revenue']) }}</div>
                                    <div class="text-sm text-gray-500">KES {{ number_format($analytics['revenue_per_house']) }}/house</div>
                                </td>
                                <td>
                                    <div>{{ number_format($analytics['efficiency_score'], 2) }}</div>
                                    <div class="text-sm text-gray-500">KES/L</div>
                                </td>
                                <td>
                                    <span class="badge {{ $analytics['growth_rate'] >= 0 ? 'badge-success' : 'badge-danger' }}">
                                        {{ $analytics['growth_rate'] >= 0 ? '+' : '' }}{{ number_format($analytics['growth_rate'], 1) }}%
                                    </span>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center py-12">
                                    <div class="text-gray-500 dark:text-gray-400">
                                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <p class="text-lg font-medium">No estate data available</p>
                                        <p class="text-sm">Try adjusting your filters or date range.</p>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('livewire:init', function() {
            const chartData = @json($chartData);

            // Initialize Consumption Chart
            const consumptionCtx = document.getElementById('consumptionChart').getContext('2d');
            const consumptionChart = new Chart(consumptionCtx, {
                type: 'bar',
                data: chartData.consumption,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' L';
                                }
                            }
                        }
                    }
                }
            });

            // Initialize Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            const revenueChart = new Chart(revenueCtx, {
                type: 'bar',
                data: chartData.revenue,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'KES ' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // Initialize Efficiency Chart
            const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
            const efficiencyChart = new Chart(efficiencyCtx, {
                type: 'bar',
                data: chartData.efficiency,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2) + ' KES/L';
                                }
                            }
                        }
                    }
                }
            });

            // Update charts when Livewire component is updated
            Livewire.on('updateCharts', (newChartData) => {
                consumptionChart.data = newChartData.consumption;
                consumptionChart.update();

                revenueChart.data = newChartData.revenue;
                revenueChart.update();

                efficiencyChart.data = newChartData.efficiency;
                efficiencyChart.update();
            });
        });
    </script>
    @endpush
</div>