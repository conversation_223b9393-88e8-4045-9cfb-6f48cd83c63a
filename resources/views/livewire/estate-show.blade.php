<div>
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center gap-3">
            <a href="{{ $this->getEstatesRoute() }}" class="flex h-9 w-9 items-center justify-center rounded-full border border-gray-300 bg-white text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.05025 14.9497C6.75736 15.2426 6.28248 15.2426 5.98959 14.9497C5.6967 14.6568 5.6967 14.1819 5.98959 13.889L11.2196 8.65899L5.98959 3.42899C5.6967 3.1361 5.6967 2.66122 5.98959 2.36833C6.28248 2.07544 6.75736 2.07544 7.05025 2.36833L12.5109 7.82899C12.8038 8.12188 12.8038 8.59676 12.5109 8.88965L7.05025 14.3497V14.9497Z" transform="rotate(180 7.05025 8.65899)" fill=""></path>
                </svg>
            </a>
            <h2 class="text-2xl font-semibold text-gray-800 dark:text-white/90">{{ $estate->name }}</h2>
            <span class="rounded-full px-2 py-0.5 text-theme-xs font-medium {{ $estate->is_active ? 'bg-success-50 text-success-700 dark:bg-success-500/15 dark:text-success-500' : 'bg-error-50 text-error-700 dark:bg-error-500/15 dark:text-error-500' }}">
                {{ $estate->is_active ? 'Active' : 'Inactive' }}
            </span>
        </div>
        
        <div class="flex items-center gap-2">
            <a href="{{ $this->getEstateEditRoute($estate) }}" class="flex w-full items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200 sm:w-auto">
                <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M15.0911 2.78206C14.2125 1.90338 12.7878 1.90338 11.9092 2.78206L4.57524 10.116C4.26682 10.4244 4.0547 10.8158 3.96468 11.2426L3.31231 14.3352C3.25997 14.5833 3.33653 14.841 3.51583 15.0203C3.69512 15.1996 3.95286 15.2761 4.20096 15.2238L7.29355 14.5714C7.72031 14.4814 8.11172 14.2693 8.42013 13.9609L15.7541 6.62695C16.6327 5.74827 16.6327 4.32365 15.7541 3.44497L15.0911 2.78206ZM12.9698 3.84272C13.2627 3.54982 13.7376 3.54982 14.0305 3.84272L14.6934 4.50563C14.9863 4.79852 14.9863 5.2734 14.6934 5.56629L14.044 6.21573L12.3204 4.49215L12.9698 3.84272ZM11.2597 5.55281L5.6359 11.1766C5.53309 11.2794 5.46238 11.4099 5.43238 11.5522L5.01758 13.5185L6.98394 13.1037C7.1262 13.0737 7.25666 13.003 7.35947 12.9002L12.9833 7.27639L11.2597 5.55281Z" fill=""></path>
                </svg>
                Edit Estate
            </a>
            <a href="{{ $this->getHouseCreateRoute($estate->id) }}" class="flex w-full items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200 sm:w-auto">
                <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.75 2.25C9.75 1.83579 9.41421 1.5 9 1.5C8.58579 1.5 8.25 1.83579 8.25 2.25V8.25H2.25C1.83579 8.25 1.5 8.58579 1.5 9C1.5 9.41421 1.83579 9.75 2.25 9.75H8.25V15.75C8.25 16.1642 8.58579 16.5 9 16.5C9.41421 16.5 9.75 16.1642 9.75 15.75V9.75H15.75C16.1642 9.75 16.5 9.41421 16.5 9C16.5 8.58579 16.1642 8.25 15.75 8.25H9.75V2.25Z" fill=""></path>
                </svg>
                Add House
            </a>
        </div>
    </div>

    {{-- Tabs Navigation --}}
    <div class="border-b border-gray-200 dark:border-gray-800 mb-6">
        <nav class="-mb-px flex space-x-8 px-4 sm:px-6 lg:px-8" aria-label="Tabs">
            <a href="#" wire:click.prevent="$set('selectedTab', 'overview')"
               class="{{ $selectedTab === 'overview' ? 'border-brand-500 text-brand-600 dark:text-brand-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-700' }}
                   whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Overview
            </a>
            <a href="#" wire:click.prevent="$set('selectedTab', 'water-rates')"
               class="{{ $selectedTab === 'water-rates' ? 'border-brand-500 text-brand-600 dark:text-brand-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-700' }}
                   whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Water Rates
            </a>
        </nav>
    </div>

    <div class="mx-auto max-w-7xl p-4 md:p-6">
        @if ($selectedTab === 'overview')
            {{-- Estate Overview --}}
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {{-- Estate Details --}}
                <div class="lg:col-span-2">
                    <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                        <div class="mb-5 sm:mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Estate Information</h3>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Estate Code</p>
                                    <p class="rounded-full border border-gray-200 bg-gray-50 px-2 py-0.5 text-theme-xs font-medium text-gray-700 dark:border-gray-700 dark:bg-white/[0.05] dark:text-gray-400">{{ $estate->code }}</p>
                                </div>
                                
                                @if($estate->description)
                                    <div class="sm:col-span-2">
                                        <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Description</p>
                                        <p class="text-sm font-medium text-gray-800 dark:text-white/90">{{ $estate->description }}</p>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="border-t border-gray-100 pt-4 dark:border-gray-800">
                                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Address</p>
                                <div class="text-sm font-medium text-gray-800 dark:text-white/90">
                                    @if($estate->address)
                                        <div>{{ $estate->address }}</div>
                                    @endif
                                    @if($estate->city || $estate->state)
                                        <div class="text-gray-500 dark:text-gray-400">
                                            {{ collect([$estate->city, $estate->state])->filter()->join(', ') }}
                                            @if($estate->postal_code)
                                                {{ $estate->postal_code }}
                                            @endif
                                        </div>
                                    @endif
                                    @if($estate->country)
                                        <div class="text-gray-500 dark:text-gray-400">{{ $estate->country }}</div>
                                    @endif
                                </div>
                            </div>
                            
                            @if($estate->manager_name)
                                <div class="border-t border-gray-100 pt-4 dark:border-gray-800">
                                    <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Estate Manager</p>
                                    <div class="text-sm font-medium text-gray-800 dark:text-white/90">
                                        <div class="font-medium">{{ $estate->manager_name }}</div>
                                        @if($estate->manager_phone)
                                            <div class="text-gray-500 dark:text-gray-400">{{ $estate->manager_phone }}</div>
                                        @endif
                                        @if($estate->manager_email)
                                            <div class="text-gray-500 dark:text-gray-400">{{ $estate->manager_email }}</div>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                
                {{-- Statistics --}}
                <div>
                    <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                        <div class="mb-5 sm:mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Statistics</h3>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-brand-600 dark:text-brand-400">{{ $stats['total_houses'] }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Total Houses</div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4 text-center">
                                <div>
                                    <div class="text-xl font-semibold text-success-600 dark:text-success-400">{{ $stats['occupied_houses'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Occupied</div>
                                </div>
                                <div>
                                    <div class="text-xl font-semibold text-gray-600 dark:text-gray-400">{{ $stats['vacant_houses'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Vacant</div>
                                </div>
                            </div>
                            
                            <div class="border-t border-gray-100 pt-4 text-center dark:border-gray-800">
                                <div class="text-lg font-semibold text-gray-800 dark:text-white/90">{{ $stats['occupancy_rate'] }}%</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Occupancy Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Assigned Staff --}}
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {{-- Managers --}}
                <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                    <div class="mb-5 sm:mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Assigned Managers</h3>
                    </div>
                    
                    @if($assignedManagers->count() > 0)
                        <div class="space-y-3">
                            @foreach($assignedManagers as $manager)
                                <div class="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-200 rounded-full flex items-center justify-center">
                                            <span class="text-blue-700 text-sm font-medium">{{ strtoupper(substr($manager->name, 0, 1)) }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $manager->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $manager->email }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <div class="text-sm mb-3">No managers assigned</div>
                            <button wire:click="openAssignManagerModal" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">
                                Assign Manager
                            </button>
                        </div>
                    @endif
                </div>
                
                {{-- Reviewers --}}
                <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                    <div class="mb-5 sm:mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Assigned Reviewers</h3>
                    </div>
                    
                    @if($assignedReviewers->count() > 0)
                        <div class="space-y-3">
                            @foreach($assignedReviewers as $reviewer)
                                <div class="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-green-200 rounded-full flex items-center justify-center">
                                            <span class="text-green-700 text-sm font-medium">{{ strtoupper(substr($reviewer->name, 0, 1)) }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $reviewer->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $reviewer->email }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <div class="text-sm mb-3">No reviewers assigned</div>
                            <button wire:click="openAssignReviewerModal" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-green-500 dark:hover:bg-green-600">
                                Assign Reviewer
                            </button>
                        </div>
                    @endif
                </div>
                
                {{-- Caretakers --}}
                <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                    <div class="mb-5 sm:mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Assigned Caretakers</h3>
                    </div>
                    
                    @if($assignedCaretakers->count() > 0)
                        <div class="space-y-3">
                            @foreach($assignedCaretakers as $caretaker)
                                <div class="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center">
                                            <span class="text-yellow-700 text-sm font-medium">{{ strtoupper(substr($caretaker->name, 0, 1)) }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $caretaker->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $caretaker->email }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <div class="text-sm mb-3">No caretakers assigned</div>
                            <button wire:click="openAssignCaretakerModal" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 dark:bg-yellow-500 dark:hover:bg-yellow-600">
                                Assign Caretaker
                            </button>
                        </div>
                    @endif
                </div>
            </div>
            
            {{-- Recent Houses --}}
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                <div class="mb-5 sm:mb-6 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Recent Houses</h3>
                    <a href="{{ $this->getHousesRoute($estate->id) }}" class="flex w-full items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200 sm:w-auto">
                        View All Houses
                    </a>
                </div>
                
                @if($recentHouses->count() > 0)
                    <div class="max-w-full overflow-x-auto">
                        <table class="min-w-full">
                            <thead>
                                <tr class="border-b border-gray-100 dark:border-gray-800">
                                    <th class="px-5 py-3 sm:px-6">
                                        <div class="flex items-center">
                                            <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">House Number</p>
                                        </div>
                                    </th>
                                    <th class="px-5 py-3 sm:px-6">
                                        <div class="flex items-center">
                                            <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">Meter Number</p>
                                        </div>
                                    </th>
                                    <th class="px-5 py-3 sm:px-6">
                                        <div class="flex items-center">
                                            <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">Contacts</p>
                                        </div>
                                    </th>
                                    <th class="px-5 py-3 sm:px-6">
                                        <div class="flex items-center">
                                            <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">Status</p>
                                        </div>
                                    </th>
                                    <th class="px-5 py-3 sm:px-6">
                                        <div class="flex items-center">
                                            <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">Actions</p>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-100 dark:divide-gray-800">
                                @foreach($recentHouses as $house)
                                    <tr>
                                        <td class="px-5 py-4 sm:px-6">
                                            <p class="text-gray-800 text-theme-sm dark:text-white/90">{{ $house->house_number }}</p>
                                        </td>
                                        
                                        <td class="px-5 py-4 sm:px-6">
                                            @if($house->meter_number)
                                                <p class="rounded-full border border-gray-200 bg-gray-50 px-2 py-0.5 text-theme-xs font-medium text-gray-700 dark:border-gray-700 dark:bg-white/[0.05] dark:text-gray-400">{{ $house->meter_number }}</p>
                                            @else
                                                <span class="text-gray-400 dark:text-gray-500">No meter</span>
                                            @endif
                                        </td>
                                        
                                        <td class="px-5 py-4 sm:px-6">
                                            @if($house->contacts->count() > 0)
                                                <div class="text-sm text-gray-800 dark:text-white/90">
                                                    {{ $house->contacts->first()->name }}
                                                    @if($house->contacts->count() > 1)
                                                        <span class="text-gray-500 dark:text-gray-400">+{{ $house->contacts->count() - 1 }} more</span>
                                                    @endif
                                                </div>
                                            @else
                                                <span class="text-gray-400 dark:text-gray-500">No contacts</span>
                                            @endif
                                        </td>
                                        
                                        <td class="px-5 py-4 sm:px-6">
                                            <p class="rounded-full px-2 py-0.5 text-theme-xs font-medium {{ $house->contacts->count() > 0 ? 'bg-success-50 text-success-700 dark:bg-success-500/15 dark:text-success-500' : 'bg-warning-50 text-warning-700 dark:bg-warning-500/15 dark:text-warning-400' }}">
                                                {{ $house->contacts->count() > 0 ? 'Occupied' : 'Vacant' }}
                                            </p>
                                        </td>
                                        
                                        <td class="px-5 py-4 sm:px-6 text-right">
                                            <a href="{{ $this->getHouseShowRoute($house) }}" class="flex h-9 w-9 items-center justify-center rounded-full border border-gray-300 bg-white text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                                                <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M15.0911 2.78206C14.2125 1.90338 12.7878 1.90338 11.9092 2.78206L4.57524 10.116C4.26682 10.4244 4.0547 10.8158 3.96468 11.2426L3.31231 14.3352C3.25997 14.5833 3.33653 14.841 3.51583 15.0203C3.69512 15.1996 3.95286 15.2761 4.20096 15.2238L7.29355 14.5714C7.72031 14.4814 8.11172 14.2693 8.42013 13.9609L15.7541 6.62695C16.6327 5.74827 16.6327 4.32365 15.7541 3.44497L15.0911 2.78206ZM12.9698 3.84272C13.2627 3.54982 13.7376 3.54982 14.0305 3.84272L14.6934 4.50563C14.9863 4.79852 14.9863 5.2734 14.6934 5.56629L14.044 6.21573L12.3204 4.49215L12.9698 3.84272ZM11.2597 5.55281L5.6359 11.1766C5.53309 11.2794 5.46238 11.4099 5.43238 11.5522L5.01758 13.5185L6.98394 13.1037C7.1262 13.0737 7.25666 13.003 7.35947 12.9002L12.9833 7.27639L11.2597 5.55281Z" fill=""></path>
                                                </svg>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        <div class="text-sm mb-4">No houses found.</div>
                        <a href="{{ $this->getHouseCreateRoute($estate->id) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 dark:bg-brand-500 dark:hover:bg-brand-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add First House
                        </a>
                    </div>
                @endif
            </div>
        @elseif ($selectedTab === 'water-rates')
            <livewire:estate.water-rate-list :estate="$estate" />
        @endif
    </div>

    <!-- Assignment Modals -->
    <x-assignment-modal
        :show="$showAssignManagerModal"
        title="Assign Managers to {{ $estate->name }}"
        :users="collect($availableManagers)"
        :selectedUserIds="$selectedManagerIds"
        selectedProperty="selectedManagerIds"
        roleColor="blue"
        onAssign="assignManagers"
        onClose="closeAssignManagerModal"
        emptyMessage="No available managers to assign."
        name="assign-manager-modal"
    />

    <x-assignment-modal
        :show="$showAssignReviewerModal"
        title="Assign Reviewers to {{ $estate->name }}"
        :users="collect($availableReviewers)"
        :selectedUserIds="$selectedReviewerIds"
        selectedProperty="selectedReviewerIds"
        roleColor="green"
        onAssign="assignReviewers"
        onClose="closeAssignReviewerModal"
        emptyMessage="No available reviewers to assign."
        name="assign-reviewer-modal"
    />

    <x-assignment-modal
        :show="$showAssignCaretakerModal"
        title="Assign Caretakers to {{ $estate->name }}"
        :users="collect($availableCaretakers)"
        :selectedUserIds="$selectedCaretakerIds"
        selectedProperty="selectedCaretakerIds"
        roleColor="yellow"
        onAssign="assignCaretakers"
        onClose="closeAssignCaretakerModal"
        emptyMessage="No available caretakers to assign."
        name="assign-caretaker-modal"
    />
</div>
