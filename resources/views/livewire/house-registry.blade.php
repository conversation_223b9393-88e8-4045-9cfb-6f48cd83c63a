<div>
    <!-- <PERSON>er -->
    <div class="mb-6">
        <div class="header-responsive">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">House Registry</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Manage all houses in the system
                </p>
            </div>
            <x-button wire:click="openCreateModal" variant="primary">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add House
            </x-button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid mb-6">
        <div class="card">
            <div class="card-header pb-2">
                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Houses</div>
            </div>
            <div class="card-body pt-2">
                <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $totalHouses }}</div>
            </div>
        </div>
        <div class="card">
            <div class="card-header pb-2">
                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Occupied Houses</div>
            </div>
            <div class="card-body pt-2">
                <div class="text-2xl font-semibold text-green-600 dark:text-green-400">{{ $occupiedHouses }}</div>
            </div>
        </div>
        <div class="card">
            <div class="card-header pb-2">
                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Vacant Houses</div>
            </div>
            <div class="card-body pt-2">
                <div class="text-2xl font-semibold text-yellow-600 dark:text-yellow-400">{{ $vacantHouses }}</div>
            </div>
        </div>
        <div class="card">
            <div class="card-header pb-2">
                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">With Meters</div>
            </div>
            <div class="card-body pt-2">
                <div class="text-2xl font-semibold text-blue-600 dark:text-blue-400">{{ $housesWithMeters }}</div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-6">
        <div class="card-body">
            <div class="form-grid">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Houses</label>
                <input type="text" wire:model.live="search" placeholder="Search by house number or address..." 
                       class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Estate</label>
                <select wire:model="estateFilter" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="">All Estates</option>
                    @foreach ($estates as $estate)
                        <option value="{{ $estate->id }}">{{ $estate->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Occupancy</label>
                <select wire:model="occupancyFilter" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="">All Status</option>
                    <option value="occupied">Occupied</option>
                    <option value="vacant">Vacant</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sort By</label>
                <select wire:model="sortBy" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="house_number">House Number</option>
                    <option value="estate_id">Estate</option>
                    <option value="created_at">Created Date</option>
                    <option value="updated_at">Last Updated</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Houses Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">House Number>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estate</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Meter Number>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contacts>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach ($houses as $house)
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ $house->house_number }}</div>
                        @if($house->address)
                            <div class="text-sm text-gray-500">{{ $house->address }}</div>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ $house->estate->name }}</div>
                        <div class="text-sm text-gray-500">{{ $house->estate->city }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        @if($house->meter_number)
                            <div class="text-sm text-gray-900 font-mono">{{ $house->meter_number }}</div>
                        @else
                            <span class="text-sm text-gray-400">No meter</span>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        @if($house->contacts->count() > 0)
                            <div class="text-sm text-gray-900">
                                {{ $house->contacts->first()->name }}
                                @if($house->contacts->count() > 1)
                                    <span class="text-gray-500">+{{ $house->contacts->count() - 1 }} more</span>
                                @endif
                            </div>
                            @if($house->contacts->first()->phone)
                                <div class="text-sm text-gray-500">{{ $house->contacts->first()->phone }}</div>
                            @endif
                        @else
                            <span class="text-sm text-gray-400">No contacts</span>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $house->contacts->count() > 0 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ $house->contacts->count() > 0 ? 'Occupied' : 'Vacant' }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                            <a href="{{ $this->getHouseShowRoute($house) }}" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                View Details
                            </a>
                            <button wire:click="openEditModal({{ $house->id }})" class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-2">
                            Edit
                        </button>
                        <button wire:click="confirmDelete({{ $house->id }})" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:text-red-200 dark:bg-red-900/50 dark:hover:bg-red-900/70">
                            Delete
                        </button>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="mt-4">
        {{ $houses->links() }}
    </div>

    <!-- Create Modal -->
    <x-form-modal
        :show="$showCreateModal"
        title="Create New House"
        onSave="save"
        onClose="$set('showCreateModal', false)"
        name="house-create-modal"
    >
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">House Number <span class="text-red-500">*</span></label>
                <input type="text" wire:model="form.house_number" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Estate <span class="text-red-500">*</span></label>
                <select wire:model="form.estate_id" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                    <option value="">Select Estate</option>
                    @foreach ($estates as $estate)
                        <option value="{{ $estate->id }}">{{ $estate->name }} ({{ $estate->city }})</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meter Number</label>
                <input type="text" wire:model="form.meter_number" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address</label>
                <textarea wire:model="form.address" rows="3" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
            </div>
        </div>
    </x-form-modal>

    <!-- Edit Modal -->
    <x-form-modal
        :show="$showEditModal"
        title="Edit House"
        onSave="save"
        onClose="$set('showEditModal', false)"
        saveText="Update"
        name="house-edit-modal"
    >
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">House Number <span class="text-red-500">*</span></label>
                <input type="text" wire:model="form.house_number" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Estate <span class="text-red-500">*</span></label>
                <select wire:model="form.estate_id" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                    <option value="">Select Estate</option>
                    @foreach ($estates as $estate)
                        <option value="{{ $estate->id }}">{{ $estate->name }} ({{ $estate->city }})</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meter Number</label>
                <input type="text" wire:model="form.meter_number" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address</label>
                <textarea wire:model="form.address" rows="3" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
            </div>
        </div>
    </x-form-modal>

    <!-- Delete Confirmation Modal -->
    <x-confirmation-modal
        :show="$showDeleteModal"
        title="Confirm Delete"
        message="Are you sure you want to delete this house? This action cannot be undone."
        confirmText="Delete"
        confirmColor="red"
        onConfirm="delete"
        onCancel="$set('showDeleteModal', false)"
        name="house-delete-modal"
    />
</div>
