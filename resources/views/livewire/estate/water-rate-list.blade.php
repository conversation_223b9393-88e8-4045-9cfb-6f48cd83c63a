<div>
    <div class="mb-5 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Water Rates for {{ $estate->name }}</h3>
        <button
            wire:click="$dispatch('openWaterRateForm')"
            class="flex items-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200"
        >
            <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M9.75 2.25C9.75 1.83579 9.41421 1.5 9 1.5C8.58579 1.5 8.25 1.83579 8.25 2.25V8.25H2.25C1.83579 8.25 1.5 8.58579 1.5 9C1.5 9.41421 1.83579 9.75 2.25 9.75H8.25V15.75C8.25 16.1642 8.58579 16.5 9 16.5C9.41421 16.5 9.75 16.1642 9.75 15.75V9.75H15.75C16.1642 9.75 16.5 9.41421 16.5 9C16.5 8.58579 16.1642 8.25 15.75 8.25H9.75V2.25Z" fill=""></path>
            </svg>
            Add Water Rate
        </button>
    </div>

    <div class="mt-4">
        @if ($waterRates->isEmpty())
            <p class="text-gray-600 dark:text-gray-400">No water rates defined for this estate yet.</p>
        @else
            <div class="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]">
                <div class="max-w-full overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-100 dark:border-gray-800">
                                <th class="px-5 py-3 sm:px-6">
                                    <div class="flex items-center">
                                        <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                                            Name
                                        </p>
                                    </div>
                                </th>
                                <th class="px-5 py-3 sm:px-6">
                                    <div class="flex items-center">
                                        <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                                            Rate per Unit
                                        </p>
                                    </div>
                                </th>
                                <th class="px-5 py-3 sm:px-6">
                                    <div class="flex items-center">
                                        <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                                            Effective From
                                        </p>
                                    </div>
                                </th>
                                <th class="px-5 py-3 sm:px-6">
                                    <div class="flex items-center">
                                        <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                                            Effective To
                                        </p>
                                    </div>
                                </th>
                                <th class="px-5 py-3 sm:px-6">
                                    <div class="flex items-center">
                                        <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                                            Status
                                        </p>
                                    </div>
                                </th>
                                <th class="px-5 py-3 sm:px-6">
                                    <div class="flex items-center">
                                        <p class="font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                                            Actions
                                        </p>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-100 dark:divide-gray-800">
                            @foreach ($waterRates as $rate)
                                <tr>
                                    <td class="px-5 py-4 sm:px-6">
                                        <p class="text-gray-800 text-theme-sm dark:text-white/90">
                                            {{ $rate->name }}
                                        </p>
                                    </td>
                                    <td class="px-5 py-4 sm:px-6">
                                        <p class="text-gray-500 text-theme-sm dark:text-gray-400">
                                            {{ number_format($rate->rate_per_unit, 2) }}
                                        </p>
                                    </td>
                                    <td class="px-5 py-4 sm:px-6">
                                        <p class="text-gray-500 text-theme-sm dark:text-gray-400">
                                            {{ $rate->effective_from->format('M d, Y') }}
                                        </p>
                                    </td>
                                    <td class="px-5 py-4 sm:px-6">
                                        <p class="text-gray-500 text-theme-sm dark:text-gray-400">
                                            {{ $rate->effective_to ? $rate->effective_to->format('M d, Y') : 'N/A' }}
                                        </p>
                                    </td>
                                    <td class="px-5 py-4 sm:px-6">
                                        <p class="rounded-full px-2 py-0.5 text-theme-xs font-medium {{ $rate->is_active ? 'bg-success-50 text-success-700 dark:bg-success-500/15 dark:text-success-500' : 'bg-error-50 text-error-700 dark:bg-error-500/15 dark:text-error-500' }}">
                                            {{ $rate->is_active ? 'Active' : 'Inactive' }}
                                        </p>
                                    </td>
                                    <td class="px-5 py-4 sm:px-6 text-right">
                                        <div class="flex items-center justify-end gap-3">
                                            <button
                                                wire:click="$dispatch('openWaterRateForm', { waterRate: {{ $rate->id }} })"
                                                class="flex h-9 w-9 items-center justify-center rounded-full border border-gray-300 bg-white text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200"
                                            >
                                                <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M15.0911 2.78206C14.2125 1.90338 12.7878 1.90338 11.9092 2.78206L4.57524 10.116C4.26682 10.4244 4.0547 10.8158 3.96468 11.2426L3.31231 14.3352C3.25997 14.5833 3.33653 14.841 3.51583 15.0203C3.69512 15.1996 3.95286 15.2761 4.20096 15.2238L7.29355 14.5714C7.72031 14.4814 8.11172 14.2693 8.42013 13.9609L15.7541 6.62695C16.6327 5.74827 16.6327 4.32365 15.7541 3.44497L15.0911 2.78206ZM12.9698 3.84272C13.2627 3.54982 13.7376 3.54982 14.0305 3.84272L14.6934 4.50563C14.9863 4.79852 14.9863 5.2734 14.6934 5.56629L14.044 6.21573L12.3204 4.49215L12.9698 3.84272ZM11.2597 5.55281L5.6359 11.1766C5.53309 11.2794 5.46238 11.4099 5.43238 11.5522L5.01758 13.5185L6.98394 13.1037C7.1262 13.0737 7.25666 13.003 7.35947 12.9002L12.9833 7.27639L11.2597 5.55281Z" fill=""></path>
                                                </svg>
                                            </button>
                                            <button
                                                wire:click="deleteWaterRate({{ $rate->id }})"
                                                wire:confirm="Are you sure you want to delete this water rate?"
                                                class="flex h-9 w-9 items-center justify-center rounded-full border border-gray-300 bg-white text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200"
                                            >
                                                <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.20096 3.51583C3.95286 3.33653 3.69512 3.25997 3.44702 3.31231L0.354431 3.96468C-0.072331 4.0547 -0.284451 4.26682 -0.374471 4.57524C-0.464491 4.88365 -0.393781 5.17411 -0.100881 5.46701L6.62695 12.1931C7.50563 13.0717 8.93025 13.0717 9.80893 12.1931L16.535 5.46701C16.8279 5.17411 16.8986 4.88365 16.8086 4.57524C16.7186 4.26682 16.5065 4.0547 16.0797 3.96468L12.9871 3.31231C12.739 3.25997 12.4813 3.33653 12.302 3.51583L5.5759 10.2419C5.47309 10.3447 5.34263 10.4154 5.20037 10.4454L3.23401 10.8602L3.64879 8.89384C3.67879 8.75158 3.7495 8.62112 3.85231 8.51831L10.5784 1.79221C10.8713 1.49932 10.8713 1.02444 10.5784 0.73155C10.2855 0.43865 9.81062 0.43865 9.51773 0.73155L2.79163 7.45765L4.20096 3.51583Z" fill=""></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="mt-4">
                {{ $waterRates->links() }}
            </div>
        @endif
    </div>

    {{-- Include the Water Rate Form Component --}}
    <livewire:estate.water-rate-form :estate="$estate" />
</div>
