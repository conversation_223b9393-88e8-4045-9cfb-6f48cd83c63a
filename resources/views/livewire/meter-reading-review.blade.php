<div>
    <div class="max-w-7xl mx-auto p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Meter Reading Review</h1>
            <div class="flex space-x-2">
                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                    {{ $stats['pending'] }} Pending
                </span>
                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                    {{ $stats['approved'] }} Approved
                </span>
                <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">
                    {{ $stats['rejected'] }} Rejected
                </span>
            </div>
        </div>

        @if (session()->has('message'))
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
                {{ session('message') }}
            </div>
        @endif

        <!-- Filters -->
        <div class="bg-white shadow rounded-lg p-4 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select wire:model.live="filterStatus" class="w-full rounded-md border-gray-300">
                        <option value="all">All</option>
                        <option value="submitted">Submitted</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Estate</label>
                    <select wire:model.live="filterEstate" class="w-full rounded-md border-gray-300">
                        <option value="all">All Estates</option>
                        @foreach($estates as $estate)
                            <option value="{{ $estate->id }}">{{ $estate->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                    <input type="date" wire:model.live="filterDateFrom" class="w-full rounded-md border-gray-300">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                    <input type="date" wire:model.live="filterDateTo" class="w-full rounded-md border-gray-300">
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Readings List -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Readings</h3>
                        
                        <div class="space-y-4">
                            @forelse($readings as $reading)
                                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer {{ $selectedReading && $selectedReading->id === $reading->id ? 'ring-2 ring-blue-500' : '' }}"
                                     wire:click="selectReading({{ $reading->id }})">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2">
                                                <h4 class="font-medium text-gray-900">{{ $reading->house->house_number }}</h4>
                                                <span class="px-2 py-1 text-xs rounded-full bg-{{ $reading->status_color }}-100 text-{{ $reading->status_color }}-800">
                                                    {{ $reading->status_label }}
                                                </span>
                                            </div>
                                            <p class="text-sm text-gray-600">{{ $reading->house->estate->name }}</p>
                                            <p class="text-sm text-gray-500">By {{ $reading->user->name }} on {{ $reading->reading_date->format('M d, Y') }}</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-medium">{{ number_format($reading->consumption, 2) }} units</p>
                                            <p class="text-sm text-gray-500">{{ $reading->previous_reading }} → {{ $reading->current_reading }}</p>
                                        </div>
                                    </div>
                                    
                                    @if($reading->notes)
                                        <p class="text-sm text-gray-600 mt-2">{{ $reading->notes }}</p>
                                    @endif
                                    
                                    @if($reading->photo_path)
                                        <div class="mt-2">
                                            <img src="{{ Storage::url($reading->photo_path) }}" alt="Meter reading photo" class="h-20 rounded">
                                        </div>
                                    @endif
                                </div>
                            @empty
                                <p class="text-gray-500 text-center py-8">No readings found</p>
                            @endforelse
                        </div>

                        <div class="mt-4">
                            {{ $readings->links() }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Review Panel -->
            <div class="lg:col-span-1">
                @if($selectedReading)
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Review Reading</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">House</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $selectedReading->house->house_number }} - {{ $selectedReading->house->estate->name }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Submitted By</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $selectedReading->user->name }}</p>
                                    <p class="text-sm text-gray-500">{{ $selectedReading->reading_date->format('M d, Y H:i') }}</p>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-2 text-center">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Previous</label>
                                        <p class="text-lg font-medium">{{ number_format($selectedReading->previous_reading, 2) }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Current</label>
                                        <p class="text-lg font-medium">{{ number_format($selectedReading->current_reading, 2) }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Consumption</label>
                                        <p class="text-lg font-medium text-blue-600">{{ number_format($selectedReading->consumption, 2) }}</p>
                                    </div>
                                </div>
                                
                                @if($selectedReading->notes)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Notes</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $selectedReading->notes }}</p>
                                    </div>
                                @endif
                                
                                @if($selectedReading->photo_path)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Photo</label>
                                        <img src="{{ Storage::url($selectedReading->photo_path) }}" alt="Meter reading photo" class="mt-1 rounded-lg max-w-full h-auto">
                                    </div>
                                @endif
                                
                                <form wire:submit.prevent="{{ $status === 'approved' ? 'approveReading' : 'rejectReading' }}" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Action</label>
                                        <select wire:model="status" class="mt-1 w-full rounded-md border-gray-300">
                                            <option value="">Select action</option>
                                            <option value="approved">Approve</option>
                                            <option value="rejected">Reject</option>
                                        </select>
                                        @error('status') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Review Notes</label>
                                        <textarea wire:model="reviewNotes" rows="3" 
                                                  class="mt-1 w-full rounded-md border-gray-300"
                                                  placeholder="Add your review notes..."></textarea>
                                        @error('reviewNotes') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                                    </div>
                                    
                                    <div class="flex space-x-3">
                                        <button type="button" wire:click="$set('selectedReading', null)" 
                                                class="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                            Cancel
                                        </button>
                                        <button type="submit" 
                                                class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                                {{ !$status ? 'disabled' : '' }}>
                                            Submit Review
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="bg-white shadow rounded-lg p-6">
                        <p class="text-gray-500 text-center">Select a reading to review</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>