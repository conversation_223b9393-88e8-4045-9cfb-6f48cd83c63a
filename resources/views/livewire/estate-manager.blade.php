<div>
    <!-- <PERSON>er -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Estates</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Manage all estates in the system
                </p>
            </div>
            <button wire:click="openCreateModal" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Estate
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-sm font-medium text-gray-500">Total Estates</div>
            <div class="mt-1 text-2xl font-semibold text-gray-900">{{ $totalEstates }}</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-sm font-medium text-gray-500">Active Estates</div>
            <div class="mt-1 text-2xl font-semibold text-green-600">{{ $activeEstates }}</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-sm font-medium text-gray-500">Inactive Estates</div>
            <div class="mt-1 text-2xl font-semibold text-red-600">{{ $inactiveEstates }}</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-sm font-medium text-gray-500">Total Houses</div>
            <div class="mt-1 text-2xl font-semibold text-blue-600">{{ \App\Models\House::count() }}</div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Estates</label>
                <input type="text" wire:model.live="search" placeholder="Search by name or code..." 
                       class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                <select wire:model="statusFilter" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sort By</label>
                <select wire:model="sortBy" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="name">Name</option>
                    <option value="code">Code</option>
                    <option value="city">City</option>
                    <option value="created_at">Created Date</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Estates Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Houses>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach ($estates as $estate)
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ $estate->name }}</div>
                        @if($estate->contact_email)
                            <div class="text-sm text-gray-500">{{ $estate->contact_email }}</div>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 font-mono">{{ $estate->code }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ $estate->city }}</div>
                        @if($estate->state)
                            <div class="text-sm text-gray-500">{{ $estate->state }}</div>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            <span class="font-semibold">{{ $estate->houses_count ?? $estate->houses()->count() }}</span> houses
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ $estate->occupied_houses ?? $estate->houses()->whereHas('contacts')->count() }} occupied
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $estate->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $estate->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                            <a href="{{ $this->getEstateShowRoute($estate) }}" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                View Details
                            </a>
                            <a href="{{ $this->getHousesRoute($estate->id) }}" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                Houses ({{ $estate->houses_count ?? $estate->houses()->count() }})
                            </a>
                            <button wire:click="openEditModal({{ $estate->id }})" class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-2">
                            Edit
                        </button>
                        <button wire:click="confirmDelete({{ $estate->id }})" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:text-red-200 dark:bg-red-900/50 dark:hover:bg-red-900/70">
                            Delete
                        </button>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="mt-4">
        {{ $estates->links() }}
    </div>



    <div class="mt-4">
        {{ $estates->links() }}
    </div>

    <!-- Create Modal -->
    <x-form-modal
        :show="$showCreateModal"
        title="Create New Estate"
        onSave="save"
        onClose="$set('showCreateModal', false)"
        maxWidth="2xl"
        name="estate-create-modal"
    >
        <x-estate-form :form="$form" />
    </x-form-modal>

    <!-- Edit Modal -->
    <x-form-modal
        :show="$showEditModal"
        title="Edit Estate"
        onSave="save"
        onClose="$set('showEditModal', false)"
        saveText="Update"
        maxWidth="2xl"
        name="estate-edit-modal"
    >
        <x-estate-form :form="$form" />
    </x-form-modal>

    <!-- Delete Confirmation Modal -->
    <x-confirmation-modal
        :show="$showDeleteModal"
        title="Confirm Delete"
        message="Are you sure you want to delete this estate? This action cannot be undone."
        confirmText="Delete"
        confirmColor="red"
        onConfirm="delete"
        onCancel="$set('showDeleteModal', false)"
        name="estate-delete-modal"
    />

    <!-- View Modal -->
    <x-ui.modal :show="$showModal" title="Estate Details" name="estate-view-modal" maxWidth="lg">
        @if($estateIdToShow)
            @php
                $estate = \App\Models\Estate::find($estateIdToShow);
            @endphp
            <div class="space-y-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</h4>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $estate->name }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Code</h4>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $estate->code }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</h4>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $estate->address ?? 'N/A' }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">City</h4>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $estate->city ?? 'N/A' }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">State/Province</h4>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $estate->state ?? 'N/A' }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Postal Code</h4>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $estate->postal_code ?? 'N/A' }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Country</h4>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $estate->country ?? 'N/A' }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Contact Email</h4>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $estate->contact_email ?? 'N/A' }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Contact Phone</h4>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $estate->contact_phone ?? 'N/A' }}</p>
                </div>
            </div>
        @endif

        <x-slot name="footer">
            <button wire:click="$set('showModal', false)" type="button" class="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-600 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm">
                Close
            </button>
        </x-slot>
    </x-ui.modal>
</div>
