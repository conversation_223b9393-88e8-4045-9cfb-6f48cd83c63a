<div>
    <div class="max-w-4xl mx-auto p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">
            {{ $isEditMode ? 'Edit Meter Reading' : 'Enter Meter Reading' }}
        </h1>

        @if (session()->has('message'))
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
                {{ session('message') }}
            </div>
        @endif

        <form wire:submit.prevent="saveReading" class="bg-white shadow rounded-lg p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Estate Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Estate</label>
                    <select wire:model.live="estateId" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Select Estate</option>
                        @foreach($estates as $estate)
                            <option value="{{ $estate->id }}">{{ $estate->name }}</option>
                        @endforeach
                    </select>
                    @error('estateId') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- House Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">House</label>
                    <select wire:model.live="houseId" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" :disabled="!$estateId">
                        <option value="">Select House</option>
                        @foreach($houses as $house)
                            <option value="{{ $house->id }}">{{ $house->house_number }} - {{ $house->address }}</option>
                        @endforeach
                    </select>
                    @error('houseId') 
                        <span class="text-red-600 text-sm">{{ $message }}</span> 
                    @enderror
                </div>
            </div>

            @if($houseDetails)
                <div class="mt-6 bg-gray-50 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">House Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="font-medium">House Number:</span>
                            <span class="ml-2">{{ $houseDetails->house_number }}</span>
                        </div>
                        <div>
                            <span class="font-medium">Meter Number:</span>
                            <span class="ml-2">{{ $houseDetails->meter_number }}</span>
                        </div>
                        <div>
                            <span class="font-medium">Previous Reading:</span>
                            <span class="ml-2">{{ $previousReading }}</span>
                        </div>
                    </div>
                </div>
            @endif

            @if($houseId)
                <div class="mt-6 space-y-6">
                    <!-- Current Reading -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Reading</label>
                        <input type="number" wire:model="currentReading" 
                               class="w-full rounded-md border-gray-300" 
                               step="0.01" 
                               min="{{ $previousReading + 0.01 }}">
                        @error('currentReading') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        @if($currentReading && $previousReading)
                            <p class="text-sm text-gray-600 mt-1">
                                Consumption: {{ number_format($currentReading - $previousReading, 2) }} units
                            </p>
                        @endif
                    </div>

                    <!-- Photo Upload -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Photo (Optional)</label>
                        <input type="file" wire:model="photo" accept="image/*" class="w-full">
                        @error('photo') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        @if($photo)
                            <img src="{{ $photo->temporaryUrl() }}" class="mt-2 h-32 rounded">
                        @endif
                    </div>

                    <!-- Notes -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                        <textarea wire:model="notes" rows="3" 
                                  class="w-full rounded-md border-gray-300"
                                  placeholder="Any additional notes about the reading..."></textarea>
                        @error('notes') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <!-- Hidden location fields -->
                    <input type="hidden" wire:model="latitude">
                    <input type="hidden" wire:model="longitude">
                    <input type="hidden" wire:model="accuracy">

                    <div class="flex justify-end space-x-3">
                        <button type="button" wire:click="reset(['currentReading', 'photo', 'notes'])" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Reset
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            {{ $isEditMode ? 'Update Reading' : 'Save as Draft' }}
                        </button>
                    </div>
                </div>
            @endif
        </form>

        <!-- Draft Readings -->
        @if($this->draftReadings->count() > 0)
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Draft Readings</h2>
                <div class="space-y-4">
                    @foreach($this->draftReadings as $reading)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="font-medium">{{ $reading->house->house_number }} - {{ $reading->house->estate->name }}</h3>
                                    <p class="text-sm text-gray-600">
                                        Previous: {{ $reading->previous_reading }} | 
                                        Current: {{ $reading->current_reading }} | 
                                        Consumption: {{ $reading->consumption }}
                                    </p>
                                    @if($reading->notes)
                                        <p class="text-sm text-gray-500 mt-1">{{ $reading->notes }}</p>
                                    @endif
                                </div>
                                <div class="flex space-x-2">
                                    <button wire:click="submitForReview({{ $reading->id }})" 
                                            class="text-sm bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700">
                                        Submit
                                    </button>
                                    <button wire:click="deleteDraft({{ $reading->id }})" 
                                            wire:confirm="Are you sure you want to delete this draft?"
                                            class="text-sm bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>

    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('detect-location', () => {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            this.setLocation(
                                position.coords.latitude,
                                position.coords.longitude,
                                position.coords.accuracy
                            );
                        },
                        (error) => {
                            console.log('Location access denied or unavailable');
                        }
                    );
                }
            });
        });
    </script>
</div>