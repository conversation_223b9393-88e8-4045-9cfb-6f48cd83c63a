<div>
    <!-- House Selection -->
    @if (count($userHouses) > 1)
        <div class="mb-6">
            <label for="house-select" class="block text-sm font-medium text-gray-700 mb-2">Select House</label>
            <select id="house-select" wire:model.live="selectedHouseId" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                @foreach ($userHouses as $house)
                    <option value="{{ $house['id'] }}">{{ $house['house_number'] }} - {{ $house['estate_name'] }}</option>
                @endforeach
            </select>
        </div>
    @endif

    @if ($selectedHouseId && $currentHouse)
        <!-- Current House Info -->
        <div class="bg-white shadow-sm rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ $currentHouse->house_number }}</h3>
                <p class="text-sm text-gray-500">{{ $currentHouse->estate->name }} - {{ $currentHouse->address }}</p>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <!-- Unpaid Invoices -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Unpaid Invoices</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $this->unpaidInvoicesCount }}</p>
                    </div>
                </div>
            </div>

            <!-- Total Unpaid Amount -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Due</p>
                        <p class="text-2xl font-semibold text-gray-900">KES {{ number_format($this->totalUnpaidAmount, 2) }}</p>
                    </div>
                </div>
            </div>

            <!-- Current Month Consumption -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Current Month</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $consumptionStats['current_month'] }} units</p>
                    </div>
                </div>
            </div>

            <!-- Unread Messages -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Unread Messages</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $this->unreadMessagesCount }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Consumption Trend -->
        <div class="bg-white shadow-sm rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Consumption Trend</h3>
            </div>
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <p class="text-sm text-gray-500">vs Previous Month</p>
                        <p class="text-2xl font-semibold {{ $consumptionStats['trend'] === 'increasing' ? 'text-red-600' : ($consumptionStats['trend'] === 'decreasing' ? 'text-green-600' : 'text-gray-600') }}">
                            {{ $consumptionStats['change_percentage'] > 0 ? '+' : '' }}{{ $consumptionStats['change_percentage'] }}%
                        </p>
                    </div>
                    <div class="flex items-center">
                        @if ($consumptionStats['trend'] === 'increasing')
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        @elseif ($consumptionStats['trend'] === 'decreasing')
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                            </svg>
                        @else
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
                            </svg>
                        @endif
                        <span class="ml-2 text-sm font-medium {{ $consumptionStats['trend'] === 'increasing' ? 'text-red-600' : ($consumptionStats['trend'] === 'decreasing' ? 'text-green-600' : 'text-gray-600') }}">
                            {{ ucfirst($consumptionStats['trend']) }}
                        </span>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <p class="text-gray-500">Current Month</p>
                        <p class="font-medium">{{ $consumptionStats['current_month'] }} units</p>
                    </div>
                    <div>
                        <p class="text-gray-500">Previous Month</p>
                        <p class="font-medium">{{ $consumptionStats['previous_month'] }} units</p>
                    </div>
                    <div>
                        <p class="text-gray-500">Average</p>
                        <p class="font-medium">{{ $consumptionStats['average_consumption'] }} units</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Invoices -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">Recent Invoices</h3>
                    <a href="{{ route('resident.invoices') }}" class="text-sm text-blue-600 hover:text-blue-800">View All</a>
                </div>
                <div class="divide-y divide-gray-200">
                    @foreach ($recentInvoices as $invoice)
                        <div class="p-6">
                            <div class="flex justify-between items-start">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Invoice #{{ $invoice->id }}</p>
                                    <p class="text-sm text-gray-500">{{ optional($invoice->billing_period_start)->format('M d, Y') ?? 'No date' }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">KES {{ number_format($invoice->total_amount, 2) }}</p>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $invoice->status === 'paid' ? 'bg-green-100 text-green-800' : ($invoice->status === 'overdue' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                                        {{ ucfirst($invoice->status) }}
                                    </span>
                                </div>
                            </div>
                            <div class="mt-4 flex space-x-3">
                                <button wire:click="downloadInvoice({{ $invoice->id }})" class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    Download
                                </button>
                                <a href="{{ route('resident.invoices.show', $invoice->id) }}" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    View Details
                                </a>
                            </div>
                        </div>
                    @endforeach
                    @if ($recentInvoices->count() === 0)
                        <div class="p-6 text-center text-gray-500">
                            <p>No invoices found</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Readings -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">Recent Readings</h3>
                    <a href="{{ route('resident.readings') }}" class="text-sm text-blue-600 hover:text-blue-800">View All</a>
                </div>
                <div class="divide-y divide-gray-200">
                    @foreach ($recentReadings as $reading)
                        <div class="p-6">
                            <div class="flex justify-between items-start">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ optional($reading->reading_date)->format('M d, Y') ?? 'No date' }}</p>
                                    <p class="text-sm text-gray-500">Reading: {{ $reading->current_reading }} units</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">{{ $reading->consumption }} units</p>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Approved
                                    </span>
                                </div>
                            </div>
                        </div>
                    @endforeach
                    @if ($recentReadings->count() === 0)
                        <div class="p-6 text-center text-gray-500">
                            <p>No readings found</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @else
        <!-- No Houses Assigned -->
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No houses assigned</h3>
                <p class="mt-1 text-sm text-gray-500">You don't have any houses assigned to your account. Please contact the administrator.</p>
                <div class="mt-6">
                    <a href="{{ route('resident.inquiry') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Contact Administrator
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>