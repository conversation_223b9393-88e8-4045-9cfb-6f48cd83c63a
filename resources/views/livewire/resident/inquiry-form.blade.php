<div>
    <!-- House Selection -->
    @if (count($userHouses) > 1)
        <div class="mb-6">
            <label for="house-select" class="block text-sm font-medium text-gray-700 mb-2">Select House</label>
            <select id="house-select" wire:model.live="selectedHouseId" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                @foreach ($userHouses as $house)
                    <option value="{{ $house['id'] }}">{{ $house['house_number'] }} - {{ $house['estate_name'] }}</option>
                @endforeach
            </select>
        </div>
    @endif

    @if ($selectedHouseId && $currentHouse)
        <!-- Current House Info -->
        <div class="bg-white shadow-sm rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ $currentHouse->house_number }}</h3>
                <p class="text-sm text-gray-500">{{ $currentHouse->estate->name }} - {{ $currentHouse->address }}</p>
            </div>
        </div>

        @if ($isSubmitted)
            <!-- Success Message -->
            <div class="bg-white shadow-sm rounded-lg mb-6">
                <div class="px-6 py-12 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Inquiry Submitted Successfullyh3>
                    <p class="text-sm text-gray-500 mb-6">Thank you for contacting us. We have received your inquiry and will get back to you soon.</p>
                    <div class="flex justify-center space-x-3">
                        <button wire:click="resetFormAndContinue" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Submit Another Inquiry
                        </button>
                        <a href="{{ route('resident.dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Return to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        @else
            <!-- Inquiry Form -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Submit an Inquiry</h3>
                    <p class="text-sm text-gray-500">Fill out the form below to contact the management team.</p>
                </div>
                <form wire:submit.prevent="submitInquiry" class="p-6 space-y-6">
                    <!-- House Selection -->
                    <div>
                        <label for="house" class="block text-sm font-medium text-gray-700 mb-1">House</label>
                        <select id="house" wire:model="selectedHouseId" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md" disabled>
                            @foreach ($userHouses as $house)
                                <option value="{{ $house['id'] }}" {{ $selectedHouseId == $house['id'] ? 'selected' : '' }}>
                                    {{ $house['house_number'] }} - {{ $house['estate_name'] }}
                                </option>
                            @endforeach
                        </select>
                        @error('selectedHouseId')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Inquiry Type -->
                    <div>
                        <label for="inquiry-type" class="block text-sm font-medium text-gray-700 mb-1">Inquiry Type</label>
                        <select id="inquiry-type" wire:model="inquiryType" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option value="">Select an inquiry type</option>
                            @foreach ($this->inquiryTypes as $key => $label)
                                <option value="{{ $key }}">{{ $label }}</option>
                            @endforeach
                        </select>
                        @error('inquiryType')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Subject -->
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                        <input type="text" id="subject" wire:model="subject" placeholder="Brief description of your inquiry" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        @error('subject')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Message -->
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                        <textarea id="message" wire:model="message" rows="6" placeholder="Please provide detailed information about your inquiry..." class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"></textarea>
                        @error('message')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Priority and Contact Method -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Priority -->
                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                            <select id="priority" wire:model="priority" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                @foreach ($this->priorityLevels as $key => $level)
                                    <option value="{{ $key }}">{{ $level['label'] }}</option>
                                @endforeach
                            </select>
                            @error('priority')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Contact Method -->
                        <div>
                            <label for="contact-method" class="block text-sm font-medium text-gray-700 mb-1">Preferred Contact Method</label>
                            <select id="contact-method" wire:model="contactMethod" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                @foreach ($this->contactMethods as $key => $label)
                                    <option value="{{ $key }}">{{ $label }}</option>
                                @endforeach
                            </select>
                            @error('contactMethod')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Contact Phone (conditional) -->
                    @if (in_array($contactMethod, ['phone', 'whatsapp']))
                        <div>
                            <label for="contact-phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                            <input type="tel" id="contact-phone" wire:model="contactPhone" placeholder="Enter your phone number" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            @error('contactPhone')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    @endif

                    <!-- Attachment -->
                    <div>
                        <label for="attachment" class="block text-sm font-medium text-gray-700 mb-1">Attachment (Optional)</label>
                        <input type="file" id="attachment" wire:model="attachment" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <p class="mt-1 text-sm text-gray-500">PDF, DOC, DOCX, JPG, JPEG, PNG files up to 5MB</p>
                        @error('attachment')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-3">
                        <a href="{{ route('resident.dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            Submit Inquiry
                        </button>
                    </div>
                </form>
            </div>
        @endif
    @else
        <!-- No Houses Assigned -->
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No houses assigned</h3>
                <p class="mt-1 text-sm text-gray-500">You don't have any houses assigned to your account. Please contact the administrator.</p>
            </div>
        </div>
    @endif
</div>