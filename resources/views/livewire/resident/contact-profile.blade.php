<div>
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">My Contact Information</h1>
        <p class="text-sm text-gray-600 dark:text-gray-400">
            Manage your personal contact details and preferences
        </p>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="mb-6 rounded-lg border-l-4 border-green-500 bg-green-100 p-4 text-green-700" role="alert">
            <p class="font-bold">Success</p>
            <p>{{ session('message') }}</p>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mb-6 rounded-lg border-l-4 border-red-500 bg-red-100 p-4 text-red-700" role="alert">
            <p class="font-bold">Error</p>
            <p>{{ session('error') }}</p>
        </div>
    @endif

    <!-- No House Assigned Message -->
    @if (!$userHouse)
        <div class="mb-6 rounded-lg border-l-4 border-yellow-500 bg-yellow-100 p-4 text-yellow-700" role="alert">
            <p class="font-bold">No House Assigned</p>
            <p>You are not currently assigned to any house. Please contact your estate manager to get assigned to a house.</p>
        </div>
    @endif

    <!-- Contact Information Card -->
    @if ($userHouse && $hasContact)
        <div class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow dark:border-gray-700 dark:bg-gray-800">
            <!-- Card Header -->
            <div class="border-b border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-700 dark:bg-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                            {{ $first_name }} {{ $last_name }}
                        </h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ $userHouse->full_address }}
                        </p>
                    </div>
                    <button
                        wire:click="edit"
                        class="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800"
                    >
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Edit Information
                    </button>
                </div>
            </div>

            <!-- Estate and House Information -->
            <div class="border-b border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-700 dark:bg-gray-700">
                <h3 class="text-base font-medium text-gray-900 dark:text-white mb-4">Property Information</h3>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                        <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Estate</label>
                        <p class="text-sm text-gray-900 dark:text-white font-medium">{{ $estate ? $estate->name : 'N/A' }}</p>
                        @if ($estate)
                            <p class="text-xs text-gray-600 dark:text-gray-400">Code: {{ $estate->id }}</p>
                        @endif
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500 dark:text-gray-400">House/Unit</label>
                        <p class="text-sm text-gray-900 dark:text-white font-medium">{{ $userHouse->house_number }}</p>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Code: {{ $userHouse->id }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Full Address</label>
                        <p class="text-sm text-gray-900 dark:text-white">{{ $userHouse->full_address }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Property Type</label>
                        <p class="text-sm text-gray-900 dark:text-white">{{ ucfirst($userHouse->type) }}</p>
                    </div>
                </div>
            </div>

            <!-- Card Body -->
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <!-- Personal Information -->
                    <div>
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-4">Personal Information</h3>
                        <div class="space-y-3">
                            <div>
                                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Full Name</label>
                                <p class="text-sm text-gray-900 dark:text-white">
                                    {{ $first_name }} {{ $middle_name }} {{ $last_name }}
                                </p>
                            </div>
                            
                            <div>
                                <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Email</label>
                                <p class="text-sm text-gray-900 dark:text-white">{{ $user->email }}</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400">From your user account</p>
                            </div>

                            @if ($email && $email != $user->email)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Contact Email</label>
                                    <p class="text-sm text-gray-900 dark:text-white">{{ $email }}</p>
                                    <p class="text-xs text-gray-600 dark:text-gray-400">Separate contact email</p>
                                </div>
                            @endif

                            @if ($phone)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
                                    <p class="text-sm text-gray-900 dark:text-white">📞 {{ $phone }}</p>
                                </div>
                            @endif

                            @if ($whatsapp_number)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">WhatsApp</label>
                                    <p class="text-sm text-gray-900 dark:text-white">💬 {{ $whatsapp_number }}</p>
                                </div>
                            @endif

                            @if ($id_number)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">ID Number</label>
                                    <p class="text-sm text-gray-900 dark:text-white">{{ $id_number }}</p>
                                </div>
                            @endif

                            @if ($date_of_birth)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Date of Birth</label>
                                    <p class="text-sm text-gray-900 dark:text-white">{{ \Carbon\Carbon::parse($date_of_birth)->format('F j, Y') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div>
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-4">Additional Information</h3>
                        <div class="space-y-3">
                            @if ($occupation)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Occupation</label>
                                    <p class="text-sm text-gray-900 dark:text-white">{{ $occupation }}</p>
                                </div>
                            @endif

                            @if ($company)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</label>
                                    <p class="text-sm text-gray-900 dark:text-white">{{ $company }}</p>
                                </div>
                            @endif

                            @if ($postal_address)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Postal Address</label>
                                    <p class="text-sm text-gray-900 dark:text-white">{{ $postal_address }}</p>
                                </div>
                            @endif

                            @if ($emergency_contact_name || $emergency_contact_phone)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Emergency Contact</label>
                                    <p class="text-sm text-gray-900 dark:text-white">
                                        @if ($emergency_contact_name)
                                            {{ $emergency_contact_name }}
                                        @endif
                                        @if ($emergency_contact_phone)
                                            <br>📞 {{ $emergency_contact_phone }}
                                        @endif
                                    </p>
                                </div>
                            @endif

                            @if ($notes)
                                <div>
                                    <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</label>
                                    <p class="text-sm text-gray-900 dark:text-white">{{ $notes }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="mt-6 border-t border-gray-200 pt-6 dark:border-gray-700">
                    <h3 class="text-base font-medium text-gray-900 dark:text-white mb-4">Communication Preferences</h3>
                    <div class="flex flex-wrap gap-4">
                        <div class="flex items-center">
                            @if ($receive_invoices)
                                <span class="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800 dark:bg-green-900 dark:text-green-300">
                                    ✓ Receives Invoices
                                </span>
                            @else
                                <span class="inline-flex items-center rounded-full bg-red-100 px-3 py-1 text-sm font-medium text-red-800 dark:bg-red-900 dark:text-red-300">
                                    ✗ Does Not Receive Invoices
                                </span>
                            @endif
                        </div>

                        <div class="flex items-center">
                            @if ($receive_notifications)
                                <span class="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    ✓ Receives Notifications
                                </span>
                            @else
                                <span class="inline-flex items-center rounded-full bg-red-100 px-3 py-1 text-sm font-medium text-red-800 dark:bg-red-900 dark:text-red-300">
                                    ✗ Does Not Receive Notifications
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @elseif ($userHouse)
        <!-- No Contact Found -->
        <div class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow dark:border-gray-700 dark:bg-gray-800">
            <div class="px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">No Contact Information</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    No contact information found for your house. Click the button below to add your contact details.
                </p>
                <div class="mt-6">
                    <button
                        wire:click="edit"
                        class="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800"
                    >
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add Contact Information
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Edit Modal -->
    <div x-data="{ open: @entangle('showEditModal') }" x-show="open" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex min-h-screen items-center justify-center p-4">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"></div>
            
            <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all dark:bg-gray-800 sm:my-8 sm:w-full sm:max-w-2xl" x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                <form wire:submit.prevent="save">
                    <div class="bg-white px-4 pb-4 pt-5 dark:bg-gray-800 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 w-full text-center sm:mt-0 sm:text-left">
                                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                                    {{ $hasContact ? 'Edit Contact Information' : 'Add Contact Information' }}
                                </h3>
                                
                                <!-- Property Information (Read-only) -->
                                <div class="mt-4 p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Property Informationh4>
                                    <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
                                        <div>
                                            <label class="text-xs font-medium text-gray-500 dark:text-gray-400">Estate</label>
                                            <div class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-100 p-2 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-white">
                                                {{ $estate ? $estate->name : 'N/A' }}
                                            </div>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Code: {{ $estate ? $estate->id : 'N/A' }}</p>
                                        </div>
                                        <div>
                                            <label class="text-xs font-medium text-gray-500 dark:text-gray-400">House/Unit</label>
                                            <div class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-100 p-2 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-white">
                                                {{ $userHouse ? $userHouse->house_number : 'N/A' }}
                                            </div>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Code: {{ $userHouse ? $userHouse->id : 'N/A' }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                        <!-- First Name -->
                                        <div>
                                            <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">First Name *</label>
                                            <input
                                                type="text"
                                                id="first_name"
                                                wire:model="first_name"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                                required
                                            />
                                            @error('first_name')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Last Name -->
                                        <div>
                                            <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Name *</label>
                                            <input
                                                type="text"
                                                id="last_name"
                                                wire:model="last_name"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                                required
                                            />
                                            @error('last_name')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Middle Name -->
                                        <div>
                                            <label for="middle_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Middle Name</label>
                                            <input
                                                type="text"
                                                id="middle_name"
                                                wire:model="middle_name"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            @error('middle_name')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Account Email (Read-only) -->
                                        <div>
                                            <label for="account_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Account Email</label>
                                            <input
                                                type="email"
                                                id="account_email"
                                                value="{{ $user->email }}"
                                                readonly
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-100 p-2.5 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-white"
                                            />
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">From your user account (cannot be changed)</p>
                                        </div>

                                        <!-- Contact Email -->
                                        <div>
                                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Contact Email</label>
                                            <input
                                                type="email"
                                                id="email"
                                                wire:model="email"
                                                placeholder="Separate contact email (optional)"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Different from account email</p>
                                            @error('email')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Phone -->
                                        <div>
                                            <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
                                            <input
                                                type="text"
                                                id="phone"
                                                wire:model="phone"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            @error('phone')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- WhatsApp Number -->
                                        <div>
                                            <label for="whatsapp_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300">WhatsApp Number</label>
                                            <input
                                                type="text"
                                                id="whatsapp_number"
                                                wire:model="whatsapp_number"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            @error('whatsapp_number')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- ID Number -->
                                        <div>
                                            <label for="id_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300">ID Number</label>
                                            <input
                                                type="text"
                                                id="id_number"
                                                wire:model="id_number"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            @error('id_number')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Date of Birth -->
                                        <div>
                                            <label for="date_of_birth" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date of Birth</label>
                                            <input
                                                type="date"
                                                id="date_of_birth"
                                                wire:model="date_of_birth"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            @error('date_of_birth')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Occupation -->
                                        <div>
                                            <label for="occupation" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Occupation</label>
                                            <input
                                                type="text"
                                                id="occupation"
                                                wire:model="occupation"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            @error('occupation')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Company -->
                                        <div>
                                            <label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Company</label>
                                            <input
                                                type="text"
                                                id="company"
                                                wire:model="company"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            @error('company')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Emergency Contact Name -->
                                        <div>
                                            <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Emergency Contact Name</label>
                                            <input
                                                type="text"
                                                id="emergency_contact_name"
                                                wire:model="emergency_contact_name"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            @error('emergency_contact_name')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Emergency Contact Phone -->
                                        <div>
                                            <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Emergency Contact Phone</label>
                                            <input
                                                type="text"
                                                id="emergency_contact_phone"
                                                wire:model="emergency_contact_phone"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            />
                                            @error('emergency_contact_phone')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Postal Address -->
                                        <div class="sm:col-span-2">
                                            <label for="postal_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Postal Address</label>
                                            <textarea
                                                id="postal_address"
                                                wire:model="postal_address"
                                                rows="2"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            ></textarea>
                                            @error('postal_address')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Notes -->
                                        <div class="sm:col-span-2">
                                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                                            <textarea
                                                id="notes"
                                                wire:model="notes"
                                                rows="2"
                                                class="mt-1 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                                            ></textarea>
                                            @error('notes')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Checkboxes -->
                                        <div class="sm:col-span-2 space-y-3">
                                            <div class="flex items-center">
                                                <input
                                                    id="receive_invoices"
                                                    wire:model="receive_invoices"
                                                    type="checkbox"
                                                    class="h-4 w-4 rounded border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-blue-600"
                                                />
                                                <label for="receive_invoices" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                                    Receive Invoices
                                                </label>
                                            </div>

                                            <div class="flex items-center">
                                                <input
                                                    id="receive_notifications"
                                                    wire:model="receive_notifications"
                                                    type="checkbox"
                                                    class="h-4 w-4 rounded border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-blue-600"
                                                />
                                                <label for="receive_notifications" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                                    Receive Notifications
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 dark:bg-gray-700 sm:flex sm:flex-row-reverse sm:px-6">
                        <button
                            type="submit"
                            class="inline-flex w-full justify-center rounded-lg bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 sm:ml-3 sm:w-auto"
                        >
                            {{ $hasContact ? 'Update' : 'Save' }}
                        </button>
                        <button
                            type="button"
                            wire:click="cancel"
                            class="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600 sm:mt-0 sm:w-auto"
                        >
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>