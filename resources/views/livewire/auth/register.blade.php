<div class="grid h-screen grid-cols-1 bg-gray-100 dark:bg-gray-900 md:grid-cols-2">
    <div class="hidden bg-blue-600 md:block">
        <div class="flex h-full flex-col items-center justify-center">
            <h1 class="text-4xl font-bold text-white">Water Management System</h1>
            <p class="mt-2 text-lg text-blue-200">Smart water management for modern estates</p>
        </div>
    </div>
    <div class="flex items-center justify-center">
        <div class="w-full max-w-md p-8 space-y-6">
            <div>
                <h2 class="text-3xl font-bold text-center text-gray-900 dark:text-white">Sign Up</h2>
                <p class="mt-2 text-sm text-center text-gray-600 dark:text-gray-400">
                    Enter your details to create an account!
                </p>
            </div>
            <form wire:submit.prevent="register" class="space-y-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                    <div class="mt-1">
                        <input wire:model="name" id="name" name="name" type="text" autocomplete="name" required class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-500 dark:text-white">
                        @error('name') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
                    </div>
                </div>
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email address</label>
                    <div class="mt-1">
                        <input wire:model="email" id="email" name="email" type="email" autocomplete="email" required class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-500 dark:text-white">
                        @error('email') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
                    </div>
                </div>
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Password</label>
                    <div class="mt-1">
                        <input wire:model="password" id="password" name="password" type="password" autocomplete="new-password" required class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-500 dark:text-white">
                        @error('password') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
                    </div>
                </div>
                <div>
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Confirm Password</label>
                    <div class="mt-1">
                        <input wire:model="password_confirmation" id="password_confirmation" name="password_confirmation" type="password" autocomplete="new-password" required class="block w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-500 dark:text-white">
                    </div>
                </div>
                <div>
                    <button type="submit" class="flex justify-center w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Sign Up
                    </button>
                </div>
            </form>
            @if (Route::has('login'))
                <p class="mt-4 text-sm text-center text-gray-600 dark:text-gray-400">
                    Already have an account?
                    <a href="{{ route('login') }}" class="font-medium text-blue-600 hover:text-blue-500">Sign In</a>
                </p>
            @endif
        </div>
    </div>
</div>
