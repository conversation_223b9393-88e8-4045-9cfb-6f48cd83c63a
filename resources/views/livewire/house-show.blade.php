<div x-data="{ showContactModal: @entangle('showContactModal'), showReadingModal: @entangle('showReadingModal') }">
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center gap-3">
            <a href="{{ $this->getHousesRoute() }}" class="flex h-9 w-9 items-center justify-center rounded-full border border-gray-300 bg-white text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.05025 14.9497C6.75736 15.2426 6.28248 15.2426 5.98959 14.9497C5.6967 14.6568 5.6967 14.1819 5.98959 13.889L11.2196 8.65899L5.98959 3.42899C5.6967 3.1361 5.6967 2.66122 5.98959 2.36833C6.28248 2.07544 6.75736 2.07544 7.05025 2.36833L12.5109 7.82899C12.8038 8.12188 12.8038 8.59676 12.5109 8.88965L7.05025 14.3497V14.9497Z" transform="rotate(180 7.05025 8.65899)" fill=""></path>
                </svg>
            </a>
            <h2 class="text-2xl font-semibold text-gray-800 dark:text-white/90">
                House {{ $house->house_number }}
            </h2>
            <span class="rounded-full px-2 py-0.5 text-theme-xs font-medium {{ $house->contacts->count() > 0 ? 'bg-success-50 text-success-700 dark:bg-success-500/15 dark:text-success-500' : 'bg-warning-50 text-warning-700 dark:bg-warning-500/15 dark:text-warning-400' }}">
                {{ $house->contacts->count() > 0 ? 'Occupied' : 'Vacant' }}
            </span>
        </div>
        
        <div class="flex items-center gap-2">
            <a href="{{ $this->getHouseEditRoute($house) }}" class="flex w-full items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200 sm:w-auto">
                <svg class="fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M15.0911 2.78206C14.2125 1.90338 12.7878 1.90338 11.9092 2.78206L4.57524 10.116C4.26682 10.4244 4.0547 10.8158 3.96468 11.2426L3.31231 14.3352C3.25997 14.5833 3.33653 14.841 3.51583 15.0203C3.69512 15.1996 3.95286 15.2761 4.20096 15.2238L7.29355 14.5714C7.72031 14.4814 8.11172 14.2693 8.42013 13.9609L15.7541 6.62695C16.6327 5.74827 16.6327 4.32365 15.7541 3.44497L15.0911 2.78206ZM12.9698 3.84272C13.2627 3.54982 13.7376 3.54982 14.0305 3.84272L14.6934 4.50563C14.9863 4.79852 14.9863 5.2734 14.6934 5.56629L14.044 6.21573L12.3204 4.49215L12.9698 3.84272ZM11.2597 5.55281L5.6359 11.1766C5.53309 11.2794 5.46238 11.4099 5.43238 11.5522L5.01758 13.5185L6.98394 13.1037C7.1262 13.0737 7.25666 13.003 7.35947 12.9002L12.9833 7.27639L11.2597 5.55281Z" fill=""></path>
                </svg>
                Edit House
            </a>
        </div>
    </div>

    {{-- Tabs Navigation --}}
    <div class="border-b border-gray-200 dark:border-gray-800 mb-6">
        <nav class="-mb-px flex space-x-8 px-4 sm:px-6 lg:px-8" aria-label="Tabs">
            <a href="#" wire:click.prevent="$set('activeTab', 'overview')"
               class="{{ $activeTab === 'overview' ? 'border-brand-500 text-brand-600 dark:text-brand-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-700' }}
                   whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Overview
            </a>
            <a href="#" wire:click.prevent="$set('activeTab', 'contacts')"
               class="{{ $activeTab === 'contacts' ? 'border-brand-500 text-brand-600 dark:text-brand-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-700' }}
                   whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Contacts ({{ $contacts->count() }})
            </a>
            <a href="#" wire:click.prevent="$set('activeTab', 'readings')"
               class="{{ $activeTab === 'readings' ? 'border-brand-500 text-brand-600 dark:text-brand-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-700' }}
                   whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Readings ({{ $stats['total_readings'] }})
            </a>
            <a href="#" wire:click.prevent="$set('activeTab', 'invoices')"
               class="{{ $activeTab === 'invoices' ? 'border-brand-500 text-brand-600 dark:text-brand-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-700' }}
                   whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Invoices ({{ $stats['total_invoices'] }})
            </a>
        </nav>
    </div>

    <div class="mx-auto max-w-7xl p-4 md:p-6">
        @if ($activeTab === 'overview')
            {{-- House Overview --}}
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {{-- House Details --}}
                <div class="lg:col-span-2">
                    <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                        <div class="mb-5 sm:mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">House Information</h3>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">House Number</p>
                                    <p class="rounded-full border border-gray-200 bg-gray-50 px-2 py-0.5 text-theme-xs font-medium text-gray-700 dark:border-gray-700 dark:bg-white/[0.05] dark:text-gray-400">{{ $house->house_number }}</p>
                                </div>
                                
                                <div>
                                    <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Estate</p>
                                    <p class="text-sm font-medium text-gray-800 dark:text-white/90">
                                        <a href="{{ $this->getEstateShowRoute($house->estate) }}" class="text-blue-600 hover:text-blue-500">{{ $house->estate->name }}</a>
                                    </p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $house->estate->city }}</p>
                                </div>
                                
                                @if($house->meter_number)
                                    <div>
                                        <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Meter Number</p>
                                        <p class="rounded-full border border-gray-200 bg-gray-50 px-2 py-0.5 text-theme-xs font-medium text-gray-700 dark:border-gray-700 dark:bg-white/[0.05] dark:text-gray-400">{{ $house->meter_number }}</p>
                                    </div>
                                @endif
                                
                                @if($house->address)
                                    <div class="sm:col-span-2">
                                        <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Address</p>
                                        <p class="text-sm font-medium text-gray-800 dark:text-white/90">{{ $house->address }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                
                {{-- Statistics --}}
                <div>
                    <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                        <div class="mb-5 sm:mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Statistics</h3>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="grid grid-cols-2 gap-4 text-center">
                                <div>
                                    <div class="text-xl font-semibold text-blue-600 dark:text-blue-400">{{ $stats['total_readings'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Readings</div>
                                </div>
                                <div>
                                    <div class="text-xl font-semibold text-purple-600 dark:text-purple-400">{{ $stats['total_invoices'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Invoices</div>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4 text-center">
                                <div>
                                    <div class="text-xl font-semibold text-green-600 dark:text-green-400">{{ $stats['paid_invoices'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Paid</div>
                                </div>
                                <div>
                                    <div class="text-xl font-semibold text-red-600 dark:text-red-400">{{ $stats['outstanding_invoices'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Outstanding</div>
                                </div>
                            </div>
                            
                            <div class="border-t border-gray-100 pt-4 text-center dark:border-gray-800">
                                <div class="text-lg font-semibold text-gray-800 dark:text-white/90">KES {{ number_format($stats['total_billed'], 2) }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Total Billed</div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-lg font-semibold text-green-600 dark:text-green-400">KES {{ number_format($stats['total_paid'], 2) }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Total Paid</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            {{-- Recent Activity --}}
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {{-- Recent Readings --}}
                <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                    <div class="mb-5 sm:mb-6 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Recent Readings</h3>
                        <button wire:click="openReadingModal" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">
                            Add Reading
                        </button>
                    </div>
                    
                    @if($recentReadings->count() > 0)
                        <div class="space-y-3">
                            @foreach($recentReadings as $reading)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $reading->reading_value }} m³</div>
                                        <div class="text-xs text-gray-500">{{ $reading->reading_date->format('M d, Y') }}</div>
                                    </div>
                                    <div class="text-right">
                                        @if($reading->consumption)
                                            <div class="text-sm font-medium text-blue-600">{{ $reading->consumption }} m³</div>
                                            <div class="text-xs text-gray-500">consumed</div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                            No readings found. <button wire:click="openReadingModal" class="ml-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">Add first reading</button>
                        </div>
                    @endif
                </div>
                
                {{-- Recent Invoices --}}
                <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                    <div class="mb-5 sm:mb-6 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Recent Invoices</h3>
                        <a href="{{ $this->getInvoiceCreateRoute($house->id) }}" class="text-sm text-blue-600 hover:text-blue-500">Generate Invoice</a>
                    </div>
                    
                    @if($recentInvoices->count() > 0)
                        <div class="space-y-3">
                            @foreach($recentInvoices as $invoice)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $invoice->invoice_number }}</div>
                                        <div class="text-xs text-gray-500">{{ $invoice->billing_period_start->format('M d, Y') }}</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-gray-900">KES {{ number_format($invoice->total_amount, 2) }}</div>
                                        <div class="text-xs {{ $invoice->status === 'paid' ? 'text-green-600' : 'text-red-600' }}">
                                            {{ $invoice->status }}
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
No invoices found. <a href="{{ $this->getInvoiceCreateRoute($house->id) }}" class="text-blue-600 hover:text-blue-500">Generate first invoice</a>                        </div>
                    @endif
                </div>
            </div>
            
        @elseif ($activeTab === 'contacts')
            {{-- Contacts Management --}}
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                <div class="mb-5 sm:mb-6 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Contacts</h3>
                    <button wire:click="openContactModal" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">
                        Add Contact
                    </button>
                </div>


                
                @if($contacts->count() > 0)
                    <div class="space-y-4">
                        @foreach($contacts as $contact)
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                            <span class="text-gray-600 font-medium">{{ strtoupper(substr($contact->name, 0, 1)) }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $contact->name }}
                                            @if($contact->is_primary)
                                                <span class="ml-2 inline-flex px-2 py-0.5 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Primary</span>
                                            @endif
                                        </div>
                                        <div class="text-sm text-gray-500">{{ $contact->phone }}</div>
                                        @if($contact->email)
                                            <div class="text-sm text-gray-500">{{ $contact->email }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button wire:click="deleteContact({{ $contact->id }})" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:text-red-200 dark:bg-red-900/50 dark:hover:bg-red-900/70">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        No contacts found. <button wire:click="openContactModal" class="ml-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">Add first contact</button>
                    </div>
                @endif
            </div>
            
        @elseif ($activeTab === 'readings')
            {{-- Meter Readings --}}
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                <div class="mb-5 sm:mb-6 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Meter Readings</h3>
                    <button wire:click="openReadingModal" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">
                        Add Reading
                    </button>
                </div>


                
                @if($recentReadings->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
<tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reading</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Consumption</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($recentReadings as $reading)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $reading->reading_date->format('M d, Y') }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $reading->reading_value }} m³</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            @if($reading->consumption)
                                                <span class="text-blue-600">{{ $reading->consumption }} m³</span>
                                            @else
                                                <span class="text-gray-400">-</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">{{ $reading->notes ?? '-' }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button wire:click="deleteReading({{ $reading->id }})" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:text-red-200 dark:bg-red-900/50 dark:hover:bg-red-900/70">
                                            Delete
                                        </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        No readings found. <button wire:click="openReadingModal" class="ml-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">Add first reading</button>
                    </div>
                @endif
            </div>
            
        @elseif ($activeTab === 'invoices')
            {{-- Invoices --}}
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
                <div class="mb-5 sm:mb-6 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">Invoices</h3>
                    <a href="{{ $this->getInvoiceCreateRoute($house->id) }}" class="text-blue-600 hover:text-blue-500">Generate Invoice</a>
                </div>

                @if($recentInvoices->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
<tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($recentInvoices as $invoice)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $invoice->invoice_number }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $invoice->billing_period_start->format('M d, Y') }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">KES {{ number_format($invoice->total_amount, 2) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $invoice->status === 'paid' ? 'bg-green-100 text-green-800' : ($invoice->status === 'overdue' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                                                {{ ucfirst($invoice->status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="{{ $this->getInvoiceShowRoute($invoice) }}" class="text-blue-600 hover:text-blue-900">View</a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        No invoices found. <a href="{{ $this->getInvoiceCreateRoute($house->id) }}" class="text-blue-600 hover:text-blue-500">Generate first invoice</a>
                    </div>
                @endif
            </div>
        @endif
    </div>

    <!-- Contact Modal -->
    <x-form-modal
        :show="$showContactModal"
        title="Add Contact"
        onSave="saveContact"
        onClose="$set('showContactModal', false)"
        name="contact-modal"
    >
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name <span class="text-red-500">*</span></label>
                <input type="text" wire:model="contactForm.name" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone <span class="text-red-500">*</span></label>
                <input type="text" wire:model="contactForm.phone" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label>
                <input type="email" wire:model="contactForm.email" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
            </div>
            <div class="flex items-center">
                <input type="checkbox" wire:model="contactForm.is_primary" id="is_primary" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                <label for="is_primary" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Primary Contact</label>
            </div>
        </div>
    </x-form-modal>

    <!-- Reading Modal -->
    <x-form-modal
        :show="$showReadingModal"
        title="Add Meter Reading"
        onSave="saveReading"
        onClose="$set('showReadingModal', false)"
        name="reading-modal"
    >
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Reading Date <span class="text-red-500">*</span></label>
                <input type="date" wire:model="readingForm.reading_date" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Reading Value (m³) <span class="text-red-500">*</span></label>
                <input type="number" step="0.01" wire:model="readingForm.reading_value" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                <textarea wire:model="readingForm.notes" rows="3" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
            </div>
        </div>
    </x-form-modal>
</div>