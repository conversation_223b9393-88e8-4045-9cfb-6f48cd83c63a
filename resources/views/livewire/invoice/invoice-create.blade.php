<div>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <h2 class="text-2xl font-bold mb-6">Create New Invoice</h2>

                <form wire:submit="save" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="house_id" class="block text-sm font-medium text-gray-700">House</label>
                            <select wire:model="house_id" id="house_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">Select a house</option>
                                @foreach($houses as $house)
                                    <option value="{{ $house->id }}">{{ $house->house_number }} - {{ $house->estate->name }}</option>
                                @endforeach
                            </select>
                            @error('house_id') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="period_start" class="block text-sm font-medium text-gray-700">Period Start</label>
                            <input type="date" wire:model="period_start" id="period_start" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('period_start') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="period_end" class="block text-sm font-medium text-gray-700">Period End</label>
                            <input type="date" wire:model="period_end" id="period_end" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('period_end') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="previous_reading" class="block text-sm font-medium text-gray-700">Previous Reading</label>
                            <input type="number" wire:model="previous_reading" id="previous_reading" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('previous_reading') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="current_reading" class="block text-sm font-medium text-gray-700">Current Reading</label>
                            <input type="number" wire:model="current_reading" id="current_reading" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('current_reading') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="consumption" class="block text-sm font-medium text-gray-700">Consumption (m³)</label>
                            <input type="number" wire:model="consumption" id="consumption" step="0.01" readonly class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm">
                        </div>

                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700">Amount (KES)</label>
                            <input type="number" wire:model="amount" id="amount" step="0.01" readonly class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm">
                        </div>
                    </div>

                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <textarea wire:model="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                        @error('notes') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div class="flex items-center justify-end space-x-3">
                        <a href="{{ route('invoices.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Create Invoice
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>