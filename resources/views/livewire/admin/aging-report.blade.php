<div>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold">Aging Report</h2>
                    @if($show_report)
                        <div class="flex space-x-3">
                            <button wire:click="exportToExcel" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Export Excel
                            </button>
                            <button wire:click="exportToPdf" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                                Export PDF
                            </button>
                        </div>
                    @endif
                </div>

                <form wire:submit.prevent="generateReport" class="space-y-6 mb-8">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <label for="estate_id" class="block text-sm font-medium text-gray-700">Estate (Optional)</label>
                            <select wire:model="estate_id" id="estate_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Estates</option>
                                @foreach(\App\Models\Estate::all() as $estate)
                                    <option value="{{ $estate->id }}">{{ $estate->name }}</option>
                                @endforeach
                            </select>
                            @error('estate_id') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-3">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Generate Report
                        </button>
                    </div>
                </form>

                @if($show_report)
                    <div class="space-y-6">
                        <!-- Summary Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
                            <div class="bg-green-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-green-800 truncate">Current</dt>
                                                <dd class="text-lg font-medium text-green-900">KES {{ number_format($aging_report['current'], 2) }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-yellow-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-yellow-800 truncate">1-30 Days</dt>
                                                <dd class="text-lg font-medium text-yellow-900">KES {{ number_format($aging_report['days_1_30'], 2) }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-orange-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-orange-800 truncate">31-60 Days</dt>
                                                <dd class="text-lg font-medium text-orange-900">KES {{ number_format($aging_report['days_31_60'], 2) }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-red-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-red-800 truncate">61-90 Days</dt>
                                                <dd class="text-lg font-medium text-red-900">KES {{ number_format($aging_report['days_61_90'], 2) }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-purple-800 truncate">Over 90 Days</dt>
                                                <dd class="text-lg font-medium text-purple-900">KES {{ number_format($aging_report['days_over_90'], 2) }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-gray-800 truncate">Total Outstanding</dt>
                                                <dd class="text-lg font-medium text-gray-900">KES {{ number_format($aging_report['total_outstanding'], 2) }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Chart Visualization -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-4">Aging Analysis</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Current</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-48 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: {{ $aging_report['total_outstanding'] > 0 ? ($aging_report['current'] / $aging_report['total_outstanding'] * 100) : 0 }}%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">{{ $aging_report['total_outstanding'] > 0 ? round($aging_report['current'] / $aging_report['total_outstanding'] * 100) : 0 }}%</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">1-30 Days</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-48 bg-gray-200 rounded-full h-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: {{ $aging_report['total_outstanding'] > 0 ? ($aging_report['days_1_30'] / $aging_report['total_outstanding'] * 100) : 0 }}%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">{{ $aging_report['total_outstanding'] > 0 ? round($aging_report['days_1_30'] / $aging_report['total_outstanding'] * 100) : 0 }}%</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">31-60 Days</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-48 bg-gray-200 rounded-full h-2">
                                            <div class="bg-orange-500 h-2 rounded-full" style="width: {{ $aging_report['total_outstanding'] > 0 ? ($aging_report['days_31_60'] / $aging_report['total_outstanding'] * 100) : 0 }}%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">{{ $aging_report['total_outstanding'] > 0 ? round($aging_report['days_31_60'] / $aging_report['total_outstanding'] * 100) : 0 }}%</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">61-90 Days</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-48 bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: {{ $aging_report['total_outstanding'] > 0 ? ($aging_report['days_61_90'] / $aging_report['total_outstanding'] * 100) : 0 }}%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">{{ $aging_report['total_outstanding'] > 0 ? round($aging_report['days_61_90'] / $aging_report['total_outstanding'] * 100) : 0 }}%</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Over 90 Days</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-48 bg-gray-200 rounded-full h-2">
                                            <div class="bg-purple-500 h-2 rounded-full" style="width: {{ $aging_report['total_outstanding'] > 0 ? ($aging_report['days_over_90'] / $aging_report['total_outstanding'] * 100) : 0 }}%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">{{ $aging_report['total_outstanding'] > 0 ? round($aging_report['days_over_90'] / $aging_report['total_outstanding'] * 100) : 0 }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Report Details -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between text-sm text-gray-600">
                                <span>Generated on: {{ $aging_report['generated_at']->format('M d, Y H:i') }}</span>
                                <span>Estate Filter: {{ $aging_report['estate_filter'] ? \App\Models\Estate::find($aging_report['estate_filter'])->name : 'All Estates' }}</span>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>