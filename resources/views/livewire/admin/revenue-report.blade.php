<div>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold">Revenue Report</h2>
                    @if($show_report)
                        <div class="flex space-x-3">
                            <button wire:click="exportToExcel" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Export Excel
                            </button>
                            <button wire:click="exportToPdf" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                                Export PDF
                            </button>
                        </div>
                    @endif
                </div>

                <form wire:submit.prevent="generateReport" class="space-y-6 mb-8">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700">Date From</label>
                            <input type="date" wire:model="date_from" id="date_from" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('date_from') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700">Date To</label>
                            <input type="date" wire:model="date_to" id="date_to" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('date_to') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="estate_id" class="block text-sm font-medium text-gray-700">Estate (Optional)</label>
                            <select wire:model="estate_id" id="estate_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Estates</option>
                                @foreach(\App\Models\Estate::all() as $estate)
                                    <option value="{{ $estate->id }}">{{ $estate->name }}</option>
                                @endforeach
                            </select>
                            @error('estate_id') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-3">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Generate Report
                        </button>
                    </div>
                </form>

                @if($show_report)
                    <div class="space-y-6">
                        <!-- Summary Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="bg-green-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-green-800 truncate">Total Revenue</dt>
                                                <dd class="text-lg font-medium text-green-900">KES {{ number_format($revenue_report['total_revenue'], 2) }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-blue-800 truncate">Total Invoices</dt>
                                                <dd class="text-lg font-medium text-blue-900">{{ $revenue_report['total_invoices'] }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-purple-800 truncate">Average Invoice</dt>
                                                <dd class="text-lg font-medium text-purple-900">KES {{ number_format($revenue_report['average_invoice_amount'], 2) }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-yellow-50 overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-yellow-800 truncate">Collection Rate</dt>
                                                <dd class="text-lg font-medium text-yellow-900">{{ number_format($revenue_report['collection_rate'], 2) }}%</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Revenue by Estate -->
                        @if(!empty($revenue_report['revenue_by_estate']))
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h3 class="text-lg font-semibold mb-4">Revenue by Estate</h3>
                                <div class="space-y-4">
                                    @foreach($revenue_report['revenue_by_estate'] as $estate => $revenue)
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm font-medium text-gray-700">{{ $estate }}</span>
                                            <div class="flex items-center space-x-2">
                                                <div class="w-32 bg-gray-200 rounded-full h-2">
                                                    <div class="bg-blue-500 h-2 rounded-full" style="width: {{ $revenue_report['total_revenue'] > 0 ? ($revenue / $revenue_report['total_revenue'] * 100) : 0 }}%"></div>
                                                </div>
                                                <span class="text-sm text-gray-600">KES {{ number_format($revenue, 2) }}</span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Revenue by Payment Method -->
                        @if(!empty($revenue_report['revenue_by_payment_method']))
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h3 class="text-lg font-semibold mb-4">Revenue by Payment Method</h3>
                                <div class="space-y-4">
                                    @foreach($revenue_report['revenue_by_payment_method'] as $method => $revenue)
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm font-medium text-gray-700">{{ $method }}</span>
                                            <div class="flex items-center space-x-2">
                                                <div class="w-32 bg-gray-200 rounded-full h-2">
                                                    <div class="bg-green-500 h-2 rounded-full" style="width: {{ $revenue_report['total_revenue'] > 0 ? ($revenue / $revenue_report['total_revenue'] * 100) : 0 }}%"></div>
                                                </div>
                                                <span class="text-sm text-gray-600">KES {{ number_format($revenue, 2) }}</span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Revenue by Month -->
                        @if(!empty($revenue_report['revenue_by_month']))
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h3 class="text-lg font-semibold mb-4">Revenue by Month</h3>
                                <div class="space-y-4">
                                    @foreach($revenue_report['revenue_by_month'] as $month => $revenue)
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm font-medium text-gray-700">{{ \Carbon\Carbon::parse($month . '-01')->format('M Y') }}</span>
                                            <div class="flex items-center space-x-2">
                                                <div class="w-32 bg-gray-200 rounded-full h-2">
                                                    <div class="bg-purple-500 h-2 rounded-full" style="width: {{ $revenue_report['total_revenue'] > 0 ? ($revenue / $revenue_report['total_revenue'] * 100) : 0 }}%"></div>
                                                </div>
                                                <span class="text-sm text-gray-600">KES {{ number_format($revenue, 2) }}</span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Report Details -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                <div>
                                    <span class="font-medium">Period:</span> {{ $revenue_report['period_start']->format('M d, Y') }} - {{ $revenue_report['period_end']->format('M d, Y') }}
                                </div>
                                <div>
                                    <span class="font-medium">Total Billed:</span> KES {{ number_format($revenue_report['total_billed'], 2) }}
                                </div>
                                <div>
                                    <span class="font-medium">Total Collected:</span> KES {{ number_format($revenue_report['total_collected'], 2) }}
                                </div>
                                <div>
                                    <span class="font-medium">Generated on:</span> {{ $revenue_report['generated_at']->format('M d, Y H:i') }}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>