<div>
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Activity Logs</h1>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Monitor system activities and user actions</p>
            </div>

            <div class="flex gap-2">
                <button
                    wire:click="clearFilters"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Clear Filters
                </button>
                <button
                    wire:click="exportLogs"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export Logs
                </button>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <p class="text-sm text-green-800 dark:text-green-200">{{ session('message') }}</p>
        </div>
    @endif

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Filters</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                    <input
                        type="text"
                        id="search"
                        wire:model.live.debounce.300ms="search"
                        placeholder="Search descriptions, actions..."
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400 sm:text-sm"
                    >
                </div>

                <!-- User Filter -->
                <div>
                    <label for="userFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">User</label>
                    <select
                        id="userFilter"
                        wire:model.live="userFilter"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400 sm:text-sm"
                    >
                        <option value="all">All Users</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}">{{ $user->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Action Filter -->
                <div>
                    <label for="actionFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Action</label>
                    <select
                        id="actionFilter"
                        wire:model.live="actionFilter"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400 sm:text-sm"
                    >
                        <option value="all">All Actions</option>
                        @foreach($actions as $action)
                            <option value="{{ $action }}">{{ ucfirst($action) }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Entity Filter -->
                <div>
                    <label for="entityFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Entity Type</label>
                    <select
                        id="entityFilter"
                        wire:model.live="entityFilter"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400 sm:text-sm"
                    >
                        <option value="all">All Entity Types</option>
                        @foreach($entityTypes as $entityType)
                            <option value="{{ $entityType }}">{{ $entityType }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Date Range -->
                <div class="md:col-span-2 lg:col-span-1">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Range</label>
                    <div class="flex gap-2">
                        <input
                            type="date"
                            wire:model.live="dateFrom"
                            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400 sm:text-sm"
                        >
                        <input
                            type="date"
                            wire:model.live="dateTo"
                            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400 sm:text-sm"
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Logs Table -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Activity Logs</h2>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{ $logs->total() }} total entries
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-900">
                    <tr>
                        <th
                            wire:click="sortBy('created_at')"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                        >
                            <div class="flex items-center space-x-1">
                                <span>Timestamp</span>
                                @if($sortBy === 'created_at')
                                    <svg class="w-4 h-4 {{ $sortDirection === 'asc' ? 'transform rotate-180' : '' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                @endif
                            </div>
                        </th>
                        <th
                            wire:click="sortBy('user_id')"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                        >
                            <div class="flex items-center space-x-1">
                                <span>User</span>
                                @if($sortBy === 'user_id')
                                    <svg class="w-4 h-4 {{ $sortDirection === 'asc' ? 'transform rotate-180' : '' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                @endif
                            </div>
                        </th>
                        <th
                            wire:click="sortBy('action')"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                        >
                            <div class="flex items-center space-x-1">
                                <span>Action</span>
                                @if($sortBy === 'action')
                                    <svg class="w-4 h-4 {{ $sortDirection === 'asc' ? 'transform rotate-180' : '' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                @endif
                            </div>
                        </th>
                        <th
                            wire:click="sortBy('entity_type')"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                        >
                            <div class="flex items-center space-x-1">
                                <span>Entity</span>
                                @if($sortBy === 'entity_type')
                                    <svg class="w-4 h-4 {{ $sortDirection === 'asc' ? 'transform rotate-180' : '' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                @endif
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Description
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            IP Address
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($logs as $log)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <div class="flex flex-col">
                                    <span class="font-medium">{{ $log->created_at->format('M d, Y') }}</span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ $log->created_at->format('H:i:s') }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @if($log->user)
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8">
                                            <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-medium">
                                                {{ $log->user->initials() }}
                                            </div>
                                        </div>
                                        <div class="ml-3">
                                            <div class="font-medium">{{ $log->user->name }}</div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ $log->user->role->label() }}</div>
                                        </div>
                                    </div>
                                @else
                                    <span class="text-gray-500 dark:text-gray-400 italic">System</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    @switch($log->action)
                                        @case('created')
                                            bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                            @break
                                        @case('updated')
                                            bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400
                                            @break
                                        @case('deleted')
                                            bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                            @break
                                        @case('login')
                                            bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400
                                            @break
                                        @case('logout')
                                            bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                            @break
                                        @default
                                            bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                    @endswitch
                                ">
                                    {{ ucfirst($log->action) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @if($log->entity_type)
                                    <div class="flex flex-col">
                                        <span class="font-medium">{{ $log->entity_type }}</span>
                                        @if($log->entity_id)
                                            <span class="text-xs text-gray-500 dark:text-gray-400">ID: {{ $log->entity_id }}</span>
                                        @endif
                                    </div>
                                @else
                                    <span class="text-gray-500 dark:text-gray-400">-</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                <div class="max-w-xs truncate" title="{{ $log->description }}">
                                    {{ $log->description }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $log->ip_address ?? '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button
                                    wire:click="showDetails({{ $log->id }})"
                                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                >
                                    View Details
                                </button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No activity logs found</h3>
                                    <p class="text-gray-500 dark:text-gray-400">Try adjusting your filters to see more results.</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($logs->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $logs->links() }}
            </div>
        @endif
    </div>

    <!-- Activity Log Details Modal -->
    @if($showDetailsModal && $selectedLog)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" wire:click="closeModal">
            <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800" wire:click.stop>
                <div class="mt-3">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Activity Log Details</h3>
                        <button
                            wire:click="closeModal"
                            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Modal Content -->
                    <div class="mt-6 space-y-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Basic Information</h4>
                                <dl class="space-y-2">
                                    <div>
                                        <dt class="text-xs font-medium text-gray-500 dark:text-gray-400">Timestamp</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $selectedLog->created_at->format('M d, Y H:i:s') }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-xs font-medium text-gray-500 dark:text-gray-400">Action</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                @switch($selectedLog->action)
                                                    @case('created')
                                                        bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                                        @break
                                                    @case('updated')
                                                        bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400
                                                        @break
                                                    @case('deleted')
                                                        bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                                        @break
                                                    @case('login')
                                                        bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400
                                                        @break
                                                    @case('logout')
                                                        bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                                        @break
                                                    @default
                                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                                @endswitch
                                            ">
                                                {{ ucfirst($selectedLog->action) }}
                                            </span>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-xs font-medium text-gray-500 dark:text-gray-400">Entity Type</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $selectedLog->entity_type ?? 'N/A' }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-xs font-medium text-gray-500 dark:text-gray-400">Entity ID</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $selectedLog->entity_id ?? 'N/A' }}</dd>
                                    </div>
                                </dl>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">User & System Information</h4>
                                <dl class="space-y-2">
                                    <div>
                                        <dt class="text-xs font-medium text-gray-500 dark:text-gray-400">User</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            @if($selectedLog->user)
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-6 w-6">
                                                        <div class="h-6 w-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-medium">
                                                            {{ $selectedLog->user->initials() }}
                                                        </div>
                                                    </div>
                                                    <div class="ml-2">
                                                        <div class="font-medium">{{ $selectedLog->user->name }}</div>
                                                        <div class="text-xs text-gray-500 dark:text-gray-400">{{ $selectedLog->user->role->label() }}</div>
                                                    </div>
                                                </div>
                                            @else
                                                <span class="text-gray-500 dark:text-gray-400 italic">System</span>
                                            @endif
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-xs font-medium text-gray-500 dark:text-gray-400">IP Address</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $selectedLog->ip_address ?? 'N/A' }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-xs font-medium text-gray-500 dark:text-gray-400">User Agent</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100 break-all">
                                            {{ $selectedLog->user_agent ? (strlen($selectedLog->user_agent) > 100 ? substr($selectedLog->user_agent, 0, 100) . '...' : $selectedLog->user_agent) : 'N/A' }}
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        <!-- Description -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Description</h4>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <p class="text-sm text-gray-900 dark:text-gray-100">{{ $selectedLog->description }}</p>
                            </div>
                        </div>

                        <!-- Changes (if available) -->
                        @if($selectedLog->old_values || $selectedLog->new_values)
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Changes</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    @if($selectedLog->old_values)
                                        <div>
                                            <h5 class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">Old Values</h5>
                                            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                                                <pre class="text-xs text-red-800 dark:text-red-200 whitespace-pre-wrap">{{ json_encode($selectedLog->old_values, JSON_PRETTY_PRINT) }}</pre>
                                            </div>
                                        </div>
                                    @endif

                                    @if($selectedLog->new_values)
                                        <div>
                                            <h5 class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">New Values</h5>
                                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                                                <pre class="text-xs text-green-800 dark:text-green-200 whitespace-pre-wrap">{{ json_encode($selectedLog->new_values, JSON_PRETTY_PRINT) }}</pre>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Modal Footer -->
                    <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
                        <button
                            wire:click="closeModal"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
