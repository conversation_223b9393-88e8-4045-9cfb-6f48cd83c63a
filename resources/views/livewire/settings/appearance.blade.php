<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Appearance')" :subheading=" __('Update the appearance settings for your account')">
        <div class="flex flex-col sm:flex-row gap-2 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <button 
                type="button"
                x-data="{ theme: 'light' }"
                x-init="$watch('$flux.appearance', val => theme = val)"
                x-bind:class="theme === 'light' ? 'bg-white dark:bg-gray-700 shadow-sm' : 'bg-transparent'"
                x-on:click="$flux.appearance = 'light'"
                class="flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
                {{ __('Light') }}
            </button>
            <button 
                type="button"
                x-data="{ theme: 'dark' }"
                x-init="$watch('$flux.appearance', val => theme = val)"
                x-bind:class="theme === 'dark' ? 'bg-white dark:bg-gray-700 shadow-sm' : 'bg-transparent'"
                x-on:click="$flux.appearance = 'dark'"
                class="flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                </svg>
                {{ __('Dark') }}
            </button>
            <button 
                type="button"
                x-data="{ theme: 'system' }"
                x-init="$watch('$flux.appearance', val => theme = val)"
                x-bind:class="theme === 'system' ? 'bg-white dark:bg-gray-700 shadow-sm' : 'bg-transparent'"
                x-on:click="$flux.appearance = 'system'"
                class="flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                {{ __('System') }}
            </button>
        </div>
    </x-settings.layout>
</section>
