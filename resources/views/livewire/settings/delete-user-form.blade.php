<section class="mt-10 space-y-6">
    <div class="relative mb-5">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{{ __('Delete account') }}</h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ __('Delete your account and all of its resources') }}</p>
    </div>

    <button 
        type="button"
        x-data="" 
        x-on:click.prevent="$dispatch('open-modal', 'confirm-user-deletion')"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors"
    >
        {{ __('Delete account') }}
    </button>

    <x-modal 
        name="confirm-user-deletion" 
        :show="$errors->isNotEmpty()" 
        maxWidth="lg"
    >
        <form wire:submit="deleteUser" class="space-y-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Are you sure you want to delete your account?') }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                    {{ __('Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.') }}
                </p>
            </div>

            <x-input 
                wire:model="password" 
                :label="__('Password')" 
                type="password" 
            />

            <div class="flex justify-end gap-2">
                <button 
                    type="button"
                    x-on:click="$dispatch('close-modal', 'confirm-user-deletion')"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors"
                >
                    {{ __('Cancel') }}
                </button>

                <x-button 
                    type="submit" 
                    variant="danger"
                >
                    {{ __('Delete account') }}
                </x-button>
            </div>
        </form>
    </x-modal>
</section>
