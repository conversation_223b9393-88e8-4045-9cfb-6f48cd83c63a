<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Update password')" :subheading="__('Ensure your account is using a long, random password to stay secure')">
        <form wire:submit="updatePassword" class="mt-6 space-y-6">
            <x-input
                wire:model="current_password"
                :label="__('Current password')"
                type="password"
                required
                autocomplete="current-password"
            />
            <x-input
                wire:model="password"
                :label="__('New password')"
                type="password"
                required
                autocomplete="new-password"
            />
            <x-input
                wire:model="password_confirmation"
                :label="__('Confirm Password')"
                type="password"
                required
                autocomplete="new-password"
            />

            <div class="flex items-center justify-between gap-4">
                <x-button 
                    type="submit" 
                    variant="primary"
                    class="min-w-[120px]"
                >
                    {{ __('Save') }}
                </x-button>

                @if (session('status') === 'password-updated')
                    <span class="text-sm text-green-600 dark:text-green-400 font-medium">
                        {{ __('Saved.') }}
                    </span>
                @endif
            </div>
        </form>
    </x-settings.layout>
</section>
