<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Profile')" :subheading="__('Update your name and email address')">
        <form wire:submit="updateProfileInformation" class="my-6 w-full space-y-6">
            <x-input 
                wire:model="name" 
                :label="__('Name')" 
                type="text" 
                required 
                autofocus 
                autocomplete="name" 
            />

            <div>
                <x-input 
                    wire:model="email" 
                    :label="__('Email')" 
                    type="email" 
                    required 
                    autocomplete="email" 
                />

                @if (auth()->user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail &&! auth()->user()->hasVerifiedEmail())
                    <div class="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <p class="text-sm text-yellow-800 dark:text-yellow-200">
                            {{ __('Your email address is unverified.') }}
                        </p>
                        <button 
                            type="button"
                            wire:click.prevent="resendVerificationNotification"
                            class="mt-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                        >
                            {{ __('Click here to re-send the verification email.') }}
                        </button>

                        @if (session('status') === 'verification-link-sent')
                            <p class="mt-2 text-sm font-medium text-green-600 dark:text-green-400">
                                {{ __('A new verification link has been sent to your email address.') }}
                            </p>
                        @endif
                    </div>
                @endif
            </div>

            <div class="flex items-center justify-between gap-4">
                <x-button 
                    type="submit" 
                    variant="primary"
                    class="min-w-[120px]"
                >
                    {{ __('Save') }}
                </x-button>

                @if (session('status') === 'profile-updated')
                    <span class="text-sm text-green-600 dark:text-green-400 font-medium">
                        {{ __('Saved.') }}
                    </span>
                @endif
            </div>
        </form>

        <livewire:settings.delete-user-form />
    </x-settings.layout>
</section>
