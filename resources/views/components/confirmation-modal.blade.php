@props([
    'show' => false,
    'title' => 'Confirm Action',
    'message' => 'Are you sure you want to perform this action?',
    'confirmText' => 'Confirm',
    'confirmColor' => 'red',
    'onConfirm' => '',
    'onCancel' => '',
    'icon' => 'warning',
    'name' => 'confirmation-modal'
])

@php
$colorClasses = [
    'red' => [
        'button' => 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
        'icon' => 'bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400',
    ],
    'blue' => [
        'button' => 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
        'icon' => 'bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400',
    ],
    'green' => [
        'button' => 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
        'icon' => 'bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400',
    ],
];

$colors = $colorClasses[$confirmColor] ?? $colorClasses['red'];

$icons = [
    'warning' => 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z',
    'question' => 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
    'info' => 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
];

$iconPath = $icons[$icon] ?? $icons['warning'];
@endphp

<x-ui.modal :show="$show" :title="$title" :name="$name" maxWidth="md">
    <div class="sm:flex sm:items-start">
        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10 {{ $colors['icon'] }}">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $iconPath }}" />
            </svg>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
            <div class="mt-2">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ $message }}
                </p>
            </div>
            
            @if(isset($details))
                <div class="mt-3">
                    {{ $details }}
                </div>
            @endif
        </div>
    </div>

    <x-slot name="footer">
        <button 
            wire:click="{{ $onConfirm }}" 
            type="button" 
            class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm {{ $colors['button'] }}"
        >
            {{ $confirmText }}
        </button>
        <button 
            wire:click="{{ $onCancel }}" 
            type="button" 
            class="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-600 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
        >
            Cancel
        </button>
    </x-slot>
</x-ui.modal>
