@props(['name', 'id' => null, 'value' => '', 'checked' => false, 'required' => false, 'disabled' => false, 'wire:model' => null, 'class' => ''])

@php
    $inputId = $id ?? $name;
    $checkboxClass = 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed ' . $class;

    // Handle wire model binding - use the attributes directly
    $wireModel = $attributes->get('wire:model');
@endphp

<div class="flex items-center">
    <input
        type="checkbox"
        id="{{ $inputId }}"
        name="{{ $name }}"
        value="{{ $value }}"
        {{ $checked ? 'checked' : '' }}
        {{ $required ? 'required' : '' }}
        {{ $disabled ? 'disabled' : '' }}
        class="{{ $checkboxClass }}"
        @if($wireModel) wire:model="{{ $wireModel }}" @endif
    />
    
    @if($name)
        <label for="{{ $inputId }}" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            {{ $name }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
    @endif
</div>

@error($name)
    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
@enderror