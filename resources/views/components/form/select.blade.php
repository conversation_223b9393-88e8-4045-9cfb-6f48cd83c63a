@props(['name', 'id' => null, 'options' => [], 'value' => '', 'required' => false, 'disabled' => false, 'placeholder' => 'Select an option', 'wire:model' => null, 'wire:model.lazy' => null, 'wire:model.live' => null, 'class' => ''])

@php
    $inputId = $id ?? $name;
    $selectClass = 'block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed ' . $class;

    // Handle wire model binding - use the attributes directly
    $wireModel = $attributes->get('wire:model');
    $wireModelLazy = $attributes->get('wire:model.lazy');
    $wireModelLive = $attributes->get('wire:model.live');
@endphp

<div>
    @if($name)
        <label for="{{ $inputId }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ $name }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif>
        </label>
    @endif
    
    <select
        id="{{ $inputId }}"
        name="{{ $name }}"
        {{ $required ? 'required' : '' }}
        {{ $disabled ? 'disabled' : '' }}
        class="{{ $selectClass }}"
        @if($wireModel) wire:model="{{ $wireModel }}" @endif
        @if($wireModelLazy) wire:model.lazy="{{ $wireModelLazy }}" @endif
        @if($wireModelLive) wire:model.live="{{ $wireModelLive }}" @endif
    >
        @if($placeholder)
            <option value="">{{ $placeholder }}</option>
        @endif
        
        @foreach($options as $key => $option)
            @if(is_array($option))
                <option value="{{ $option['value'] ?? $key }}" {{ (string) $value === (string) ($option['value'] ?? $key) ? 'selected' : '' }}>
                    {{ $option['label'] ?? $option }}
                </option>
            @else
                <option value="{{ $key }}" {{ (string) $value === (string) $key ? 'selected' : '' }}>
                    {{ $option }}
                </option>
            @endif
        @endforeach
    </select>
    
    @error($name)
        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
    @enderror
</div>