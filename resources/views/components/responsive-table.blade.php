@props([
    'headers' => [],
    'data' => [],
    'emptyMessage' => 'No data available',
    'emptyIcon' => 'document-text',
])

<div class="responsive-table">
    <!-- Desktop Table View -->
    <div class="hidden md:block">
        <table class="table">
            <thead>
                <tr>
                    @foreach($headers as $header)
                    <th class="{{ $header['class'] ?? '' }}">
                        @if(isset($header['sortable']) && $header['sortable'])
                            <div class="flex items-center space-x-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 px-2 py-1 rounded" 
                                 wire:click="{{ $header['sortAction'] ?? 'sortBy(\'' . $header['key'] . '\')' }}">
                                <span>{{ $header['label'] }}</span>
                                @if(isset($header['sorted']) && $header['sorted'])
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $header['sortDirection'] === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7' }}"></path>
                                    </svg>
                                @endif
                            </div>
                        @else
                            {{ $header['label'] }}
                        @endif
                    </th>
                    @endforeach
                </tr>
            </thead>
            <tbody>
                @forelse($data as $item)
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    @foreach($headers as $header)
                    <td class="{{ $header['cellClass'] ?? '' }}">
                        {{ $header['render']($item) ?? $item[$header['key']] ?? '' }}
                    </td>
                    @endforeach
                </tr>
                @empty
                <tr>
                    <td colspan="{{ count($headers) }}" class="text-center py-12">
                        <div class="text-gray-500 dark:text-gray-400">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-lg font-medium">{{ $emptyMessage }}</p>
                        </div>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Mobile Card View -->
    <div class="md:hidden space-y-4">
        @forelse($data as $item)
        <div class="mobile-card">
            <div class="mobile-card-header">
                <div>
                    <div class="mobile-card-title">{{ $headers[0]['render']($item) ?? $item[$headers[0]['key']] ?? '' }}</div>
                    <div class="mobile-card-subtitle">{{ $headers[1]['render']($item) ?? $item[$headers[1]['key']] ?? '' }}</div>
                </div>
                @if(isset($headers[count($headers)-1]['render']) && str_contains($headers[count($headers)-1]['class'] ?? '', 'text-right'))
                    <div>
                        {{ $headers[count($headers)-1]['render']($item) }}
                    </div>
                @endif
            </div>
            <div class="mobile-card-content">
                @foreach(array_slice($headers, 2, -1) as $header)
                <div class="mobile-card-row">
                    <span class="mobile-card-label">{{ $header['label'] }}</span>
                    <span class="mobile-card-value">{{ $header['render']($item) ?? $item[$header['key']] ?? '' }}</span>
                </div>
                @endforeach
            </div>
        </div>
        @empty
        <div class="mobile-card">
            <div class="text-center py-8">
                <div class="text-gray-500 dark:text-gray-400">
                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-lg font-medium">{{ $emptyMessage }}</p>
                </div>
            </div>
        </div>
        @endforelse
    </div>
</div>