@props([
    'show' => false,
    'title' => 'Assign Users',
    'users' => collect(),
    'selectedUserIds' => [],
    'selectedProperty' => 'selectedUserIds',
    'roleColor' => 'blue',
    'onAssign' => '',
    'onClose' => '',
    'emptyMessage' => 'No users available to assign.',
    'name' => 'assignment-modal'
])

@php
$colorClasses = [
    'blue' => [
        'checkbox' => 'text-blue-600 focus:border-blue-300 focus:ring-blue-200',
        'button' => 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
    ],
    'green' => [
        'checkbox' => 'text-green-600 focus:border-green-300 focus:ring-green-200',
        'button' => 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
    ],
    'yellow' => [
        'checkbox' => 'text-yellow-600 focus:border-yellow-300 focus:ring-yellow-200',
        'button' => 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
    ],
];

$colors = $colorClasses[$roleColor] ?? $colorClasses['blue'];
@endphp

<x-ui.modal :show="$show" :title="$title" :name="$name" maxWidth="lg">
    <div class="mt-4">
        @if($users->count() > 0)
            <div class="space-y-2 max-h-60 overflow-y-auto">
                @foreach($users as $user)
                    <label class="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg border border-transparent hover:border-gray-200 dark:hover:border-gray-600 transition-colors cursor-pointer">
                        <input
                            type="checkbox"
                            wire:model="{{ $selectedProperty }}"
                            value="{{ $user->id }}"
                            class="rounded border-gray-300 shadow-sm focus:ring focus:ring-offset-0 focus:ring-opacity-50 {{ $colors['checkbox'] }}"
                        >
                        <div class="ml-3 flex-1">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $user->name }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ $user->email }}</div>
                        </div>
                    </label>
                @endforeach
            </div>
            @error($selectedProperty)
                <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
            @enderror
        @else
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.196-2.121M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.196-2.121M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">{{ $emptyMessage }}</p>
            </div>
        @endif
    </div>

    <x-slot name="footer">
        @if($users->count() > 0)
            <button 
                wire:click="{{ $onAssign }}" 
                type="button" 
                class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm {{ $colors['button'] }}"
            >
                Assign Selected
            </button>
        @endif
        <button 
            wire:click="{{ $onClose }}" 
            type="button" 
            class="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-600 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
        >
            Cancel
        </button>
    </x-slot>
</x-ui.modal>
