@props([
    'title',
    'value',
    'icon' => null,
    'iconColor' => 'blue',
    'trend' => null,
    'trendDirection' => null,
    'subtitle' => null,
    'loading' => false,
    'href' => null,
    'size' => 'default'
])

@php
$iconColorClasses = [
    'blue' => 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400',
    'teal' => 'bg-teal-100 text-teal-600 dark:bg-teal-900 dark:text-teal-400',
    'green' => 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400',
    'yellow' => 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-400',
    'red' => 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400',
    'purple' => 'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-400',
];

$trendClasses = [
    'up' => 'text-green-600 dark:text-green-400',
    'down' => 'text-red-600 dark:text-red-400',
    'neutral' => 'text-gray-500 dark:text-gray-400',
];

$sizeClasses = [
    'sm' => 'p-4',
    'default' => 'p-6',
    'lg' => 'p-8',
];

$component = $href ? 'a' : 'div';
$componentProps = $href ? ['href' => $href, 'wire:navigate' => true] : [];
@endphp

<{{ $component }} 
    {{ $attributes->merge([
        'class' => 'block rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800 ' . 
                  ($sizeClasses[$size] ?? $sizeClasses['default']) . 
                  ($href ? ' hover:shadow-md transition-shadow duration-200' : '')
    ])->merge($componentProps) }}
>
    @if($loading)
        <!-- Loading State -->
        <div class="animate-pulse">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                </div>
                <div class="ml-4 flex-1">
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-2"></div>
                    <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                </div>
            </div>
        </div>
    @else
        <div class="flex items-center">
            @if($icon)
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 rounded-lg flex items-center justify-center {{ $iconColorClasses[$iconColor] ?? $iconColorClasses['blue'] }}">
                        @if(is_string($icon))
                            {!! $icon !!}
                        @else
                            {{ $icon }}
                        @endif
                    </div>
                </div>
            @endif
            
            <div class="{{ $icon ? 'ml-4' : '' }} flex-1 min-w-0">
                <div class="flex items-center justify-between">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{{ $title }}</p>
                    @if($trend && $trendDirection)
                        <div class="flex items-center ml-2">
                            <span class="text-xs font-medium {{ $trendClasses[$trendDirection] ?? $trendClasses['neutral'] }}">
                                @if($trendDirection === 'up')
                                    <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L10 4.414 4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                @elseif($trendDirection === 'down')
                                    <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L10 15.586l5.293-5.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                @else
                                    <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                                    </svg>
                                @endif
                                {{ $trend }}
                            </span>
                        </div>
                    @endif
                </div>
                
                <div class="mt-1">
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $value }}</p>
                    @if($subtitle)
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ $subtitle }}</p>
                    @endif
                </div>
            </div>
        </div>
        
        @if($href)
            <div class="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </div>
        @endif
    @endif
</{{ $component }}>
