@props([
    'headers' => [],
    'rows' => [],
    'searchable' => true,
    'sortable' => true,
    'filterable' => false,
    'exportable' => false,
    'selectable' => false,
    'loading' => false,
    'emptyMessage' => 'No data available',
    'searchPlaceholder' => 'Search...',
    'perPage' => 10,
    'showPerPage' => true
])

<div {{ $attributes->merge(['class' => 'bg-white rounded-lg border border-gray-200 shadow-sm dark:bg-gray-800 dark:border-gray-700']) }}>
    <!-- Table Header with Search and Filters -->
    @if($searchable || $filterable || $exportable)
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <!-- Search -->
                @if($searchable)
                    <div class="relative flex-1 max-w-md">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <input 
                            type="text" 
                            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="{{ $searchPlaceholder }}"
                            wire:model.live.debounce.300ms="search"
                        >
                    </div>
                @endif

                <!-- Actions -->
                <div class="flex items-center gap-3">
                    @if($filterable)
                        <!-- Filter Button -->
                        <button 
                            type="button"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            data-modal-open="filter-modal"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
                            </svg>
                            Filter
                        </button>
                    @endif

                    @if($exportable)
                        <!-- Export Button -->
                        <div class="relative">
                            <button 
                                type="button"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                data-dropdown-toggle="export-dropdown"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Export
                                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    @endif

                    {{ $actions ?? '' }}
                </div>
            </div>
        </div>
    @endif

    <!-- Table -->
    <div class="overflow-x-auto">
        @if($loading)
            <!-- Loading State -->
            <div class="p-8">
                <div class="animate-pulse">
                    <div class="grid grid-cols-{{ count($headers) }} gap-4 mb-4">
                        @foreach($headers as $header)
                            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        @endforeach
                    </div>
                    @for($i = 0; $i < 5; $i++)
                        <div class="grid grid-cols-{{ count($headers) }} gap-4 mb-3">
                            @foreach($headers as $header)
                                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                            @endforeach
                        </div>
                    @endfor
                </div>
            </div>
        @elseif(empty($rows))
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">{{ $emptyMessage }}</h3>
            </div>
        @else
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        @if($selectable)
                            <th scope="col" class="w-4 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                <input 
                                    type="checkbox" 
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    wire:model="selectAll"
                                >
                            </th>
                        @endif
                        
                        @foreach($headers as $key => $header)
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider {{ $sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600' : '' }}">
                                <div class="flex items-center gap-1">
                                    <span>{{ is_array($header) ? $header['label'] : $header }}</span>
                                    @if($sortable)
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                                        </svg>
                                    @endif
                                </div>
                            </th>
                        @endforeach
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                    @foreach($rows as $row)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                            @if($selectable)
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input 
                                        type="checkbox" 
                                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        wire:model="selected"
                                        value="{{ $row['id'] ?? $loop->index }}"
                                    >
                                </td>
                            @endif
                            
                            @foreach($headers as $key => $header)
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                                    @if(is_array($header) && isset($header['component']))
                                        <!-- Custom component for this column -->
                                        @include($header['component'], ['value' => $row[$key] ?? '', 'row' => $row])
                                    @else
                                        {{ $row[$key] ?? '' }}
                                    @endif
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @endif
    </div>

    <!-- Pagination and Per Page -->
    @if($showPerPage || isset($pagination))
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                @if($showPerPage)
                    <div class="flex items-center gap-2">
                        <label class="text-sm text-gray-700 dark:text-gray-300">Show:</label>
                        <select 
                            class="rounded border-gray-300 text-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600"
                            wire:model.live="perPage"
                        >
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span class="text-sm text-gray-700 dark:text-gray-300">entries</span>
                    </div>
                @endif

                @if(isset($pagination))
                    <div>
                        {{ $pagination }}
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>

<!-- Status Badge Component for table cells -->
@if(!function_exists('statusBadge'))
    @php
    function statusBadge($status) {
        $classes = [
            'approved' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
            'rejected' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
            'draft' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        ];
        
        return '<span class="px-2 py-1 text-xs font-medium rounded-full ' . ($classes[$status] ?? 'bg-gray-100 text-gray-800') . '">' . ucfirst($status) . '</span>';
    }
    @endphp
@endif
