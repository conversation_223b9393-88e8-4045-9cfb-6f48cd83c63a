@props([
    'id',
    'type' => 'consumption',
    'title' => null,
    'data' => null,
    'height' => '320',
    'loading' => false,
    'lazy' => false,
    'options' => []
])

@php
$chartId = $id ?? 'chart-' . uniqid();
$canvasHeight = is_numeric($height) ? $height . 'px' : $height;
@endphp

<div {{ $attributes->merge(['class' => 'bg-white rounded-lg border border-gray-200 shadow-sm p-6 dark:bg-gray-800 dark:border-gray-700']) }}>
    @if($title)
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $title }}</h3>
            <div class="flex items-center gap-2">
                {{ $controls ?? '' }}
            </div>
        </div>
    @endif

    <div class="relative" style="height: {{ $canvasHeight }}">
        @if($loading)
            <!-- Loading State -->
            <div class="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex flex-col items-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading chart...</p>
                </div>
            </div>
        @else
            <canvas 
                id="{{ $chartId }}"
                @if($lazy)
                    data-lazy-chart="true"
                    data-chart-type="{{ $type }}"
                    data-chart-data="{{ $data ? json_encode($data) : '{}' }}"
                @endif
                class="w-full h-full"
                role="img"
                aria-label="{{ $title ? $title . ' chart' : 'Data visualization chart' }}"
            ></canvas>
        @endif
    </div>

    @if(!$lazy && $data)
        <!-- Initialize chart immediately if not lazy loading -->
        @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const chartData = @json($data);
                const chartOptions = @json($options);
                
                if (window.ChartManager) {
                    window.ChartManager.createChart('{{ $chartId }}', '{{ $type }}', chartData, chartOptions);
                } else {
                    console.warn('ChartManager not available. Make sure app.js is loaded.');
                }
            });

            // Update chart when Livewire updates
            document.addEventListener('livewire:init', function() {
                Livewire.hook('message.processed', function() {
                    if (window.ChartManager) {
                        const chartData = @json($data);
                        window.ChartManager.updateChart('{{ $chartId }}', chartData);
                    }
                });
            });
        </script>
        @endpush
    @endif

    <!-- Error State -->
    <div id="{{ $chartId }}-error" class="hidden absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Chart unavailable</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Unable to load chart data</p>
        </div>
    </div>
</div>

@if($lazy)
    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Lazy loading is handled by the PerformanceMonitor in app.js
        });
    </script>
    @endpush
@endif
