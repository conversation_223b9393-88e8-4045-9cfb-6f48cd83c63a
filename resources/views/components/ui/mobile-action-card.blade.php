@props([
    'title',
    'subtitle' => null,
    'icon' => null,
    'iconColor' => 'blue',
    'status' => null,
    'progress' => null,
    'href' => null,
    'action' => null,
    'disabled' => false,
    'urgent' => false
])

@php
$iconColorClasses = [
    'blue' => 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400',
    'teal' => 'bg-teal-100 text-teal-600 dark:bg-teal-900 dark:text-teal-400',
    'green' => 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400',
    'yellow' => 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-400',
    'red' => 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400',
];

$statusClasses = [
    'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'completed' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'overdue' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'draft' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
];

$component = $href ? 'a' : ($action ? 'button' : 'div');
$componentProps = [];

if ($href) {
    $componentProps['href'] = $href;
    $componentProps['wire:navigate'] = true;
} elseif ($action) {
    $componentProps['wire:click'] = $action;
    $componentProps['type'] = 'button';
}

if ($disabled) {
    $componentProps['disabled'] = true;
}
@endphp

<{{ $component }}
    {{ $attributes->merge([
        'class' => 'touch-target block w-full bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 transition-all duration-200 ' . 
                   ($href || $action ? 'hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 active:scale-[0.98] ' : '') .
                   ($disabled ? 'opacity-50 cursor-not-allowed ' : '') .
                   ($urgent ? 'ring-2 ring-red-200 dark:ring-red-800 border-red-300 dark:border-red-600 ' : '')
    ])->merge($componentProps) }}
>
    <div class="flex items-center gap-4">
        @if($icon)
            <div class="flex-shrink-0">
                <div class="w-12 h-12 rounded-lg flex items-center justify-center {{ $iconColorClasses[$iconColor] ?? $iconColorClasses['blue'] }}">
                    @if(is_string($icon))
                        {!! $icon !!}
                    @else
                        {{ $icon }}
                    @endif
                </div>
            </div>
        @endif

        <div class="flex-1 min-w-0">
            <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                    <h3 class="text-base font-semibold text-gray-900 dark:text-white truncate">
                        {{ $title }}
                    </h3>
                    @if($subtitle)
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                            {{ $subtitle }}
                        </p>
                    @endif
                </div>

                @if($status)
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusClasses[$status] ?? $statusClasses['pending'] }}">
                        {{ ucfirst($status) }}
                    </span>
                @endif
            </div>

            @if($progress !== null)
                <div class="mt-3">
                    <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                        <span>Progress</span>
                        <span>{{ $progress }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style="width: '{{ $progress }}%'"
                        ></div>
                    </div>
                </div>
            @endif
        </div>

        @if($href || $action)
            <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </div>
        @endif
    </div>

    @if($urgent)
        <!-- Urgent indicator -->
        <div class="absolute top-2 right-2">
            <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
        </div>
    @endif
</{{ $component }}>
