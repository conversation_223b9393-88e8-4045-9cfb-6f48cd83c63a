@props([
    'items' => [],
    'separator' => '/',
    'showHome' => true
])

<nav aria-label="Breadcrumb" class="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
    @if($showHome)
        <a 
            href="{{ route('dashboard') }}" 
            wire:navigate
            class="flex items-center hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
        >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <span class="sr-only">{{ __('Home') }}</span>
        </a>
        
        @if(count($items) > 0)
            <span class="text-gray-400 dark:text-gray-500">{{ $separator }}</span>
        @endif
    @endif

    @foreach($items as $index => $item)
        @if($loop->last)
            <span class="text-gray-900 dark:text-gray-100 font-medium" aria-current="page">
                {{ $item['label'] }}
            </span>
        @else
            <a 
                href="{{ $item['url'] ?? '#' }}" 
                wire:navigate
                class="hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
            >
                {{ $item['label'] }}
            </a>
            <span class="text-gray-400 dark:text-gray-500">{{ $separator }}</span>
        @endif
    @endforeach
</nav>
