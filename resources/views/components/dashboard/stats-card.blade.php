@props([
    'title' => '',           // Required: Card title
    'value' => '',           // Required: Main value to display
    'icon' => '',           // Optional: SVG icon markup
    'trend' => null,        // Optional: 'up', 'down', or null
    'trendValue' => null,   // Optional: Trend percentage or value
    'type' => 'default',    // Optional: 'default', 'currency', 'percentage'
    'loading' => false,     // Optional: Whether to show loading state
])

@php
    $trendColor = match($trend) {
        'up' => 'text-green-600',
        'down' => 'text-red-600',
        default => 'text-gray-500',
    };
    
    $trendIcon = match($trend) {
        'up' => '<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /></svg>',
        'down' => '<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" /></svg>',
        default => '',
    };
    
    $formattedValue = match($type) {
        'currency' => 'KES ' . number_format($value, 2),
        'percentage' => $value . '%',
        default => $value,
    };
@endphp

<div class="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
    <div class="p-5">
        @if($loading)
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2 dark:bg-gray-700"></div>
                <div class="h-8 bg-gray-200 rounded w-1/2 dark:bg-gray-700"></div>
            </div>
        @else
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    @if($icon)
                        <div class="h-6 w-6 text-gray-400">
                            {!! $icon !!}
                        </div>
                    @endif
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                            {{ $title }}
                        </dt>
                        <dd class="flex items-baseline">
                            <div class="text-2xl font-semibold text-gray-900 dark:text-white">
                                {{ $formattedValue }}
                            </div>
                            @if($trend && $trendValue)
                                <div class="ml-2 flex items-baseline text-sm font-semibold {{ $trendColor }}">
                                    {!! $trendIcon !!}
                                    {{ $trendValue }}
                                </div>
                            @endif
                        </dd>
                    </dl>
                </div>
            </div>
        @endif
    </div>
</div>