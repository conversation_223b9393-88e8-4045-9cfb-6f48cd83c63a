@props(['roles' => [], 'showEditModal' => false])

<div class="space-y-4">
    <div>
        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Full Name</label>
        <input wire:model="name" id="name" type="text" placeholder="Enter full name" class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white" />
        @error('name') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
    </div>
    <div>
        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email Address</label>
        <input wire:model="email" id="email" type="email" placeholder="Enter email address" class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white" />
        @error('email') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
    </div>
    <div>
        <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Role</label>
        <select wire:model="role" id="role" class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            @foreach($roles as $role)
                <option value="{{ $role->value }}">{{ $role->label() }}</option>
            @endforeach
        </select>
        @error('role') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
    </div>
    <div>
        <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ $showEditModal ? 'New Password (optional)' : 'Password' }}</label>
        <input wire:model="password" id="password" type="password" placeholder="{{ $showEditModal ? 'Leave blank to keep current password' : 'Enter password' }}" class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white" />
        @error('password') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
    </div>
    <div>
        <label for="passwordConfirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Confirm Password</label>
        <input wire:model="passwordConfirmation" id="passwordConfirmation" type="password" placeholder="Confirm password" class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white" />
    </div>
</div>
