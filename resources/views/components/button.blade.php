@props([
    'variant' => 'primary',
    'size' => 'md',
    'disabled' => false,
    'type' => 'button',
    'icon' => null,
    'iconPosition' => 'left',
])

@php
    $variants = [
        'primary' => 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500 disabled:bg-blue-300',
        'secondary' => 'bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-500 disabled:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-100 dark:disabled:bg-gray-800',
        'success' => 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500 disabled:bg-green-300',
        'danger' => 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500 disabled:bg-red-300',
        'warning' => 'bg-yellow-500 hover:bg-yellow-600 text-white focus:ring-yellow-500 disabled:bg-yellow-300',
        'ghost' => 'border border-gray-300 bg-transparent hover:bg-gray-50 text-gray-700 focus:ring-gray-500 disabled:opacity-50 dark:border-gray-600 dark:hover:bg-gray-800 dark:text-gray-300',
        'link' => 'text-blue-600 hover:text-blue-700 hover:underline focus:ring-blue-500 disabled:opacity-50 dark:text-blue-400 dark:hover:text-blue-300',
    ];

    $sizes = [
        'xs' => 'px-2 py-1 text-xs',
        'sm' => 'px-3 py-1.5 text-sm',
        'md' => 'px-4 py-2 text-sm',
        'lg' => 'px-6 py-3 text-base',
        'xl' => 'px-8 py-4 text-lg',
    ];

    $baseClasses = 'inline-flex items-center justify-center font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-md disabled:cursor-not-allowed';
    $variantClasses = $variants[$variant] ?? $variants['primary'];
    $sizeClasses = $sizes[$size] ?? $sizes['md'];
@endphp

<button
    type="{{ $type }}"
    {{ $disabled ? 'disabled' : '' }}
    {{ $attributes->merge(['class' => "$baseClasses $variantClasses $sizeClasses"]) }}
>
    @if ($icon && $iconPosition === 'left')
        <span class="mr-2">{{ $icon }}</span>
    @endif
    
    {{ $slot }}
    
    @if ($icon && $iconPosition === 'right')
        <span class="ml-2">{{ $icon }}</span>
    @endif
</button>