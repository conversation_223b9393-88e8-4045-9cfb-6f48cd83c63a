@props([
    'variant' => 'ghost',
    'size' => 'md',
    'disabled' => false,
    'type' => 'button',
    'tooltip' => null,
])

@php
    $variants = [
        'primary' => 'text-blue-600 hover:bg-blue-50 focus:ring-blue-500 disabled:text-blue-300 dark:text-blue-400 dark:hover:bg-blue-900/50',
        'secondary' => 'text-gray-600 hover:bg-gray-100 focus:ring-gray-500 disabled:text-gray-300 dark:text-gray-400 dark:hover:bg-gray-800',
        'success' => 'text-green-600 hover:bg-green-50 focus:ring-green-500 disabled:text-green-300 dark:text-green-400 dark:hover:bg-green-900/50',
        'danger' => 'text-red-600 hover:bg-red-50 focus:ring-red-500 disabled:text-red-300 dark:text-red-400 dark:hover:bg-red-900/50',
        'warning' => 'text-yellow-600 hover:bg-yellow-50 focus:ring-yellow-500 disabled:text-yellow-300 dark:text-yellow-400 dark:hover:bg-yellow-900/50',
        'ghost' => 'text-gray-500 hover:bg-gray-100 focus:ring-gray-500 disabled:text-gray-300 dark:text-gray-400 dark:hover:bg-gray-800',
    ];

    $sizes = [
        'xs' => 'p-1',
        'sm' => 'p-1.5',
        'md' => 'p-2',
        'lg' => 'p-3',
        'xl' => 'p-4',
    ];

    $baseClasses = 'inline-flex items-center justify-center transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-md disabled:cursor-not-allowed';
    $variantClasses = $variants[$variant] ?? $variants['ghost'];
    $sizeClasses = $sizes[$size] ?? $sizes['md'];
@endphp

<div class="relative inline-block">
    <button
        type="{{ $type }}"
        {{ $disabled ? 'disabled' : '' }}
        {{ $attributes->merge(['class' => "$baseClasses $variantClasses $sizeClasses"]) }}
        @if ($tooltip)
            x-data="{ tooltipOpen: false }"
            @mouseenter="tooltipOpen = true"
            @mouseleave="tooltipOpen = false"
        @endif
    >
        {{ $slot }}
    </button>
    
    @if ($tooltip)
        <div
            x-show="tooltipOpen"
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 scale-95"
            x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-150"
            x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-95"
            class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded-md whitespace-nowrap z-50"
        >
            {{ $tooltip }}
            <div class="absolute top-full left-1/2 transform -translate-x-1/2 -mt-1">
                <div class="border-4 border-transparent border-t-gray-900"></div>
            </div>
        </div>
    @endif
</div>