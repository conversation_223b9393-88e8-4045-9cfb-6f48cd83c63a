<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title ?? 'Water Management System' }}</title>
    <meta name="description" content="Water Management System - TailAdmin CRM Dashboard">
    
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
</head>
<body 
    x-data="{ 
        darkMode: JSON.parse(localStorage.getItem('darkMode') || 'false'),
        loaded: true
    }"
    x-init="
        $watch('darkMode', value => localStorage.setItem('darkMode', JSON.stringify(value)));
        window.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => loaded = false, 500)
        })
    "
    :class="{'dark bg-gray-900': darkMode === true}"
    class="h-full bg-gray-50 dark:bg-gray-900"
>
    <!-- Preloader -->
    <div x-show="loaded" 
         class="fixed left-0 top-0 z-50 flex h-screen w-screen items-center justify-center bg-white dark:bg-black">
        <div class="h-16 w-16 animate-spin rounded-full border-4 border-solid border-blue-500 border-t-transparent"></div>
    </div>

    <!-- Main Content -->
    <main class="flex items-center justify-center min-h-screen p-4">
        {{ $slot }}
    </main>

    @livewireScripts
</body>
</html>
