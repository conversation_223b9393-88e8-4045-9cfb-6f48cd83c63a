@props(['title' => null])

<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
x-data="{ 
           darkMode: localStorage.getItem('darkMode') === 'true',
           sidebarToggle: true
       }"
       x-init="
           $watch('darkMode', val => localStorage.setItem('darkMode', val)); 
           $watch('sidebarToggle', val => localStorage.setItem('sidebarToggle', val ? 'expanded' : 'collapsed'));
           // Initialize from localStorage
           if (localStorage.getItem('sidebarToggle') === 'collapsed') {
               sidebarToggle = false;
           }
       "      :class="{ 'dark': darkMode }"
>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $title ?? config('app.name', 'Water Management') }}</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-gray-100 font-sans antialiased dark:bg-gray-900">
    <div class="flex h-screen bg-gray-100 dark:bg-gray-900">
<!-- Dynamic Role-Based Sidebar -->
        @php
            $user = auth()->user();
            $sidebarComponent = 'navigation.sidebar.resident-sidebar'; // Default fallback
            
            if ($user->hasRole(\App\Enums\UserRole::ADMIN)) {
                $sidebarComponent = 'navigation.sidebar.admin-sidebar';
            } elseif ($user->hasRole(\App\Enums\UserRole::MANAGER)) {
                $sidebarComponent = 'navigation.sidebar.manager-sidebar';
            } elseif ($user->hasRole(\App\Enums\UserRole::REVIEWER)) {
                $sidebarComponent = 'navigation.sidebar.reviewer-sidebar';
            } elseif ($user->hasRole(\App\Enums\UserRole::CARETAKER)) {
                $sidebarComponent = 'navigation.sidebar.caretaker-sidebar';
            }
        @endphp

        <x-dynamic-component :component="$sidebarComponent" :title="$title" />

        <!-- Main Content -->
        <div class="flex flex-1 flex-col">
            <!-- Header -->
            <header class="sticky top-0 z-30 flex h-16 items-center gap-4 border-b border-gray-200 bg-white px-4 dark:border-gray-800 dark:bg-gray-900 sm:px-6">
                <!-- Sidebar Toggle -->
                <button
                    class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    @click="sidebarToggle = !sidebarToggle"
                >
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>

                <!-- Search -->
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <input type="search" placeholder="Search..." 
                               class="block w-full rounded-lg border border-gray-300 bg-gray-50 pl-10 pr-4 py-2 text-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                    </div>
                </div>

                <!-- User Menu -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center space-x-2 p-2">
                        <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                {{ substr(auth()->user()->name, 0, 2) }}
                            </span>
                        </div>
                        <span class="hidden sm:block text-sm font-medium text-gray-700 dark:text-gray-300">{{ auth()->user()->name }}</span>
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>

                    <div x-show="open" @click.away="open = false" 
                         class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1">
                        <a href="{{ route('settings.profile') }}" 
                           class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            Profile
                        </a>
                        <a href="{{ route('settings.password') }}" 
                           class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            Settings
                        </a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" 
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </header>

            <!-- Main content -->
            <main class="flex-1 overflow-y-auto overflow-x-hidden bg-gray-50 p-4 dark:bg-gray-800/50">
                <div class="mx-auto max-w-screen-2xl">
                    {{ $slot }}
                </div>
            </main>
        </div>
    </div>
</body>
</html>

