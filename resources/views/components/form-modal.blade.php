@props([
    'show' => false,
    'title' => 'Form',
    'onSave' => '',
    'onClose' => '',
    'saveText' => 'Save',
    'maxWidth' => 'lg',
    'name' => 'form-modal'
])

<x-ui.modal :show="$show" :title="$title" :name="$name" :maxWidth="$maxWidth">
    <div class="mt-4">
        {{ $slot }}
    </div>

    <x-slot name="footer">
        <button 
            wire:click="{{ $onSave }}" 
            type="button" 
            class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
        >
            {{ $saveText }}
        </button>
        <button 
            wire:click="{{ $onClose }}" 
            type="button" 
            class="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-600 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
        >
            Cancel
        </button>
    </x-slot>
</x-ui.modal>
