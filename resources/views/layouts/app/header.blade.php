@props(['title' => null])

<header class="sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 bg-white px-4 shadow-sm dark:border-gray-800 dark:bg-gray-900 sm:px-6">
    <!-- Mobile sidebar toggle -->
    <button @click="sidebarToggle = !sidebarToggle" class="mr-2 lg:hidden">
        <svg class="h-6 w-6 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
    </button>

    <!-- Logo and title -->
    <div class="flex items-center">
        <a href="{{ route('dashboard') }}" class="flex items-center space-x-3">
            <x-application-logo class="h-8 w-auto" />
            <span class="hidden text-lg font-semibold text-gray-800 dark:text-white/90 sm:block">
                {{ config('app.name', 'Water Management') }}
            </span>
        </a>
    </div>

    <!-- Breadcrumbs -->
    <div class="hidden items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 lg:flex lg:ml-4">
        <span>/</span>
        <span class="text-gray-700 dark:text-gray-300">
            @if(request()->routeIs('dashboard'))
                {{ __('Dashboard') }}
            @elseif(request()->routeIs('estates*'))
                {{ __('Estates') }}
            @elseif(request()->routeIs('houses*'))
                {{ __('Houses') }}
            @elseif(request()->routeIs('contacts*'))
                {{ __('Contacts') }}
            @elseif(request()->routeIs('readings*'))
                {{ __('Water Readings') }}
            @elseif(request()->routeIs('billing*'))
                {{ __('Billing') }}
            @elseif(request()->routeIs('reports*'))
                {{ __('Reports') }}
            @elseif(request()->routeIs('admin*'))
                {{ __('Administration') }}
            @endif
        </span>
    </div>

    <div class="ml-auto flex items-center space-x-4">
        <!-- Search -->
        <button class="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100 text-gray-500 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        </button>

        <!-- Notifications -->
        @can('view-notifications')
        <div class="relative">
            <button class="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100 text-gray-500 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                @if(isset($unreadNotifications) && $unreadNotifications > 0)
                    <span class="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs text-white">
                        {{ $unreadNotifications > 9 ? '9+' : $unreadNotifications }}
                    </span>
                @endif
            </button>
        </div>
        @endcan

        <!-- Theme toggle -->
        <button 
            @click="darkMode = !darkMode" 
            class="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100 text-gray-500 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
        >
            <svg x-show="!darkMode" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <svg x-show="darkMode" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
        </button>

        <!-- User dropdown -->
        <div class="relative" x-data="{ open: false }">
            <button 
                @click="open = !open"
                class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
                {{ auth()->user()->initials() }}
            </button>

            <div 
                x-show="open"
                @click.away="open = false"
                class="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800"
            >
                <a 
                    href="{{ route('settings.profile') }}" 
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                >
                    {{ __('Settings') }}
                </a>
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button 
                        type="submit"
                        class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                    >
                        {{ __('Log Out') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</header>
