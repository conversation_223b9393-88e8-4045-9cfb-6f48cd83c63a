@props(['placeholder' => null, 'wireModelLive' => null, 'class' => ''])
<select
    @if($placeholder) placeholder="{{ $placeholder }}" @endif
    @if($wireModelLive) wire:model.live="{{ $wireModelLive }}" @endif
    {{ $attributes->merge([
        'class' => 'block w-full rounded-lg border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-zinc-900 dark:text-white shadow-sm focus:border-water-500 focus:ring-water-500 sm:text-sm ' . $class
    ]) }}
>
    {{ $slot }}
</select>