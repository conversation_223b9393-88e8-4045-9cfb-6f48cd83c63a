@props(['size' => 'base'])
@php
    $sizes = [
        'xs' => 'text-xs font-medium',
        'sm' => 'text-sm font-medium',
        'base' => 'text-base font-semibold',
        'lg' => 'text-lg font-semibold',
        'xl' => 'text-xl font-bold',
        '2xl' => 'text-2xl font-bold',
    ];
    
    $sizeClass = $sizes[$size] ?? $sizes['base'];
@endphp

<h{{ match($size) {
    'xs', 'sm' => 3,
    'base', 'lg' => 2,
    default => 1,
} }} {{ $attributes->merge(['class' => $sizeClass . ' text-zinc-900 dark:text-white']) }}>
    {{ $slot }}
</h{{ match($size) {
    'xs', 'sm' => 3,
    'base', 'lg' => 2,
    default => 1,
} }}>