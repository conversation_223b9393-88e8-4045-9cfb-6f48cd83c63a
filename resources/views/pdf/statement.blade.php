<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Statement</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .company-info {
            font-size: 10px;
            color: #666;
        }
        .statement-title {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        .info-label {
            font-weight: bold;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }
        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        .summary-item.total {
            border-top: 1px solid #333;
            padding-top: 8px;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .amount {
            text-align: right;
        }
        .status {
            text-align: center;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .status.paid {
            background-color: #d4edda;
            color: #155724;
        }
        .status.sent {
            background-color: #cce5ff;
            color: #004085;
        }
        .status.overdue {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.draft {
            background-color: #fff3cd;
            color: #856404;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ $company['name'] }}</div>
        <div class="company-info">
            {{ $company['address'] }} | Phone: {{ $company['phone'] }} | Email: {{ $company['email'] }}
        </div>
    </div>

    <!-- Statement Title -->
    <div class="statement-title">CUSTOMER STATEMENT</div>

    <!-- Customer Information -->
    <div class="section">
        <div class="section-title">Customer Information</div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">House Number:</span>
                <span>{{ $house->house_number }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Estate:</span>
                <span>{{ $house->estate->name }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Contact Name:</span>
                <span>{{ $house->contact?->name ?? 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Phone:</span>
                <span>{{ $house->contact?->phone ?? 'N/A' }}</span>
            </div>
        </div>
        <div class="info-item">
            <span class="info-label">Statement Period:</span>
            <span>{{ \Carbon\Carbon::parse($period['start'])->format('M d, Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('M d, Y') }}</span>
        </div>
    </div>

    <!-- Account Summary -->
    <div class="section">
        <div class="section-title">Account Summary</div>
        <div class="summary-grid">
            <div class="summary-item">
                <span>Opening Balance:</span>
                <span class="amount">{{ number_format($summary['opening_balance'], 2) }}</span>
            </div>
            <div class="summary-item">
                <span>Total Invoices:</span>
                <span class="amount">{{ number_format($summary['total_invoices'], 2) }}</span>
            </div>
            <div class="summary-item">
                <span>Total Payments:</span>
                <span class="amount">{{ number_format($summary['total_payments'], 2) }}</span>
            </div>
            <div class="summary-item">
                <span>Total Adjustments:</span>
                <span class="amount">{{ number_format($summary['total_adjustments'], 2) }}</span>
            </div>
            <div class="summary-item total">
                <span>Closing Balance:</span>
                <span class="amount">{{ number_format($summary['closing_balance'], 2) }}</span>
            </div>
        </div>
    </div>

    <!-- Invoices -->
    @if($invoices->count() > 0)
    <div class="section">
        <div class="section-title">Invoices</div>
        <table>
            <thead>
                <tr>
                    <th>Date>
                    <th>Invoice #</th>
                    <th>Period</th>
                    <th class="amount">Amount>
                    <th>Status>
                </tr>
            </thead>
            <tbody>
                @foreach($invoices as $invoice)
                <tr>
                    <td>{{ $invoice->created_at->format('M d, Y') }}</td>
                    <td>{{ $invoice->invoice_number }}</td>
                    <td>{{ $invoice->billing_period_start->format('M Y') }}</td>
                    <td class="amount">{{ number_format($invoice->total_due, 2) }}</td>
                    <td><span class="status {{ $invoice->status }}">{{ ucfirst($invoice->status) }}</span></td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Transactions -->
    @if($transactions->count() > 0)
    <div class="section">
        <div class="section-title">Transactions</div>
        <table>
            <thead>
                <tr>
                    <th>Date>
                    <th>Type>
                    <th>Description>
                    <th class="amount">Amount>
                    <th class="amount">Balance After</th>
                </tr>
            </thead>
            <tbody>
                @foreach($transactions as $transaction)
                <tr>
                    <td>{{ $transaction->created_at->format('M d, Y') }}</td>
                    <td>{{ ucfirst($transaction->transaction_type) }}</td>
                    <td>{{ $transaction->description }}</td>
                    <td class="amount">{{ number_format($transaction->amount, 2) }}</td>
                    <td class="amount">{{ number_format($transaction->balance_after, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>This is a computer-generated statement. For any inquiries, please contact our office.</p>
        <p>Generated on {{ now()->format('M d, Y H:i') }}</p>
    </div>
</body>
</html>