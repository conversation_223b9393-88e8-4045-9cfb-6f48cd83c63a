<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revenue Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #333;
            margin: 0;
            padding: 15px;
        }
        .header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 2px solid #333;
            padding-bottom: 8px;
        }
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        .company-info {
            font-size: 9px;
            color: #666;
        }
        .report-title {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 15px 0;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 8px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 3px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 12px;
            font-size: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .amount {
            text-align: right;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        .summary-item {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .summary-label {
            font-size: 9px;
            color: #666;
            margin-bottom: 3px;
        }
        .summary-value {
            font-size: 13px;
            font-weight: bold;
            color: #333;
        }
        .total-row {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .footer {
            margin-top: 25px;
            text-align: center;
            font-size: 9px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 8px;
        }
        .chart-placeholder {
            width: 100%;
            height: 150px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin: 12px 0;
            font-size: 10px;
        }
        .metric-card {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 8px;
        }
        .metric-title {
            font-size: 9px;
            color: #666;
            margin-bottom: 2px;
        }
        .metric-value {
            font-size: 12px;
            font-weight: bold;
            color: #333;
        }
        .metric-change {
            font-size: 8px;
            margin-top: 2px;
        }
        .positive { color: #2e7d32; }
        .negative { color: #c62828; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ $company['name'] }}</div>
        <div class="company-info">
            {{ $company['address'] }} | Phone: {{ $company['phone'] }} | Email: {{ $company['email'] }}
        </div>
    </div>

    <!-- Report Title -->
    <div class="report-title">REVENUE ANALYSIS REPORT</div>

    <!-- Key Metrics -->
    <div class="section">
        <div class="section-title">Key Performance Metrics</div>
        <div class="summary-grid">
            <div class="summary-item" style="background-color: #e8f5e8; border-color: #4caf50;">
                <div class="summary-label">Total Revenue</div>
                <div class="summary-value">{{ number_format($revenueData['total_revenue'] ?? 0, 2) }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Paid Invoices</div>
                <div class="summary-value">{{ $revenueData['paid_invoices_count'] ?? 0 }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Outstanding</div>
                <div class="summary-value">{{ number_format($revenueData['total_outstanding'] ?? 0, 2) }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Collection Rate</div>
                <div class="summary-value">{{ $revenueData['collection_rate'] ?? 0 }}%</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Avg Invoice Value</div>
                <div class="summary-value">{{ number_format($revenueData['average_invoice_value'] ?? 0, 2) }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Active Customers</div>
                <div class="summary-value">{{ $revenueData['active_customers'] ?? 0 }}</div>
            </div>
        </div>
    </div>

    <!-- Revenue Trend Chart -->
    <div class="section">
        <div class="section-title">Revenue Trend</div>
        <div class="chart-placeholder">
            [Chart: Monthly Revenue Trend - Last 12 Months]
        </div>
    </div>

    <!-- Revenue by Estate -->
    @if(isset($revenueData['by_estate']) && count($revenueData['by_estate']) > 0)
    <div class="section">
        <div class="section-title">Revenue by Estate</div>
        <table>
            <thead>
                <tr>
                    <th>Estateth>
                    <th>Houses</th>
                    <th class="amount">Revenue</th>
                    <th class="amount">Outstanding>
                    <th class="amount">Collection Rate>
                    <th class="amount">Avg Invoice>
                </tr>
            </thead>
            <tbody>
                @foreach($revenueData['by_estate'] as $estate)
                <tr>
                    <td>{{ $estate['name'] ?? '' }}</td>
                    <td>{{ $estate['houses_count'] ?? 0 }}</td>
                    <td class="amount">{{ number_format($estate['revenue'] ?? 0, 2) }}</td>
                    <td class="amount">{{ number_format($estate['outstanding'] ?? 0, 2) }}</td>
                    <td class="amount">{{ $estate['collection_rate'] ?? 0 }}%</td>
                    <td class="amount">{{ number_format($estate['average_invoice'] ?? 0, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Monthly Breakdown -->
    @if(isset($revenueData['monthly_breakdown']) && count($revenueData['monthly_breakdown']) > 0)
    <div class="section">
        <div class="section-title">Monthly Revenue Breakdown</div>
        <table>
            <thead>
                <tr>
                    <th>Month</th>
                    <th>Invoices</th>
                    <th class="amount">Revenue>
                    <th class="amount">Payments>
                    <th class="amount">Outstanding</th>
                    <th class="amount">Growth>
                </tr>
            </thead>
            <tbody>
                @foreach($revenueData['monthly_breakdown'] as $month)
                <tr>
                    <td>{{ $month['month'] ?? '' }}</td>
                    <td>{{ $month['invoices_count'] ?? 0 }}</td>
                    <td class="amount">{{ number_format($month['revenue'] ?? 0, 2) }}</td>
                    <td class="amount">{{ number_format($month['payments'] ?? 0, 2) }}</td>
                    <td class="amount">{{ number_format($month['outstanding'] ?? 0, 2) }}</td>
                    <td class="amount">
                        <span class="{{ ($month['growth_rate'] ?? 0) >= 0 ? 'positive' : 'negative' }}">
                            {{ ($month['growth_rate'] ?? 0) >= 0 ? '+' : '' }}{{ $month['growth_rate'] ?? 0 }}%
                        </span>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Top Performing Estates -->
    @if(isset($revenueData['top_estates']) && count($revenueData['top_estates']) > 0)
    <div class="section">
        <div class="section-title">Top Performing Estates</div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
            @foreach($revenueData['top_estates'] as $index => $estate)
            <div class="metric-card">
                <div class="metric-title">#{{ $index + 1 }} {{ $estate['name'] ?? '' }}</div>
                <div class="metric-value">{{ number_format($estate['revenue'] ?? 0, 2) }}</div>
                <div class="metric-change {{ ($estate['growth'] ?? 0) >= 0 ? 'positive' : 'negative' }}">
                    {{ ($estate['growth'] ?? 0) >= 0 ? '↑' : '↓' }} {{ abs($estate['growth'] ?? 0) }}% vs last period
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>This is a computer-generated report. For any inquiries, please contact our finance department.</p>
        <p>Generated on {{ $generatedAt->format('M d, Y H:i') }}</p>
    </div>
</body>
</html>