<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aging Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .company-info {
            font-size: 10px;
            color: #666;
        }
        .report-title {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .amount {
            text-align: right;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .summary-item {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .summary-label {
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }
        .summary-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .total-row {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        .chart-placeholder {
            width: 100%;
            height: 200px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ $company['name'] }}</div>
        <div class="company-info">
            {{ $company['address'] }} | Phone: {{ $company['phone'] }} | Email: {{ $company['email'] }}
        </div>
    </div>

    <!-- Report Title -->
    <div class="report-title">ACCOUNTS RECEIVABLE AGING REPORT</div>

    <!-- Summary Section -->
    <div class="section">
        <div class="section-title">Aging Summary</div>
        <div class="summary-grid">
            <div class="summary-item">
                <div class="summary-label">Current</div>
                <div class="summary-value">{{ number_format($agingData['current'] ?? 0, 2) }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">1-30 Days</div>
                <div class="summary-value">{{ number_format($agingData['days_1_30'] ?? 0, 2) }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">31-60 Days</div>
                <div class="summary-value">{{ number_format($agingData['days_31_60'] ?? 0, 2) }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">61-90 Days</div>
                <div class="summary-value">{{ number_format($agingData['days_61_90'] ?? 0, 2) }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Over 90 Days</div>
                <div class="summary-value">{{ number_format($agingData['days_over_90'] ?? 0, 2) }}</div>
            </div>
            <div class="summary-item" style="background-color: #e8f5e8; border-color: #4caf50;">
                <div class="summary-label">Total Outstanding</div>
                <div class="summary-value" style="color: #2e7d32;">{{ number_format($agingData['total_outstanding'] ?? 0, 2) }}</div>
            </div>
        </div>
    </div>

    <!-- Chart Section -->
    <div class="section">
        <div class="section-title">Aging Distribution</div>
        <div class="chart-placeholder">
            [Chart: Aging Distribution by Days Outstanding]
        </div>
    </div>

    <!-- Detailed Breakdown -->
    @if(isset($agingData['breakdown']) && count($agingData['breakdown']) > 0)
    <div class="section">
        <div class="section-title">Detailed Breakdown</div>
        <table>
            <thead>
                <tr>
                    <th>House Number>
                    <th>Customer</th>
                    <th>Estate>
                    <th class="amount">Current>
                    <th class="amount">1-30 Days</th>
                    <th class="amount">31-60 Days</th>
                    <th class="amount">61-90 Days</th>
                    <th class="amount">Over 90 Days>
                    <th class="amount">Total>
                </tr>
            </thead>
            <tbody>
                @foreach($agingData['breakdown'] as $item)
                <tr>
                    <td>{{ $item['house_number'] ?? '' }}</td>
                    <td>{{ $item['customer_name'] ?? '' }}</td>
                    <td>{{ $item['estate_name'] ?? '' }}</td>
                    <td class="amount">{{ number_format($item['current'] ?? 0, 2) }}</td>
                    <td class="amount">{{ number_format($item['days_1_30'] ?? 0, 2) }}</td>
                    <td class="amount">{{ number_format($item['days_31_60'] ?? 0, 2) }}</td>
                    <td class="amount">{{ number_format($item['days_61_90'] ?? 0, 2) }}</td>
                    <td class="amount">{{ number_format($item['days_over_90'] ?? 0, 2) }}</td>
                    <td class="amount">{{ number_format($item['total'] ?? 0, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr class="total-row">
                    <td colspan="3"><strong>Total</strong></td>
                    <td class="amount"><strong>{{ number_format($agingData['current'] ?? 0, 2) }}</strong></td>
                    <td class="amount"><strong>{{ number_format($agingData['days_1_30'] ?? 0, 2) }}</strong></td>
                    <td class="amount"><strong>{{ number_format($agingData['days_31_60'] ?? 0, 2) }}</strong></td>
                    <td class="amount"><strong>{{ number_format($agingData['days_61_90'] ?? 0, 2) }}</strong></td>
                    <td class="amount"><strong>{{ number_format($agingData['days_over_90'] ?? 0, 2) }}</strong></td>
                    <td class="amount"><strong>{{ number_format($agingData['total_outstanding'] ?? 0, 2) }}</strong></td>
                </tr>
            </tfoot>
        </table>
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>This is a computer-generated report. For any inquiries, please contact our accounting department.</p>
        <p>Generated on {{ $generatedAt->format('M d, Y H:i') }}</p>
    </div>
</body>
</html>