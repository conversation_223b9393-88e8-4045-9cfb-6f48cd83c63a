<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-info h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }
        .company-info p {
            margin: 5px 0;
            font-size: 11px;
        }
        .invoice-info {
            text-align: right;
        }
        .invoice-info h2 {
            margin: 0;
            font-size: 20px;
            color: #333;
        }
        .invoice-info p {
            margin: 5px 0;
            font-size: 11px;
        }
        .bill-to {
            margin-bottom: 30px;
        }
        .bill-to h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            color: #333;
        }
        .bill-to p {
            margin: 5px 0;
            font-size: 11px;
        }
        .invoice-details {
            margin-bottom: 30px;
        }
        .invoice-details table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .invoice-details th,
        .invoice-details td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 11px;
        }
        .invoice-details th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .invoice-table {
            margin-bottom: 30px;
        }
        .invoice-table table {
            width: 100%;
            border-collapse: collapse;
        }
        .invoice-table th,
        .invoice-table td {
            padding: 10px;
            text-align: left;
            border: 1px solid #ddd;
            font-size: 11px;
        }
        .invoice-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .invoice-table .amount {
            text-align: right;
        }
        .total-section {
            margin-top: 20px;
        }
        .total-section table {
            width: 100%;
            border-collapse: collapse;
        }
        .total-section td {
            padding: 8px;
            text-align: right;
            font-size: 11px;
        }
        .total-section .total-row td {
            border-top: 2px solid #333;
            font-weight: bold;
            font-size: 12px;
        }
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-left: 4px solid #007bff;
        }
        .notes h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #333;
        }
        .notes p {
            margin: 0;
            font-size: 11px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-draft {
            background-color: #6c757d;
            color: white;
        }
        .status-sent {
            background-color: #007bff;
            color: white;
        }
        .status-paid {
            background-color: #28a745;
            color: white;
        }
        .status-overdue {
            background-color: #dc3545;
            color: white;
        }
        .status-cancelled {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                <h1>{{ $company['name'] }}</h1>
                <p>{{ $company['address'] }}</p>
                <p>Phone: {{ $company['phone'] }}</p>
                <p>Email: {{ $company['email'] }}</p>
            </div>
            <div class="invoice-info">
                <h2>INVOICE</h2>
                <p><strong>Invoice #:</strong> {{ $invoice->invoice_number }}</p>
                <p><strong>Date:</strong> {{ $invoice->created_at->format('M d, Y') }}</p>
                <p><strong>Due Date:</strong> {{ $invoice->due_date->format('M d, Y') }}</p>
                <p><strong>Status:</strong> 
                    <span class="status-badge status-{{ $invoice->status }}">
                        {{ ucfirst($invoice->status) }}
                    </span>
                </p>
            </div>
        </div>

        <!-- Bill To -->
        <div class="bill-to">
            <h3>Bill To:</h3>
            <p><strong>House:</strong> {{ $invoice->house->house_number }}</p>
            <p><strong>Estate:</strong> {{ $invoice->house->estate->name }}</p>
            @if($invoice->house->primaryContact)
                <p><strong>Contact:</strong> {{ $invoice->house->primaryContact->name }}</p>
                <p><strong>Phone:</strong> {{ $invoice->house->primaryContact->phone }}</p>
            @else
                <p><strong>Contact:</strong> No primary contact assigned</p>
            @endif
        </div>

        <!-- Invoice Details -->
        <div class="invoice-details">
            <table>
                <tr>
                    <th><strong>Billing Period</strong></th>
                    <td>{{ $invoice->billing_period_start->format('M d, Y') }} - {{ $invoice->billing_period_end->format('M d, Y') }}</td>
                </tr>
                <tr>
                    <th><strong>Water Rate</strong></th>
                    <td>KES {{ number_format($invoice->rate_per_unit, 4) }} per m³</td>
                </tr>
                <tr>
                    <th><strong>Fixed Charge</strong></th>
                    <td>KES {{ number_format($invoice->fixed_charge, 2) }}</td>
                </tr>
                @if($invoice->previous_balance > 0)
                <tr>
                    <th><strong>Previous Balance</strong></th>
                    <td>KES {{ number_format($invoice->previous_balance, 2) }}</td>
                </tr>
                @endif
            </table>
        </div>

        <!-- Invoice Items -->
        <div class="invoice-table">
            <table>
                <thead>
                    <tr>
                        <th>Description>
                        <th>Previous Reading</th>
                        <th>Current Reading</th>
                        <th>Consumption (m³)</th>
                        <th class="amount">Amount (KES)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Water Consumption</td>
                        <td>{{ number_format($invoice->previous_reading, 2) }}</td>
                        <td>{{ number_format($invoice->current_reading, 2) }}</td>
                        <td>{{ number_format($invoice->consumption, 2) }}</td>
                        <td class="amount">{{ number_format($invoice->amount, 2) }}</td>
                    </tr>
                    @if($invoice->fixed_charge > 0)
                    <tr>
                        <td>Fixed Charge</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td class="amount">{{ number_format($invoice->fixed_charge, 2) }}</td>
                    </tr>
                    @endif
                    @if($invoice->previous_balance > 0)
                    <tr>
                        <td>Previous Balance</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td class="amount">{{ number_format($invoice->previous_balance, 2) }}</td>
                    </tr>
                    @endif
                </tbody>
            </table>

            <!-- Total Section -->
            <div class="total-section">
                <table>
                    <tr>
                        <td width="80%">Subtotal:</td>
                        <td width="20%">KES {{ number_format($invoice->amount + $invoice->fixed_charge, 2) }}</td>
                    </tr>
                    @if($invoice->previous_balance > 0)
                    <tr>
                        <td>Previous Balance:</td>
                        <td>KES {{ number_format($invoice->previous_balance, 2) }}</td>
                    </tr>
                    @endif
                    @if($invoice->late_fee > 0)
                    <tr>
                        <td>Late Fee:</td>
                        <td>KES {{ number_format($invoice->late_fee, 2) }}</td>
                    </tr>
                    @endif
                    @if($invoice->tax_amount > 0)
                    <tr>
                        <td>Tax:</td>
                        <td>KES {{ number_format($invoice->tax_amount, 2) }}</td>
                    </tr>
                    @endif
                    <tr class="total-row">
                        <td><strong>TOTAL DUE:</strong></td>
                        <td><strong>KES {{ number_format($invoice->total_due ?: $invoice->total_amount, 2) }}</strong></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Notes -->
        @if($invoice->notes)
        <div class="notes">
            <h4>Notes:</h4>
            <p>{{ $invoice->notes }}</p>
        </div>
        @endif

        <!-- Payment Information -->
        <div class="notes">
            <h4>Payment Information:</h4>
            <p>Please make payment by the due date to avoid late fees. Payment can be made via:</p>
            <p>• M-Pesa: {{ $company['phone'] }}</p>
            <p>• Bank Transfer: Account details available upon request</p>
            <p>• Cash: At our office during business hours</p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Thank you for your business! For inquiries, please contact us at {{ $company['phone'] }} or {{ $company['email'] }}</p>
            <p>This is a computer-generated invoice. No signature required.</p>
        </div>
    </div>
</body>
</html>