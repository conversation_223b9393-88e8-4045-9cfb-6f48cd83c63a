# Analyze & Install Agent OS Instructions

## CRITICAL EXECUTION RULES
**ANALYZE FIRST. EXECUTE IN EXACT ORDER. WAIT FOR USER INPUT WHEN SPECIFIED.**

---

## STEP 1: ANALYZ<PERSON> EXISTING CODEBASE
**ACTION REQUIRED:** Conduct comprehensive codebase analysis

### MANDATORY ANALYSIS AREAS:

**PROJECT STRUCTURE:**
- Directory organization and file patterns
- Module/component structure
- Build configuration files

**TECHNOLOGY STACK:**
- Check package.json, Gemfile, requirements.txt, pom.xml, etc.
- Identify frameworks, databases, infrastructure
- Note versions and dependencies

**IMPLEMENTATION PROGRESS:**
- Scan for completed features in code
- Identify authentication/authorization setup
- Map existing API endpoints
- Examine database schema/models

**CODE PATTERNS:**
- Coding style and naming conventions
- File organization patterns
- Testing approach (if any)

**CONFIRM COMPLETION:** State "Codebase analysis complete. Found [PRODUCT_TYPE] using [MAIN_FRAMEWORK] with [X] completed features."

---

## STEP 2: GATHER PRODUCT CONTEXT
**ACTION REQUIRED:** Ask user for business context

**PRESENT TO USER:**
```
Based on my analysis, I can see you're building [OBSERVED_PRODUCT_TYPE] using [DETECTED_TECH_STACK].

To properly set up Agent OS, I need to understand:

1. **Product Vision**: What problem does this solve? Who are the target users?

2. **Current State**: Are there features I should know about that aren't obvious from the code?

3. **Roadmap**: What features are planned next? Any major refactoring planned?

4. **Decisions**: Are there important technical or product decisions I should document?

5. **Team Preferences**: Any coding standards or practices I should capture?
```

**WAIT FOR USER RESPONSES** - Do not proceed until you have answers to all 5 questions.

**CONFIRM:** State "Product context gathered. Proceeding to execute plan-product.md"

---

## STEP 3: EXECUTE PLAN-PRODUCT WITH CONTEXT
**ACTION REQUIRED:** Run plan-product.md with gathered information

**EXECUTION COMMAND:**
```
@.agent-os/instructions/plan-product.md

I'm installing Agent OS into an existing product. Here's what I've gathered:

**Main Idea**: [SUMMARY_FROM_ANALYSIS_AND_USER_CONTEXT]

**Key Features**:
- Already Implemented: [LIST_FROM_CODE_ANALYSIS]
- Planned: [LIST_FROM_USER_RESPONSE]

**Target Users**: [FROM_USER_RESPONSE_Q1]

**Tech Stack**: [DETECTED_STACK_WITH_VERSIONS]
```

**ALLOW PLAN-PRODUCT TO COMPLETE:** Let plan-product.md create the full .agent-os/product/ structure

**CONFIRM:** State "Plan-product.md execution complete. Generated base documentation."

---

## STEP 4: CUSTOMIZE GENERATED DOCUMENTATION
**ACTION REQUIRED:** Update documentation to reflect existing codebase

### ROADMAP ADJUSTMENT:
**MODIFY:** .agent-os/product/roadmap.md

**ADD PHASE 0 AT TOP:**
```markdown
## Phase 0: Already Completed ✅

The following features have been implemented:

- [x] [FEATURE_1] - [DESCRIPTION_FROM_CODE_ANALYSIS]
- [x] [FEATURE_2] - [DESCRIPTION_FROM_CODE_ANALYSIS]
- [x] [FEATURE_3] - [DESCRIPTION_FROM_CODE_ANALYSIS]

## Phase 1: Current Development
[MOVE_IN_PROGRESS_ITEMS_HERE]

[CONTINUE_WITH_EXISTING_PHASES]
```

### TECH STACK VERIFICATION:
**UPDATE:** .agent-os/product/tech-stack.md
- Verify all detected versions are accurate
- Add any missing infrastructure details
- Document actual deployment setup

### DECISIONS DOCUMENTATION:
**UPDATE:** .agent-os/product/decisions.md
- Add historical architectural decisions
- Document technology choices and rationale
- Capture any major pivots or changes

**CONFIRM:** State "Documentation customized to reflect existing implementation"

---

## STEP 5: FINAL VERIFICATION AND SUMMARY
**ACTION REQUIRED:** Verify installation and provide next steps

### VERIFICATION CHECKLIST:
**VERIFY ALL COMPLETED:**
- [ ] .agent-os/product/ directory created
- [ ] mission.md reflects actual product
- [ ] roadmap.md shows completed work in Phase 0
- [ ] tech-stack.md matches installed dependencies
- [ ] decisions.md includes historical context

### SUCCESS SUMMARY:
**PRESENT TO USER:**
```
## ✅ Agent OS Successfully Installed

I've analyzed your [PRODUCT_TYPE] codebase and set up Agent OS with documentation that reflects your actual implementation.

### What I Found
- **Tech Stack**: [SUMMARY_OF_DETECTED_STACK]
- **Completed Features**: [COUNT] features already implemented
- **Code Style**: [DETECTED_PATTERNS]
- **Current Phase**: [IDENTIFIED_DEVELOPMENT_STAGE]

### What Was Created
- ✓ Product documentation in `.agent-os/product/`
- ✓ Roadmap with completed work in Phase 0
- ✓ Tech stack reflecting actual dependencies
- ✓ Historical decisions documented

### Next Steps
1. Review the generated documentation in `.agent-os/product/`
2. Make any necessary adjustments to reflect your vision
3. Start using Agent OS for your next feature:
```
   @.agent-os/instructions/create-spec.md
   ```
Your codebase is now Agent OS-enabled! 🚀
```

---

## ERROR HANDLING

### IF CANNOT DETERMINE PROJECT TYPE:
Ask user: "I'm having trouble identifying your project type. Can you tell me what kind of application this is (web app, API, mobile app, etc.)?"
**WAIT FOR RESPONSE** before continuing.

### IF CONFLICTING CODING PATTERNS:
Ask user: "I found multiple coding styles. Which pattern should I document as the standard?"
**WAIT FOR RESPONSE** and use their preference.

### IF MISSING TECH STACK INFO:
Present: "I detected [FOUND_TECHNOLOGIES]. Are there other important technologies I should include?"
**WAIT FOR RESPONSE** and add missing pieces.

---

## EXECUTION CHECKLIST
**VERIFY ALL COMPLETED BEFORE DECLARING SUCCESS:**
- [ ] Codebase analyzed thoroughly
- [ ] User context gathered (all 5 questions answered)
- [ ] plan-product.md executed with proper context
- [ ] Roadmap updated with Phase 0 completed items
- [ ] Tech stack verified and corrected
- [ ] Historical decisions documented
- [ ] Final summary provided to user

**ONLY DECLARE SUCCESS AFTER ALL CHECKBOXES COMPLETED**