# Spec Creation Instructions

## CRITICAL EXECUTION RULES
**NEVER SKIP STEPS. EXECUTE IN EXACT ORDER. WAIT FOR CONFIRMATION WHEN SPECIFIED.**

---

## STEP 1: SPEC INITIATION
**ACTION REQUIRED:** Identify trigger and route accordingly

### IF user asks "what's next?" or "what should we work on?"
1. READ @.agent-os/product/roadmap.md
2. FIND next uncompleted item
3. SUGGEST to user: "I recommend we work on: [ITEM]. Should we create a spec for this?"
4. **WAIT FOR USER APPROVAL** - DO NOT PROCEED WITHOUT "YES"

### IF user provides specific spec idea
1. ACCEPT any format/length/detail
2. CONFIRM: "I'll create a spec for: [BRIEF_SUMMARY]. Proceeding to gather context."
3. PROCEED immediately to Step 2

---

## STEP 2: CONTEXT GATHERING
**ACTION REQUIRED:** Read all three files

**MANDATORY READS:**
1. @.agent-os/product/mission.md
2. @.agent-os/product/roadmap.md  
3. @.agent-os/product/tech-stack.md

**CONFIRM COMPLETION:** State "Context gathered from mission, roadmap, and tech-stack documents."

---

## STEP 3: REQUIREMENTS CLARIFICATION
**ACTION REQUIRED:** Ask questions if anything is unclear

**DECISION POINT:**
- IF spec requirements are clear → State "Requirements are clear, proceeding to date determination"
- IF clarification needed → Ask numbered questions using this template:

```
I need clarification on:
1. [SPECIFIC SCOPE QUESTION]
2. [SPECIFIC TECHNICAL QUESTION]  
3. [SPECIFIC UX QUESTION]
```

**WAIT FOR ANSWERS** before proceeding if questions asked.

---

## STEP 4: DATE DETERMINATION
**ACTION REQUIRED:** Get current date for folder naming

**PRIMARY METHOD:**
1. RUN date '+%Y-%m-%d'
2. EXTRACT YYYY-MM-DD format
3. STORE date variable

**IF RUN METHOD FAILS:**
Ask user: "What is today's date? (YYYY-MM-DD format)"
**WAIT FOR RESPONSE**

**CONFIRM:** State "Date confirmed as: [YYYY-MM-DD]"

---

## STEP 5: CREATE SPEC FOLDER
**ACTION REQUIRED:** Create dated folder

**FOLDER NAME FORMAT:** YYYY-MM-DD-spec-name
- Use date from Step 4
- Convert spec name to kebab-case
- Maximum 5 words in name

**EXAMPLES:**
- 2025-03-15-password-reset-flow
- 2025-03-16-user-dashboard

**CONFIRM:** State "Created folder: [FOLDER_NAME]"

---

## STEP 6: CREATE SPEC.MD
**ACTION REQUIRED:** Create main spec file with exact template

**FILE PATH:** .agent-os/specs/[FOLDER_NAME]/spec.md

**MANDATORY TEMPLATE:**
```markdown
# Spec Requirements Document

> Spec: [SPEC_NAME]
> Created: [CURRENT_DATE]
> Status: Planning

## Overview
[1-2 sentence goal and objective]

## User Stories
### [STORY_TITLE]
As a [USER_TYPE], I want to [ACTION], so that [BENEFIT].
[Detailed workflow description]

## Spec Scope
1. **[FEATURE_NAME]** - [One sentence description]
2. **[FEATURE_NAME]** - [One sentence description]

## Out of Scope
- [Excluded functionality 1]
- [Excluded functionality 2]

## Expected Deliverable
1. [Testable outcome 1]
2. [Testable outcome 2]
```

**CONFIRM:** State "Created spec.md with all required sections"

---

## STEP 7: CREATE TECHNICAL SPEC
**ACTION REQUIRED:** Create technical specification

**CREATE:** sub-specs/ folder and sub-specs/technical-spec.md

**MANDATORY TEMPLATE:**
```markdown
# Technical Specification

This is the technical specification for @.agent-os/specs/[FOLDER_NAME]/spec.md

> Created: [CURRENT_DATE]
> Version: 1.0.0

## Technical Requirements
- [Specific requirement 1]
- [Specific requirement 2]

## Approach Options
**Option A:** [Description]
- Pros: [List]
- Cons: [List]

**Option B:** [Description] (Selected)
- Pros: [List]  
- Cons: [List]

**Rationale:** [Explanation of selection]

## External Dependencies
- **[Library Name]** - [Purpose and justification]
```

**CONFIRM:** State "Created technical-spec.md"

---

## STEP 8: DATABASE SCHEMA (CONDITIONAL)
**DECISION POINT:** Does this spec require database changes?

**IF YES:**
Create sub-specs/database-schema.md with:
- New tables/columns
- Migration syntax
- Relationships and constraints

**IF NO:**
State "No database changes needed - skipping schema creation"

---

## STEP 9: API SPECIFICATION (CONDITIONAL)  
**DECISION POINT:** Does this spec require API changes?

**IF YES:**
Create sub-specs/api-spec.md with:
- Endpoints (method, path, params, responses)
- Controllers and actions
- Error handling

**IF NO:**
State "No API changes needed - skipping API spec creation"

---

## STEP 10: CREATE TESTS SPECIFICATION
**ACTION REQUIRED:** Always create tests spec

**CREATE:** sub-specs/tests.md

**MANDATORY SECTIONS:**
- Unit Tests (models, services, helpers)
- Integration Tests (controllers, APIs, workflows)  
- Feature Tests (end-to-end scenarios)
- Mocking Requirements (external services)

**CONFIRM:** State "Created tests.md with comprehensive test coverage"

---

## STEP 11: USER REVIEW
**ACTION REQUIRED:** Request user approval

**PRESENT TO USER:**
```
I've created the spec documentation:
- Spec Requirements: @.agent-os/specs/[FOLDER_NAME]/spec.md
- Technical Spec: @.agent-os/specs/[FOLDER_NAME]/sub-specs/technical-spec.md
[List other created specs]

Please review and let me know if any changes are needed before I create the task breakdown.
```

**WAIT FOR USER APPROVAL** - DO NOT PROCEED WITHOUT CONFIRMATION

---

## STEP 12: CREATE TASKS.MD
**ACTION REQUIRED:** Create implementation tasks

**CREATE:** tasks.md in spec folder

**TASK STRUCTURE:**
- 1-5 major tasks (numbered checklist)
- Up to 8 subtasks each (decimal notation: 1.1, 1.2)
- First subtask: typically "Write tests for [component]"
- Last subtask: "Verify all tests pass"

**TEMPLATE:**
```markdown
# Spec Tasks

> Created: [CURRENT_DATE]
> Status: Ready for Implementation

## Tasks

- [ ] 1. [Major Task Description]
  - [ ] 1.1 Write tests for [component]
  - [ ] 1.2 [Implementation step]
  - [ ] 1.3 [Implementation step]  
  - [ ] 1.4 Verify all tests pass

- [ ] 2. [Major Task Description]
  - [ ] 2.1 Write tests for [component]
  - [ ] 2.2 [Implementation step]
```

**CONFIRM:** State "Created tasks.md with TDD approach"

---

## STEP 13: ADD CROSS-REFERENCES
**ACTION REQUIRED:** Update spec.md with file references

**ADD TO spec.md:**
```markdown
## Spec Documentation
- Tasks: @.agent-os/specs/[FOLDER_NAME]/tasks.md
- Technical Specification: @.agent-os/specs/[FOLDER_NAME]/sub-specs/technical-spec.md
[List only files that were actually created]
```

**CONFIRM:** State "Added cross-references to spec.md"

---

## STEP 14: DECISION DOCUMENTATION
**DECISION POINT:** Does this spec impact mission/roadmap significantly?

**IF YES:**
1. IDENTIFY up to 3 key decisions
2. ASK USER: "This spec involves strategic decisions. Should I document them in decisions.md?"
3. **WAIT FOR APPROVAL**
4. IF approved, UPDATE @.agent-os/product/decisions.md

**IF NO:**
State "This spec aligns with current mission and roadmap - no strategic decisions to document"

---

## STEP 15: EXECUTION READINESS
**ACTION REQUIRED:** Present first task and request confirmation

**PRESENT TO USER:**
```
The spec planning is complete. The first task is:

**Task 1:** [First Task Title]
[Brief description of task 1 and subtasks]

Would you like me to proceed with implementing Task 1? I will follow @.agent-os/instructions/execute-tasks.md and focus only on this first task.

Type 'yes' to proceed with Task 1, or let me know if you'd like to review the plan first.
```

**WAIT FOR USER CONFIRMATION** before any implementation.

---

## EXECUTION CHECKLIST
**VERIFY ALL COMPLETED:**
- [ ] Date determined via file system
- [ ] Spec folder created with date prefix
- [ ] spec.md created with all sections
- [ ] technical-spec.md created
- [ ] Conditional specs created if needed
- [ ] tests.md created
- [ ] User approved documentation  
- [ ] tasks.md created with TDD approach
- [ ] Cross-references added
- [ ] Strategic decisions evaluated
- [ ] User confirmed execution readiness

**ONLY PROCEED TO IMPLEMENTATION AFTER ALL CHECKBOXES COMPLETED**