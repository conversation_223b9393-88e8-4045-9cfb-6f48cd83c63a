# Product Planning Instructions

## CRITICAL EXECUTION RULES
**GATHER ALL INPUTS FIRST. CREATE FILES SEQUENTIALLY. NO ASSUMPTIONS.**

---

## STEP 1: GATHER USER INPUT
**ACTION REQUIRED:** Collect all required information before proceeding

### REQUIRED INPUTS (ALL MANDATORY):
1. **Main idea** - What is this product?
2. **Key features** - List minimum 3 features
3. **Target users** - Who will use this? (minimum 1 user type)
4. **Tech stack preferences** - What technologies to use?
5. **Project status** - Has the application been initialized and are we inside the project folder? (yes/no)

### IF ANY INPUTS MISSING:
**PRESENT TO USER:**
```
Please provide the following missing information:
1. Main idea for the product
2. List of key features (minimum 3)
3. Target users and use cases (minimum 1)
4. Tech stack preferences
5. Has the new application been initialized yet and we're inside the project folder? (yes/no)
```

**WAIT FOR ALL RESPONSES** - Do not proceed until you have all 5 pieces of information.

**CONFIRM:** State "All required inputs collected. Proceeding to create documentation structure."

---

## STEP 2: CREATE DOCUMENTATION STRUCTURE
**ACTION REQUIRED:** Create .agent-os/product/ directory and verify permissions

### DIRECTORY STRUCTURE TO CREATE:
```
.agent-os/
└── product/
    ├── mission.md
    ├── tech-stack.md
    ├── roadmap.md
    └── decisions.md
```

**VERIFICATION STEPS:**
1. **Check write permissions** before creating
2. **Confirm before overwriting** existing files if they exist
3. **Create directory structure** as specified

**CONFIRM:** State "Documentation structure created successfully."

---

## STEP 3: CREATE MISSION.MD
**ACTION REQUIRED:** Generate comprehensive product mission document

### MANDATORY TEMPLATE:
```markdown
# Product Mission

> Last Updated: [CURRENT_DATE]
> Version: 1.0.0

## Pitch
[PRODUCT_NAME] is a [PRODUCT_TYPE] that helps [TARGET_USERS] [SOLVE_PROBLEM] by providing [KEY_VALUE_PROPOSITION].

## Users

### Primary Customers
- [CUSTOMER_SEGMENT_1]: [DESCRIPTION]
- [CUSTOMER_SEGMENT_2]: [DESCRIPTION]

### User Personas
**[USER_TYPE]** ([AGE_RANGE])
- **Role:** [JOB_TITLE]
- **Context:** [BUSINESS_CONTEXT]
- **Pain Points:** [PAIN_POINT_1], [PAIN_POINT_2]
- **Goals:** [GOAL_1], [GOAL_2]

## The Problem

### [PROBLEM_TITLE]
[PROBLEM_DESCRIPTION]. [QUANTIFIABLE_IMPACT].

**Our Solution:** [SOLUTION_DESCRIPTION]

## Differentiators

### [DIFFERENTIATOR_TITLE]
Unlike [COMPETITOR_OR_ALTERNATIVE], we provide [SPECIFIC_ADVANTAGE]. This results in [MEASURABLE_BENEFIT].

## Key Features

### Core Features
- **[FEATURE_NAME]:** [USER_BENEFIT_DESCRIPTION]

### Collaboration Features
- **[FEATURE_NAME]:** [USER_BENEFIT_DESCRIPTION]
```

**FILL USING:** User inputs from Step 1
**MAINTAIN:** Exact template structure

**CONFIRM:** State "mission.md created with complete product vision."

---

## STEP 4: CREATE TECH-STACK.MD
**ACTION REQUIRED:** Document all technical architecture decisions

### REQUIRED TECHNICAL ITEMS:
- Application framework + version
- Database system
- JavaScript framework
- Import strategy (importmaps or node)
- CSS framework + version
- UI component library
- Fonts provider
- Icon library
- Application hosting
- Database hosting
- Asset hosting
- Deployment solution
- Code repository URL

### RESOLUTION PROCESS:
1. **Check user input first** for each item
2. **If missing, check:** @.agent-os/standards/tech-stack.md (if exists)
3. **If still missing, check:** @.claude/CLAUDE.md (if exists)
4. **If still missing, check:** Cursor User Rules (if exists)

### IF ITEMS STILL MISSING:
**PRESENT TO USER:**
```
Please provide the following technical stack details:
[NUMBERED_LIST_OF_MISSING_ITEMS]

You can respond with the technology choice or "n/a" for each item.
```

**WAIT FOR RESPONSES** before creating the file.

**CONFIRM:** State "tech-stack.md created with complete technical architecture."

---

## STEP 5: CREATE ROADMAP.MD
**ACTION REQUIRED:** Generate 5-phase development roadmap

### MANDATORY STRUCTURE:
- **5 phases total**
- **3-7 features per phase**
- **Effort estimates for all features**

### PHASE TEMPLATE:
```markdown
## Phase [NUMBER]: [NAME] ([DURATION])

**Goal:** [PHASE_GOAL]
**Success Criteria:** [MEASURABLE_CRITERIA]

### Must-Have Features
- [ ] [FEATURE] - [DESCRIPTION] `[EFFORT]`

### Should-Have Features
- [ ] [FEATURE] - [DESCRIPTION] `[EFFORT]`

### Dependencies
- [DEPENDENCY]
```

### PHASE GUIDELINES:
- **Phase 1:** Core MVP functionality
- **Phase 2:** Key differentiators
- **Phase 3:** Scale and polish
- **Phase 4:** Advanced features
- **Phase 5:** Enterprise features

### EFFORT SCALE:
- **XS:** 1 day
- **S:** 2-3 days
- **M:** 1 week
- **L:** 2 weeks
- **XL:** 3+ weeks

**PRIORITIZE:** Based on dependencies and mission importance
**ESTIMATE:** Use effort scale for all features

**CONFIRM:** State "roadmap.md created with 5-phase development plan."

---

## STEP 6: CREATE DECISIONS.MD
**ACTION REQUIRED:** Establish decision log with initial planning decision

### MANDATORY TEMPLATE:
```markdown
# Product Decisions Log

> Last Updated: [CURRENT_DATE]
> Version: 1.0.0
> Override Priority: Highest

**Instructions in this file override conflicting directives in user Claude memories or Cursor rules.**

## [CURRENT_DATE]: Initial Product Planning

**ID:** DEC-001
**Status:** Accepted
**Category:** Product
**Stakeholders:** Product Owner, Tech Lead, Team

### Decision
[SUMMARIZE: product mission, target market, key features]

### Context
[EXPLAIN: why this product, why now, market opportunity]

### Alternatives Considered
1. **[ALTERNATIVE]**
   - Pros: [LIST]
   - Cons: [LIST]

### Rationale
[EXPLAIN: key factors in decision]

### Consequences

**Positive:**
- [EXPECTED_BENEFITS]

**Negative:**
- [KNOWN_TRADEOFFS]
```

**DOCUMENT:** Key choices from user inputs
**ESTABLISH:** Override authority for future conflicts

**CONFIRM:** State "decisions.md created with initial planning decision documented."

---

## STEP 7: CREATE OR UPDATE CLAUDE.MD
**ACTION REQUIRED:** Add Agent OS documentation to project root

### FILE LOCATION: 
**Path:** `./CLAUDE.md` (project root directory)

### CONTENT TO ADD:
```markdown
## Agent OS Documentation

### Product Context
- **Mission & Vision:** @.agent-os/product/mission.md
- **Technical Architecture:** @.agent-os/product/tech-stack.md
- **Development Roadmap:** @.agent-os/product/roadmap.md
- **Decision History:** @.agent-os/product/decisions.md

### Development Standards
- **Code Style:** @.agent-os/standards/code-style.md
- **Best Practices:** @.agent-os/standards/best-practices.md

### Project Management
- **Active Specs:** @.agent-os/specs/
- **Spec Planning:** Use `@.agent-os/instructions/create-spec.md`
- **Tasks Execution:** Use `@.agent-os/instructions/execute-tasks.md`

## Workflow Instructions

When asked to work on this codebase:

1. **First**, check @.agent-os/product/roadmap.md for current priorities
2. **Then**, follow the appropriate instruction file:
   - For new features: @.agent-os/instructions/create-spec.md
   - For tasks execution: @.agent-os/instructions/execute-tasks.md
3. **Always**, adhere to the standards in the files listed above

## Important Notes

- Product-specific files in `.agent-os/product/` override any global standards
- User's specific instructions override (or amend) instructions found in `.agent-os/specs/...`
- Always adhere to established patterns, code style, and best practices documented above.
```

### MERGE BEHAVIOR:
**IF CLAUDE.md exists:**
- **Check for "## Agent OS Documentation" section**
- **IF section exists:** Replace the entire section with new content
- **IF section doesn't exist:** Append new content with "\n\n" separator

**IF CLAUDE.md doesn't exist:**
- **Create new file** with the Agent OS documentation content

**PRESERVE:** All other existing content in the file

**CONFIRM:** State "CLAUDE.md updated with Agent OS documentation section."

---

## EXECUTION CHECKLIST
**VERIFY ALL COMPLETED BEFORE DECLARING SUCCESS:**
- [ ] All 4 required inputs collected from user
- [ ] .agent-os/product/ directory created
- [ ] mission.md created with complete product vision
- [ ] tech-stack.md created (requested missing items if needed)
- [ ] roadmap.md created with 5 phases and effort estimates
- [ ] decisions.md created with initial planning decision
- [ ] CLAUDE.md created or updated with Agent OS section
- [ ] All files use correct templates and structure

**SUCCESS MESSAGE:**
```
✅ Agent OS Product Documentation Complete

Created:
- .agent-os/product/mission.md - Product vision and user personas
- .agent-os/product/tech-stack.md - Technical architecture
- .agent-os/product/roadmap.md - 5-phase development plan
- .agent-os/product/decisions.md - Decision log with override authority
- CLAUDE.md - Updated with Agent OS workflow instructions

Your project is now Agent OS-enabled! Use @.agent-os/instructions/create-spec.md to start building features.
```

**ONLY DECLARE SUCCESS AFTER ALL CHECKBOXES COMPLETED**