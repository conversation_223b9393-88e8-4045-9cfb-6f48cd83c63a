# Task Execution Instructions

## CRITICAL EXECUTION RULES
**EXECUTE SEQUENTIALLY. FOLLOW TDD. WAIT FOR APPROVALS. NO SHORTCUTS.**

---

## STEP 1: TASK ASSIGNMENT
**ACTION REQUIRED:** Identify which task(s) to execute

### TASK SELECTION LOGIC:
**IF user specifies exact task(s):**
- Use their specified task(s)
- Confirm: "Executing [TASK_NAME] as requested"

**IF no task specified:**
- Find next uncompleted parent task in tasks.md
- Confirm: "Found next task: [TASK_NAME]. Proceeding with this task."

**CONFIRM TASK SELECTION:** Always state which task you're executing before proceeding.

---

## STEP 2: CONTEXT ANALYSIS
**ACTION REQUIRED:** Read all relevant documentation

### MANDATORY READS:
1. **Spec SRD file** - Main requirements
2. **Spec tasks.md** - Task details and dependencies
3. **All files in spec sub-specs/ folder** - Technical specs, API specs, database schema, tests
4. **@.agent-os/product/mission.md** - Overall product alignment

**CONFIRM COMPLETION:** State "Context analysis complete. Understanding requirements for [TASK_NAME]."

---

## STEP 3: IMPLEMENTATION PLANNING
**ACTION REQUIRED:** Create detailed execution plan and get user approval

### CREATE IMPLEMENTATION PLAN:
**FORMAT:**
```
## Implementation Plan for [TASK_NAME]

1. **[MAJOR_STEP_1]**
   - [SPECIFIC_ACTION]
   - [SPECIFIC_ACTION]

2. **[MAJOR_STEP_2]**
   - [SPECIFIC_ACTION]
   - [SPECIFIC_ACTION]

**Dependencies to Install:**
- [LIBRARY_NAME] - [PURPOSE]

**Test Strategy:**
- [TEST_APPROACH_DESCRIPTION]
```

**PRESENT TO USER:**
"I've prepared the above implementation plan. Please review and confirm before I proceed with execution."

**WAIT FOR EXPLICIT APPROVAL** - Do not proceed without "yes" or clear confirmation.

---

## STEP 4: DEVELOPMENT SERVER CHECK
**ACTION REQUIRED:** Check for running development server

**IF development server is running:**
- Ask user: "A development server is currently running. Should I shut it down before proceeding? (yes/no)"
- **WAIT FOR RESPONSE**

**IF no server detected:**
- Proceed immediately to Step 5

**CONFIRM:** State server status before proceeding.

---

## STEP 5: GIT BRANCH MANAGEMENT
**ACTION REQUIRED:** Ensure proper git branch setup

### BRANCH NAMING:
- **Format:** Remove date prefix from spec folder name
- **Example:** Folder `2025-03-15-password-reset` → Branch `password-reset`

### BRANCH LOGIC:
**CASE A - Current branch matches spec name:**
- Proceed immediately

**CASE B - Current branch is main/staging/review:**
- Create new branch and proceed

**CASE C - Current branch is different feature:**
- Ask: "Current branch: [CURRENT_BRANCH]. This spec needs branch: [SPEC_BRANCH]. May I create a new branch for this spec? (yes/no)"
- **WAIT FOR RESPONSE**

**CONFIRM:** State final branch being used.

---

## STEP 6: DEVELOPMENT EXECUTION
**ACTION REQUIRED:** Execute the approved implementation plan

### EXECUTION STANDARDS:
- **Follow exactly:** Approved implementation plan
- **Read and follow:** @.agent-os/product/code-style.md
- **Read and follow:** @.agent-os/product/dev-best-practices.md
- **Use TDD approach:** Write tests first, then implementation

### TDD WORKFLOW:
1. Write failing tests first
2. Implement minimal code to pass tests
3. Refactor while keeping tests green
4. Repeat for each feature

**MAINTAIN:** High code quality throughout development.

---

## STEP 7: TASK STATUS UPDATES
**ACTION REQUIRED:** Update tasks.md immediately after each completion

### UPDATE FORMAT:
- **Completed:** `- [x] Task description`
- **Incomplete:** `- [ ] Task description`
- **Blocked:** `- [ ] Task description` + `⚠️ Blocking issue: [DESCRIPTION]`

### BLOCKING CRITERIA:
- **Maximum 3 attempts** at different approaches
- **If still blocked:** Document with ⚠️ emoji and description

**UPDATE IMMEDIATELY:** Mark completed tasks with [x] as soon as finished.

---

## STEP 8: TEST SUITE VERIFICATION
**ACTION REQUIRED:** Run entire test suite and ensure 100% pass rate

### TEST EXECUTION ORDER:
1. **Verify new tests pass**
2. **Run entire test suite**
3. **Fix any failures immediately**

### REQUIREMENTS:
- **100% pass rate required**
- **No proceeding with failing tests**
- **Fix all failures before continuing**

**CONFIRM:** State "All tests passing ✓" before proceeding.

---

## STEP 9: GIT WORKFLOW
**ACTION REQUIRED:** Commit, push, and create pull request

### COMMIT PROCESS:
1. **Commit with descriptive message**
2. **Push to spec branch on origin**
3. **Create pull request**

### PULL REQUEST TEMPLATE:
```
## Summary
[BRIEF_DESCRIPTION_OF_CHANGES]

## Changes Made
- [CHANGE_1]
- [CHANGE_2]

## Testing
- [TEST_COVERAGE_DESCRIPTION]
- All tests passing ✓
```

**CONFIRM:** State "Pull request created: [PR_URL]"

---

## STEP 10: ROADMAP PROGRESS CHECK
**ACTION REQUIRED:** Check if spec completes any roadmap items

### UPDATE CRITERIA:
- **Spec fully implements roadmap feature**
- **All related tasks completed**
- **All tests passing**

### CAUTION:
- **Only mark complete if absolutely certain**
- **Review @.agent-os/product/roadmap.md carefully**

**IF UPDATING:** State which roadmap items were marked complete.
**IF NOT:** State "No roadmap updates needed."

---

## STEP 11: COMPLETION NOTIFICATION
**ACTION REQUIRED:** Play system sound to alert user

**COMMAND:** `afplay /System/Library/Sounds/Glass.aiff`

**PURPOSE:** Alert user that task execution is complete.

---

## STEP 12: COMPLETION SUMMARY
**ACTION REQUIRED:** Provide comprehensive completion summary

### MANDATORY TEMPLATE:
```
## ✅ What's been done
1. **[FEATURE_1]** - [ONE_SENTENCE_DESCRIPTION]
2. **[FEATURE_2]** - [ONE_SENTENCE_DESCRIPTION]

## ⚠️ Issues encountered
[ONLY IF APPLICABLE]
- **[ISSUE_1]** - [DESCRIPTION_AND_REASON]

## 👀 Ready to test in browser
[ONLY IF APPLICABLE]
1. [STEP_1_TO_TEST]
2. [STEP_2_TO_TEST]

## 📦 Pull Request
View PR: [GITHUB_PR_URL]
```

### REQUIRED SECTIONS:
- **What's been done** (always)
- **Pull Request** (always)

### CONDITIONAL SECTIONS:
- **Issues encountered** (if any blocking issues)
- **Ready to test in browser** (if user-testable features)

---

## ERROR HANDLING

### BLOCKING ISSUES:
1. **Attempt 3 different approaches**
2. **Document in tasks.md with ⚠️ emoji**
3. **Include in completion summary**
4. **Ask user for guidance**

### TEST FAILURES:
1. **Never commit broken tests**
2. **Fix immediately before proceeding**
3. **No exceptions to 100% pass requirement**

### TECHNICAL ROADBLOCKS:
1. **Try 3 different solutions**
2. **Document the attempts**
3. **Seek user input if unresolved**

---

## EXECUTION CHECKLIST
**VERIFY ALL COMPLETED BEFORE DECLARING SUCCESS:**
- [ ] Task implementation complete
- [ ] All tests passing (100% pass rate)
- [ ] tasks.md updated with [x] for completed items
- [ ] Code committed with descriptive message
- [ ] Code pushed to GitHub
- [ ] Pull request created with detailed description
- [ ] Roadmap checked and updated if applicable
- [ ] Completion sound played
- [ ] Comprehensive summary provided to user

**ONLY DECLARE TASK COMPLETE AFTER ALL CHECKBOXES VERIFIED**