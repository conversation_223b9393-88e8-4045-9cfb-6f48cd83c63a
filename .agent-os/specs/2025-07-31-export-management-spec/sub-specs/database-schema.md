# Database Schema

This is the database schema specification for @.agent-os/specs/2025-07-31-export-management-spec/spec.md

> Created: 2025-07-31
> Version: 1.0.0

## Existing Models/Tables to Utilize
- `export_jobs` table (Model: `App\Models\ExportJob`)
- `export_templates` table (Model: `App\Models\ExportTemplate`)
- `users` table (Model: `App\Models\User`) - To link export jobs to the user who initiated them.

## No New Database Changes Needed
The existing `export_jobs` and `export_templates` tables are sufficient for managing export data and templates. No new tables or significant column additions are anticipated for this spec. The focus will be on building the UI and business logic around the existing data structures.
