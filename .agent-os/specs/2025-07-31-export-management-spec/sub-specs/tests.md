# Tests Specification

This is the tests specification for @.agent-os/specs/2025-07-31-export-management-spec/spec.md

> Created: 2025-07-31
> Version: 1.0.0

## Unit Tests
- **Models:**
    - `ExportJob`: Test relationships (`user`), attribute casting, and status updates.
    - `ExportTemplate`: Test attribute casting and any custom methods.
- **Services:**
    - `ReportExportService`: Test the core logic for generating different types of exports (e.g., invoices, meter readings).
    - `ExportJobService` (new service if created for business logic): Test methods for initiating, updating status, and retrieving export jobs.

## Integration Tests
- **Livewire Components:**
    - `ExportInitiationForm` (or similar): Test form submission, validation, and successful job queuing.
    - `ExportJobList` (or similar): Test data display, pagination, and status updates.
    - `ExportTemplateForm` (or similar, if applicable): Test CRUD operations for templates.
- **API Endpoints (if implemented):**
    - Test all new API endpoints for export initiation, job status, and download, ensuring proper authorization.
- **Queues:**
    - Test that export jobs are correctly dispatched to the queue.
    - Test that queued jobs execute successfully and update `ExportJob` status.

## Feature Tests
- **End-to-End Scenarios:**
    - As a Management Staff, I can navigate to the Export Management section.
    - As a Management Staff, I can initiate an export for invoices with specific filters.
    - As a Management Staff, I can monitor the export job status and see it transition from "pending" to "completed".
    - As a Management Staff, I can download the completed export file.
    - (If templates are in scope) As a Management Staff, I can create, edit, and delete export templates.
    - Test error handling for failed exports.

## Mocking Requirements
- **File System:** Mock file system interactions for storing and retrieving export files.
- **Queue:** Mock Laravel's queue system to assert job dispatching without actually running workers during unit/integration tests.
- **maatwebsite/excel:** Mock the Excel/CSV generation process to avoid actual file creation during tests.
- **barryvdh/laravel-dompdf:** Mock PDF generation process.
