# Spec Tasks

> Created: 2025-07-27
> Status: Completed

## Tasks

- [x] 1. **Estate Management Views**
  - [x] 1.1 Write tests for EstateManager component.
  - [x] 1.2 Create `EstateManager` Livewire component.
  - [x] 1.3 Implement list view for estates with create, edit, delete buttons.
  - [x] 1.4 Implement create/edit modal/form using Flux UI components.
  - [x] 1.5 Implement delete confirmation modal.
  - [x] 1.6 Connect routes to the `EstateManager` component.
  - [x] 1.7 Verify all tests pass.

- [ ] 2. **House Management Views**
  - [ ] 2.1 Write tests for HouseManager component.
  - [ ] 2.2 Create `HouseManager` Livewire component.
  - [ ] 2.3 Implement list view for houses within an estate.
  - [ ] 2.4 Implement create/edit modal/form for houses.
  - [ ] 2.5 Implement delete confirmation modal.
  - [ ] 2.6 Connect routes to the `HouseManager` component.
  - [ ] 2.7 Verify all tests pass.

- [ ] 3. **Contact Management Views**
  - [ ] 3.1 Write tests for ContactManager component.
  - [ ] 3.2 Update `ContactManager` Livewire component to include full CRUD.
  - [ ] 3.3 Implement list view for contacts.
  - [ ] 3.4 Implement create/edit modal/form for contacts.
  - [ ] 3.5 Implement delete confirmation modal.
  - [ ] 3.6 Connect routes to the `ContactManager` component.
  - [ ] 3.7 Verify all tests pass.

- [ ] 4. **Water Rate Management Views**
  - [ ] 4.1 Write tests for WaterRateManager component.
  - [ ] 4.2 Create `WaterRateManager` Livewire component.
  - [ ] 4.3 Implement list view for water rates.
  - [ ] 4.4 Implement create/edit modal/form for water rates.
  - [ ] 4.5 Implement delete confirmation modal.
  - [ ] 4.6 Connect routes to the `WaterRateManager` component.
  - [ ] 4.7 Verify all tests pass.

- [x] 5. **Route Fixes and RBAC Integration**
  - [x] 5.1 Fix deprecated routes in estate-manager.blade.php - replace generic routes with role-specific routes
  - [x] 5.2 Fix deprecated routes in house-registry.blade.php - replace generic routes with role-specific routes
  - [x] 5.3 Fix deprecated routes in estate-show.blade.php - replace generic routes with role-specific routes
  - [x] 5.4 Fix deprecated routes in house-show.blade.php - replace generic routes with role-specific routes
  - [x] 5.5 Add role-based route methods to all affected Livewire components
  - [x] 5.6 Update all navigation links to use dynamic role-based routing
  - [x] 5.7 Verify all routes work correctly for each user role
  - [x] 5.8 Update specs to reflect completed route fixes
