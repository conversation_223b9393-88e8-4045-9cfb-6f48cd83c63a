# Tests Specification

This is the tests specification for @.agent-os/specs/2025-07-27-add-crud-views-for-userflow/spec.md

> Created: 2025-07-27
> Version: 1.0.0

## Unit Tests
- **Models:** No new model logic is being added, so no new unit tests are required for models.
- **Services:** No new services are being created.
- **Helpers:** No new helpers are being created.

## Integration Tests
- **Livewire Components:**
  - Test that each manager component (`EstateManager`, `HouseManager`, `ContactManager`, `WaterRateManager`) can be rendered.
  - Test that data is passed correctly to the views.
  - Test that actions (e.g., `save`, `delete`) are called correctly when forms are submitted or buttons are clicked.
  - Test validation rules for create and edit forms.

## Feature Tests
- **Estate Management:**
  - Test that an authorized user can access the estate management page.
  - Test that a user can see a list of estates.
  - Test that a user can create a new estate.
  - Test that a user can edit an existing estate.
  - Test that a user can delete an estate.
  - Test that a user can view a single estate's details.
- **House Management:**
  - Test that an authorized user can access the house management page for a given estate.
  - Test that a user can see a list of houses for an estate.
  - Test that a user can create a new house.
  - Test that a user can edit an existing house.
  - Test that a user can delete a house.
- **Contact Management:**
  - Test that an authorized user can access the contact management page.
  - Test that a user can see a list of contacts.
  - Test that a user can create a new contact.
  - Test that a user can edit an existing contact.
  - Test that a user can delete a contact.
- **Water Rate Management:**
  - Test that an authorized user can access the water rate management page.
  - Test that a user can see a list of water rates.
  - Test that a user can create a new water rate.
  - Test that a user can edit an existing water rate.

## Mocking Requirements
- No external services need to be mocked for these feature tests as they primarily interact with the database and internal application logic.
