# Spec Tasks

> Created: 2025-07-31
> Status: Ready for Implementation

## Tasks

- [x] 1. Implement Invoice Listing View
  - [x] 1.1 Write feature tests for InvoiceList component (filtering, pagination, data display)
  - [x] 1.2 Create `app/Livewire/Invoice/InvoiceList.php` Livewire component
  - [x] 1.3 Create `resources/views/livewire/invoice/invoice-list.blade.php` view
  - [x] 1.4 Implement filtering logic (status, estate, house, date range)
  - [x] 1.5 Implement pagination for invoices
  - [x] 1.6 Ensure proper data display for each invoice entry
  - [x] 1.7 Add navigation link to Invoice List from dashboard/sidebar
  - [x] 1.8 Verify all tests pass

- [x] 2. Implement Invoice Detail View and Actions
  - [x] 2.1 Write feature tests for InvoiceDetail component (data display, actions)
  - [x] 2.2 Create `app/Livewire/Invoice/InvoiceDetail.php` Livewire component
  - [x] 2.3 Create `resources/views/livewire/invoice/invoice-detail.blade.php` view
  - [x] 2.4 Display comprehensive invoice details (line items, payments, adjustments)
  - [x] 2.5 Implement "Download PDF" action
  - [x] 2.6 Implement "Send via WhatsApp" action
  - [x] 2.7 Implement "Mark as Paid" action (if applicable, considering payments)
  - [x] 2.8 Verify all tests pass

- [x] 3. Implement Manual Payment Entry
  - [x] 3.1 Write feature tests for InvoicePaymentForm component (submission, validation)
  - [x] 3.2 Create `app/Livewire/Invoice/InvoicePaymentForm.php` Livewire component
  - [x] 3.3 Create `resources/views/livewire/invoice/invoice-payment-form.blade.php` view
  - [x] 3.4 Integrate payment form into Invoice Detail view (e.g., modal or section)
  - [x] 3.5 Implement form validation for amount, date, and description
  - [x] 3.6 Implement logic to record payment against the invoice
  - [x] 3.7 Verify all tests pass

- [x] 4. Implement Manual Adjustment Entry
  - [x] 4.1 Write feature tests for InvoiceAdjustmentForm component (submission, validation)
  - [x] 4.2 Create `app/Livewire/Invoice/InvoiceAdjustmentForm.php` Livewire component
  - [x] 4.3 Create `resources/views/livewire/invoice/invoice-adjustment-form.blade.php` view
  - [x] 4.4 Integrate adjustment form into Invoice Detail view
  - [x] 4.5 Implement form validation for amount, date, and description
  - [x] 4.6 Implement logic to record adjustment against the invoice
  - [x] 4.7 Verify all tests pass
