# Tests Specification

This is the tests specification for @.agent-os/specs/2025-07-31-invoice-management-spec/spec.md

> Created: 2025-07-31
> Version: 1.0.0

## Unit Tests
- **Models:**
    - `Invoice`: Test relationships (`house`, `estate`, `lineItems`, `payments`, `adjustments`), scope methods (e.g., `paid`, `pending`, `overdue`), and attribute casting.
    - `InvoiceLineItem`, `InvoicePayment`, `InvoiceAdjustment`: Test relationships and attribute casting.
- **Services:**
    - `InvoiceGenerationService`: Test the logic for calculating water usage, applying rates, and generating line items.
    - `InvoiceService` (new service if created for business logic): Test methods for marking invoices as paid, adding payments/adjustments.

## Integration Tests
- **Livewire Components:**
    - `InvoiceList` (or similar): Test filtering, pagination, and data display.
    - `InvoiceDetail` (or similar): Test data display, button actions (download, send WhatsApp), and interaction with payment/adjustment forms.
    - `InvoicePaymentForm` (or similar): Test form submission, validation, and successful payment recording.
    - `InvoiceAdjustmentForm` (or similar): Test form submission, validation, and successful adjustment recording.
- **Routes:**
    - Ensure authenticated users with appropriate roles can access invoice management routes.

## Feature Tests
- **End-to-End Scenarios:**
    - As a Management Staff, I can navigate to the Invoice Management section, view a list of invoices, filter them, and click on an invoice to see its details.
    - As a Management Staff, I can view an invoice, add a manual payment, and see the invoice status update.
    - As a Management Staff, I can view an invoice, add a manual adjustment, and see the invoice total update.
    - As a Management Staff, I can download an invoice PDF and trigger a WhatsApp send.

## Mocking Requirements
- **WhatsAppService:** Mock the `WhatsAppService` to prevent actual message sending during tests.
- **PDF Generation:** Mock PDF generation to avoid file system interactions during tests.
- **Date/Time:** Mock `now()` or `Carbon::now()` for consistent date-based testing (e.g., overdue invoices).
