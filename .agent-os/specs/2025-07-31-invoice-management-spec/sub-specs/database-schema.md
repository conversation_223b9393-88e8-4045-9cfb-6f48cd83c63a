# Database Schema

This is the database schema specification for @.agent-os/specs/2025-07-31-invoice-management-spec/spec.md

> Created: 2025-07-31
> Version: 1.0.0

## Existing Models/Tables to Utilize
- `invoices` table (Model: `App\Models\Invoice`)
- `invoice_line_items` table (Model: `App\Models\InvoiceLineItem`)
- `invoice_payments` table (Model: `App\Models\InvoicePayment`)
- `invoice_adjustments` table (Model: `App\Models\InvoiceAdjustment`)
- `users` table (Model: `App\Models\User`)
- `houses` table (Model: `App\Models\House`)
- `estates` table (Model: `App\Models\Estate`)

## No New Database Changes Needed
The existing database schema already supports the core data structures required for invoice management, including invoices, line items, payments, and adjustments. No new tables or significant column additions are anticipated for this spec. The focus will be on building the UI and business logic around the existing data.
