# Spec Requirements Document

> Spec: Invoice Management
> Created: 2025-07-31
> Status: Planning

## Overview
This spec outlines the development of comprehensive invoice management features, allowing users to view, filter, manage, and interact with invoices, payments, and adjustments.

## User Stories
### Invoice Listing and Filtering
As a Management/Review Staff, I want to view a list of all invoices, so that I can quickly find specific invoices and monitor billing status.
Detailed workflow description: Users should be able to see a paginated list of invoices, with filtering options by status (paid, pending, overdue), estate, house, and date range. Each invoice entry should display key information like invoice number, amount, due date, and current status.

### Invoice Details and Actions
As a Management/Review Staff, I want to view detailed information for a specific invoice and perform actions, so that I can understand the full billing breakdown and manage its lifecycle.
Detailed workflow description: Clicking on an invoice from the list should open a detailed view showing all line items, associated payments, and adjustments. Actions like marking as paid, adding a payment, adding an adjustment, downloading PDF, or sending via WhatsApp should be available.

### Manual Payment/Adjustment Entry
As a Management/Review Staff, I want to manually record payments and adjustments against an invoice, so that I can accurately reconcile accounts.
Detailed workflow description: A form should be available within the invoice details view to add new payments or adjustments, specifying amount, date, and a description.

## Spec Scope
1. **Invoice Listing View** - Display a paginated and filterable list of all invoices.
2. **Invoice Detail View** - Show comprehensive details of a single invoice, including line items, payments, and adjustments.
3. **Invoice Actions** - Enable actions like download PDF, send via WhatsApp, mark as paid.
4. **Manual Payment Entry** - Provide a form to record payments against an invoice.
5. **Manual Adjustment Entry** - Provide a form to record adjustments against an invoice.

## Out of Scope
- Automated payment gateway integration (e.g., M-Pesa)
- Complex reporting beyond basic filtering
- Resident-facing portal for invoice viewing (covered in Phase 4 roadmap)

## Expected Deliverable
1. A functional Invoice Management section accessible from the dashboard.
2. A list view displaying invoices with filtering and pagination.
3. A detailed view for individual invoices with all relevant financial data.
4. Forms for adding payments and adjustments to invoices.
5. Integration with existing invoice generation and WhatsApp sending functionalities.

## Spec Documentation
- Tasks: @.agent-os/specs/2025-07-31-invoice-management-spec/tasks.md
- Technical Specification: @.agent-os/specs/2025-07-31-invoice-management-spec/sub-specs/technical-spec.md
- Database Schema: @.agent-os/specs/2025-07-31-invoice-management-spec/sub-specs/database-schema.md
- API Spec: @.agent-os/specs/2025-07-31-invoice-management-spec/sub-specs/api-spec.md
- Tests Spec: @.agent-os/specs/2025-07-31-invoice-management-spec/sub-specs/tests.md
