# Technical Spec: Fix Failing Tests

**Objective:** Provide a detailed technical plan for fixing the failing tests.

## Part 1: Fix Blade Component Errors

The root cause of the `InvalidArgumentException` is that views are trying to render Blade components that were deleted in the `replace-custom-components-with-flux` merge. The solution is to replace the old component tags with the new `flux` component tags.

**Affected Files:**

Based on the test output, the following views are the main culprits:

*   `resources/views/livewire/auth/login.blade.php`
*   `resources/views/livewire/auth/register.blade.php`
*   `resources/views/livewire/auth/forgot-password.blade.php`
*   `resources/views/livewire/auth/reset-password.blade.php`
*   `resources/views/livewire/auth/confirm-password.blade.php`
*   `resources/views/livewire/settings/profile.blade.php`
*   `resources/views/dashboard.blade.php`

**Changes Required:**

I will perform the following replacements in the affected files:

*   `<x-auth-header>` -> `<flux-header>`
*   `<x-input>` -> `<flux-input>`
*   `<x-label>` -> `<flux-label>`
*   `<x-button>` -> `<flux-button>`
*   `<x-input-error>` -> `<flux-input-error>`
*   `<x-action-message>` -> `<flux-action-message>`
*   `<x-auth-session-status>` -> `<flux-auth-session-status>`

I will need to carefully check the attributes of each component to ensure they are still valid with the new Flux components.

## Part 2: Fix `WhatsAppMessageTest` Failures

These failures are more complex and require a more detailed investigation.

**1. `can create whatsapp message` & `invoice relationship`:**

*   **Problem:** The `WhatsAppMessage` is not being created in the database, and the relationship with `Invoice` is not being established correctly.
*   **Solution:** I will examine the `WhatsAppMessageFactory` and the test setup. I suspect the `forInvoice` state in the factory is not correctly setting the `messageable_id` and `messageable_type`. I will also ensure that the `Contact` and `House` used in the test are correctly set up to allow message creation.

**2. `scope filters`:**

*   **Problem:** The `sent()` scope is missing from the `WhatsAppMessage` model.
*   **Solution:** I will add the `scopeSent` method to the `WhatsAppMessage` model:

```php
public function scopeSent($query)
{
    return $query->where('status', 'sent');
}
```

**3. `estate scope filter`, `house scope filter`, `for invoice scope`:**

*   **Problem:** The assertions for these scopes are failing.
*   **Solution:** I will debug the scopes and the test data. It's likely that the factories are not creating the data in the way the test expects. I will use `dd()` or `Log` to inspect the data being generated and the queries being executed.

**4. `retry count functionality`:**

*   **Problem:** `$message->fresh()` is returning `null`.
*   **Solution:** This is a strong indication that the `$message` was never saved to the database in the first place. This is likely related to the first issue. Fixing the creation of the message should also fix this test.

**Execution Order:**

1.  Fix all Blade component errors.
2.  Run tests to confirm that all `Auth`, `Dashboard`, and `Settings` tests pass.
3.  Address the `WhatsAppMessageTest` failures, starting with the creation and relationship issues.
4.  Implement the `sent()` scope.
5.  Debug the other scope failures.
6.  Verify that the `retry count` test passes after fixing the creation issue.
7.  Run all tests one last time to ensure everything is passing.
