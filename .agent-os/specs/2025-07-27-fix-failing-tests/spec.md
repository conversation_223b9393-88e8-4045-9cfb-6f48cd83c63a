# Spec: Fix Failing Tests

**Objective:** Resolve all failing tests introduced after merging the `replace-custom-components-with-flux` branch.

**Analysis of Failures:**

The test failures can be categorized into two main groups:

1.  **Missing Blade Components:** The most frequent error is `InvalidArgumentException: Unable to locate a class or view for component [flux-...]`. This is a direct result of removing the old custom components and replacing them with the Flux component library. The views used in the tests have not been updated to use the new Flux components.

2.  **`WhatsAppMessageTest` Failures:** Several tests within `WhatsAppMessageTest` are failing for various reasons, including database assertion errors, incorrect relationship mocking, and undefined method calls. These seem to be unrelated to the component changes and might be pre-existing issues or bugs introduced during development.

**Plan:**

1.  **Fix Missing Component Errors:**
    *   Systematically go through each failing test in the `Auth`, `Dashboard`, and `Settings` suites.
    *   Identify the Blade views being rendered by these tests.
    *   Update the views to use the correct Flux components instead of the old, deleted components. This will involve replacing tags like `<x-auth-header>` with their `flux-` equivalents.

2.  **Address `WhatsAppMessageTest` Failures:**
    *   **`can create whatsapp message`:** Investigate why the message is not being saved to the database. This could be a validation issue or a problem with the factory.
    *   **`invoice relationship`:** Correctly mock the `Invoice` relationship in the test setup.
    *   **`scope filters`:** The `sent()` scope is missing from the `WhatsAppMessage` model. This needs to be implemented.
    *   **`estate scope filter`, `house scope filter`, `for invoice scope`:** The assertions are failing. Review the factory logic and the scope implementations to ensure they are working as expected.
    *   **`retry count functionality`:** The `fresh()` method is returning null. This indicates the model is not being persisted correctly in the test.

**Execution:**

This will be a multi-step process. I will tackle the component-related failures first, as they are the most widespread. Then, I will move on to the `WhatsAppMessageTest` failures, addressing them one by one.
