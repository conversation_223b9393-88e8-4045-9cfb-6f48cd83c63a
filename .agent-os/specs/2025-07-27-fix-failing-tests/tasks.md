# Tasks: Fix Failing Tests

- [ ] **Task 1: Update Auth Views**
    - [ ] `resources/views/livewire/auth/login.blade.php`
    - [ ] `resources/views/livewire/auth/register.blade.php`
    - [ ] `resources/views/livewire/auth/forgot-password.blade.php`
    - [ ] `resources/views/livewire/auth/reset-password.blade.php`
    - [ ] `resources/views/livewire/auth/confirm-password.blade.php`

- [ ] **Task 2: Update Settings View**
    - [ ] `resources/views/livewire/settings/profile.blade.php`

- [ ] **Task 3: Update Dashboard View**
    - [ ] `resources/views/dashboard.blade.php`

- [ ] **Task 4: Fix `WhatsAppMessage` Creation**
    - [ ] Analyze and fix `WhatsAppMessageFactory`.
    - [ ] Ensure `forInvoice` state works correctly.

- [ ] **Task 5: Implement `sent()` Scope**
    - [ ] Add `scopeSent()` to `WhatsAppMessage` model.

- [ ] **Task 6: Debug and Fix <PERSON>ope Tests**
    - [ ] `estate scope filter`
    - [ ] `house scope filter`
    - [ ] `for invoice scope`

- [ ] **Task 7: Verify `retry count` Test**
    - [ ] Ensure the test passes after fixing message creation.

- [ ] **Task 8: Final Test Run**
    - [ ] Run the entire test suite to confirm all tests pass.
