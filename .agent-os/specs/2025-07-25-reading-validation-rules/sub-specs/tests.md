# Tests Specification

This is the tests coverage details for the spec detailed in @.agent-os/specs/2025-07-25-reading-validation-rules/spec.md

> Created: 2025-07-25
> Version: 1.0.0

## Test Coverage

### Unit Tests

**ReadingValidationService**
- Validates basic reading requirements (value, date, photo, GPS)
- Calculates confidence scores based on validation results
- Handles estate-specific rule configurations
- Processes validation results correctly
- Handles edge cases (null values, missing data)

**RuleEngine**
- Executes all registered validation rules
- Aggregates results from multiple rules correctly
- Handles rule configuration overrides
- Processes rules in correct order
- Handles rule failures gracefully

**BasicValidationRule**
- Validates reading value is positive number
- Validates reading date is not in future
- Validates photo is present when required
- Validates GPS coordinates are within estate boundaries
- <PERSON><PERSON> missing optional fields appropriately

**ConsumptionValidationRule**
- Validates consumption is positive (except first reading)
- Validates consumption is within reasonable thresholds
- Validates sequential readings show increase
- Validates daily consumption rate is reasonable
- Handles meter reset scenarios

**StatisticalAnomalyRule**
- Calculates z-scores correctly for consumption data
- Identifies readings beyond threshold standard deviations
- Compares against historical baselines
- Handles insufficient historical data
- Calculates percentiles accurately

**PatternAnomalyRule**
- Detects sudden consumption spikes (>300% increase)
- Detects sudden consumption drops (>90% decrease)
- Identifies zero consumption patterns
- Recognizes unusual usage patterns
- Handles seasonal variations

**ContextualRule**
- Applies seasonal adjustments correctly
- Correlates consumption with occupancy data
- Adjusts for rate changes
- Handles estate-wide events
- Validates billing period alignment

### Integration Tests

**Validation API Endpoints**
- POST /api/readings/{id}/validate triggers validation correctly
- GET /api/readings/flagged returns filtered results
- PUT /api/readings/{id}/review updates status appropriately
- Authorization checks work correctly for different user roles
- Rate limiting prevents abuse

**Database Integration**
- Validation results are stored correctly in meter_readings
- Validation logs are created for each rule execution
- Estate-specific rules override global configuration
- Historical baselines are calculated and stored correctly
- Transaction rollback on validation failures

**Estate Configuration**
- Estate managers can update validation rules
- Rule configurations are validated before storage
- Global rules apply when estate-specific rules are missing
- Configuration changes take effect immediately
- Invalid configurations are rejected with clear errors

**Validation Workflow**
- Complete reading submission to validation flow
- Flagged readings appear in reviewer dashboard
- Manual review process updates reading status
- Notification system triggers for high-severity anomalies
- Batch processing handles multiple readings efficiently

### Feature Tests

**End-to-End Reading Validation**
- Caretaker submits reading → system validates → auto-approves valid readings
- Caretaker submits anomalous reading → system flags → reviewer reviews → approves/rejects
- Estate manager configures custom rules → new readings use updated rules
- System generates validation reports for management review
- Historical baseline recalculation updates validation thresholds

**Reviewer Dashboard Workflow**
- Reviewer logs in → sees flagged readings list
- Reviewer filters by severity/estate/date range
- Reviewer opens reading details → sees validation reasons
- Reviewer approves/rejects with notes → system updates status
- Reviewer exports flagged readings report

**Configuration Management**
- Estate manager accesses validation settings
- Manager updates consumption thresholds
- New readings immediately use updated rules
- System validates configuration changes
- Rollback mechanism for invalid configurations

### Mocking Requirements

**External Services**
- **GPS Validation Service**: Mock coordinate boundary checks
- **File Storage**: Mock photo upload/validation for reading photos
- **Notification Service**: Mock WhatsApp/email notifications for anomalies
- **Cache Service**: Mock Redis caching for validation baselines

**Database Queries**
- Mock historical reading data for statistical calculations
- Mock estate configuration retrieval
- Mock user role/permission checks
- Mock validation log storage

**Time-based Tests**
- Mock current date for seasonal adjustment tests
- Mock reading submission dates for billing period validation
- Mock created_at timestamps for age-based filtering
- Mock updated_at for configuration change detection

**Large Dataset Tests**
- Generate test data with 1000+ readings for performance testing
- Create readings with known statistical properties
- Simulate estate with varying consumption patterns
- Generate edge case data (meter resets, zero consumption, etc.)

### Test Data Setup

**Test Estates**
- Estate with standard validation rules
- Estate with custom validation rules
- Estate with disabled validation rules
- Estate with insufficient historical data

**Test Houses**
- House with consistent consumption patterns
- House with seasonal variations
- House with occupancy changes
- House with meter replacement history

**Test Readings**
- Valid readings across different consumption ranges
- Readings with basic validation failures
- Readings with statistical anomalies
- Readings with pattern anomalies
- Readings requiring contextual validation

### Performance Tests

**Validation Speed**
- Single reading validation completes within 100ms
- Batch validation of 100 readings completes within 5 seconds
- Statistical calculations handle 1000+ historical readings efficiently
- Memory usage stays under 128MB for large datasets

**Database Performance**
- Validation queries use appropriate indexes
- Baseline calculations complete within 30 seconds for 1000 houses
- Flagged readings queries return results within 1 second
- Configuration lookups complete within 50ms

### Security Tests

**Authorization**
- Users can only validate readings for their assigned estates
- Reviewers can only update readings they have permission to review
- Estate managers can only configure their own estate rules
- Unauthorized access attempts are properly rejected

**Input Validation**
- Malicious input is sanitized before processing
- SQL injection attempts are prevented
- XSS attempts in validation messages are blocked
- File upload validation prevents malicious uploads

**Rate Limiting**
- Excessive validation requests are blocked
- Rule configuration updates are rate-limited
- API abuse attempts are logged and blocked
- Legitimate usage patterns are not affected