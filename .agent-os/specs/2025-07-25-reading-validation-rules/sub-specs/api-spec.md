# API Specification

This is the API specification for the spec detailed in @.agent-os/specs/2025-07-25-reading-validation-rules/spec.md

> Created: 2025-07-25
> Version: 1.0.0

## Endpoints

### POST /api/readings/{id}/validate

**Purpose:** Manually trigger validation for a specific meter reading
**Parameters:**
- `id` (path): Meter reading ID
- `force` (query, optional): Force re-validation even if already validated

**Response:**
```json
{
  "success": true,
  "data": {
    "reading_id": 123,
    "validation_score": 0.85,
    "status": "valid",
    "severity": "low",
    "errors": [],
    "warnings": ["Consumption slightly above average"],
    "anomalies": []
  }
}
```

**Errors:**
- 404: Reading not found
- 403: Unauthorized access
- 422: Validation failed

### GET /api/readings/flagged

**Purpose:** Retrieve flagged readings for review
**Parameters:**
- `estate_id` (query, optional): Filter by estate
- `severity` (query, optional): Filter by severity (low/medium/high)
- `status` (query, optional): Filter by validation status
- `page` (query, optional): Pagination page (default: 1)
- `per_page` (query, optional): Items per page (default: 20)

**Response:**
```json
{
  "data": [
    {
      "id": 123,
      "house": {
        "id": 456,
        "house_number": "A-101",
        "estate": {
          "id": 789,
          "name": "Green Valley Estate"
        }
      },
      "reading_value": 1250.5,
      "previous_reading": 1000.0,
      "consumption": 250.5,
      "reading_date": "2025-07-20",
      "validation_score": 0.25,
      "severity": "high",
      "validation_errors": ["Consumption exceeds 300% of average"],
      "validation_warnings": ["GPS coordinates slightly outside estate"],
      "created_at": "2025-07-20T10:30:00Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 20,
    "total": 45,
    "last_page": 3
  }
}
```

### PUT /api/readings/{id}/review

**Purpose:** Update validation status after manual review
**Parameters:**
- `id` (path): Meter reading ID
- `status` (body): New status (valid/rejected)
- `review_notes` (body, optional): Reviewer notes
- `override_rules` (body, optional): Rules to override

**Request:**
```json
{
  "status": "valid",
  "review_notes": "Verified with customer - pool filling caused spike",
  "override_rules": ["consumption_spike"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "reading_id": 123,
    "status": "valid",
    "reviewed_by": 789,
    "reviewed_at": "2025-07-25T14:30:00Z",
    "review_notes": "Verified with customer - pool filling caused spike"
  }
}
```

### GET /api/estates/{id}/validation-rules

**Purpose:** Get validation rules configuration for an estate
**Parameters:**
- `id` (path): Estate ID

**Response:**
```json
{
  "estate_id": 789,
  "rules": {
    "basic": {
      "photo_required": true,
      "gps_required": true
    },
    "consumption": {
      "max_daily_rate": 1000,
      "max_monthly_increase": 500,
      "min_monthly_decrease": 90
    },
    "statistical": {
      "z_score_threshold": 2.0,
      "percentile_threshold": 5
    }
  },
  "is_customized": true
}
```

### PUT /api/estates/{id}/validation-rules

**Purpose:** Update validation rules for an estate
**Parameters:**
- `id` (path): Estate ID
- `rules` (body): Rule configuration object

**Request:**
```json
{
  "rules": {
    "consumption": {
      "max_daily_rate": 1500,
      "max_monthly_increase": 400
    },
    "statistical": {
      "z_score_threshold": 1.5
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "estate_id": 789,
    "updated_rules": [...],
    "updated_at": "2025-07-25T15:00:00Z"
  }
}
```

### GET /api/validation/stats

**Purpose:** Get validation statistics and metrics
**Parameters:**
- `estate_id` (query, optional): Filter by estate
- `date_from` (query, optional): Start date (YYYY-MM-DD)
- `date_to` (query, optional): End date (YYYY-MM-DD)

**Response:**
```json
{
  "data": {
    "total_readings": 1250,
    "valid_readings": 1100,
    "flagged_readings": 120,
    "rejected_readings": 30,
    "by_severity": {
      "low": 80,
      "medium": 30,
      "high": 10
    },
    "by_rule_type": {
      "basic": 25,
      "consumption": 60,
      "statistical": 20,
      "pattern": 10,
      "contextual": 5
    },
    "average_validation_score": 0.87
  }
}
```

## Controllers

### ValidationController
- `validateReading($id)` - Trigger validation for specific reading
- `getFlaggedReadings(Request $request)` - List flagged readings with filters
- `reviewReading($id, Request $request)` - Update reading after manual review

### EstateValidationController
- `getRules($estateId)` - Get estate-specific validation rules
- `updateRules($estateId, Request $request)` - Update estate validation rules

### ValidationStatsController
- `getStats(Request $request)` - Get validation statistics and metrics

## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Reading value must be greater than previous reading",
    "details": {
      "field": "reading_value",
      "previous_value": 1000,
      "current_value": 999
    }
  }
}
```

### Error Codes
- `READING_NOT_FOUND`: Specified reading does not exist
- `UNAUTHORIZED_ACCESS`: User lacks permission for this estate
- `VALIDATION_ERROR`: Business rule validation failed
- `INVALID_RULE_CONFIG`: Validation rule configuration is invalid
- `RATE_LIMIT_EXCEEDED`: Too many validation requests

## Rate Limiting

- **Validation requests**: 60 per minute per user
- **Rule updates**: 10 per minute per estate
- **Stats requests**: 100 per minute per user

## Authentication & Authorization

- **Authentication**: Laravel Sanctum token-based
- **Authorization**: Role-based access control
  - Reviewers: Can view flagged readings and update status
  - Estate Managers: Can configure estate validation rules
  - Management: Can view all validation stats
  - Caretakers: Can trigger validation for their assigned readings