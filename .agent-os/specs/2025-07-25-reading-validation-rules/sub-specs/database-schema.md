# Database Schema

This is the database schema implementation for the spec detailed in @.agent-os/specs/2025-07-25-reading-validation-rules/spec.md

> Created: 2025-07-25
> Version: 1.0.0

## Database Changes

### New Columns in meter_readings table
```sql
-- Add validation-related columns to existing meter_readings table
ALTER TABLE meter_readings ADD COLUMN validation_score DECIMAL(3,2) DEFAULT NULL;
ALTER TABLE meter_readings ADD COLUMN validation_errors JSON DEFAULT NULL;
ALTER TABLE meter_readings ADD COLUMN validation_warnings JSON DEFAULT NULL;
ALTER TABLE meter_readings ADD COLUMN anomalies JSON DEFAULT NULL;
ALTER TABLE meter_readings ADD COLUMN severity ENUM('low', 'medium', 'high') DEFAULT NULL;
ALTER TABLE meter_readings ADD COLUMN validation_status ENUM('pending', 'valid', 'flagged', 'rejected') DEFAULT 'pending';
ALTER TABLE meter_readings ADD COLUMN validated_at TIMESTAMP NULL DEFAULT NULL;
ALTER TABLE meter_readings ADD COLUMN validated_by BIGINT UNSIGNED NULL DEFAULT NULL;
```

### New validation_logs table
```sql
CREATE TABLE validation_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    meter_reading_id BIGINT UNSIGNED NOT NULL,
    rule_name VARCHAR(100) NOT NULL,
    rule_type ENUM('basic', 'consumption', 'statistical', 'pattern', 'contextual') NOT NULL,
    result ENUM('pass', 'fail', 'warning') NOT NULL,
    message TEXT,
    severity ENUM('low', 'medium', 'high') DEFAULT 'low',
    metadata JSON DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (meter_reading_id) REFERENCES meter_readings(id) ON DELETE CASCADE,
    INDEX idx_reading_id (meter_reading_id),
    INDEX idx_rule_type (rule_type),
    INDEX idx_result (result),
    INDEX idx_created_at (created_at)
);
```

### New estate_validation_rules table
```sql
CREATE TABLE estate_validation_rules (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    estate_id BIGINT UNSIGNED NOT NULL,
    rule_name VARCHAR(100) NOT NULL,
    rule_config JSON NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (estate_id) REFERENCES estates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_estate_rule (estate_id, rule_name),
    INDEX idx_estate_id (estate_id),
    INDEX idx_active (is_active)
);
```

### New validation_baselines table
```sql
CREATE TABLE validation_baselines (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    house_id BIGINT UNSIGNED NOT NULL,
    baseline_type ENUM('daily', 'weekly', 'monthly', 'seasonal') NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    average_consumption DECIMAL(10,2) NOT NULL,
    standard_deviation DECIMAL(10,2) DEFAULT NULL,
    sample_size INT NOT NULL,
    metadata JSON DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (house_id) REFERENCES houses(id) ON DELETE CASCADE,
    INDEX idx_house_type (house_id, baseline_type),
    INDEX idx_period (period_start, period_end),
    INDEX idx_updated_at (updated_at)
);
```

## Indexes for Performance

### Existing table indexes
```sql
-- Optimize meter_readings for validation queries
CREATE INDEX idx_validation_status ON meter_readings(validation_status);
CREATE INDEX idx_severity ON meter_readings(severity);
CREATE INDEX idx_validation_score ON meter_readings(validation_score);
CREATE INDEX idx_validated_at ON meter_readings(validated_at);
CREATE INDEX idx_reading_date_house ON meter_readings(reading_date, house_id);
```

## Migration Specifications

### Migration 1: Add validation columns to meter_readings
```php
// database/migrations/2025_07_25_100000_add_validation_fields_to_meter_readings.php
Schema::table('meter_readings', function (Blueprint $table) {
    $table->decimal('validation_score', 3, 2)->nullable()->after('photo_path');
    $table->json('validation_errors')->nullable()->after('validation_score');
    $table->json('validation_warnings')->nullable()->after('validation_errors');
    $table->json('anomalies')->nullable()->after('validation_warnings');
    $table->enum('severity', ['low', 'medium', 'high'])->nullable()->after('anomalies');
    $table->enum('validation_status', ['pending', 'valid', 'flagged', 'rejected'])
          ->default('pending')->after('status');
    $table->timestamp('validated_at')->nullable()->after('updated_at');
    $table->foreignId('validated_by')->nullable()->constrained('users')->onDelete('set null');
});
```

### Migration 2: Create validation_logs table
```php
// database/migrations/2025_07_25_100001_create_validation_logs_table.php
Schema::create('validation_logs', function (Blueprint $table) {
    $table->id();
    $table->foreignId('meter_reading_id')->constrained()->onDelete('cascade');
    $table->string('rule_name', 100);
    $table->enum('rule_type', ['basic', 'consumption', 'statistical', 'pattern', 'contextual']);
    $table->enum('result', ['pass', 'fail', 'warning']);
    $table->text('message')->nullable();
    $table->enum('severity', ['low', 'medium', 'high'])->default('low');
    $table->json('metadata')->nullable();
    $table->timestamps();
    
    $table->index(['meter_reading_id', 'rule_type']);
    $table->index(['rule_name', 'result']);
});
```

### Migration 3: Create estate_validation_rules table
```php
// database/migrations/2025_07_25_100002_create_estate_validation_rules_table.php
Schema::create('estate_validation_rules', function (Blueprint $table) {
    $table->id();
    $table->foreignId('estate_id')->constrained()->onDelete('cascade');
    $table->string('rule_name', 100);
    $table->json('rule_config');
    $table->boolean('is_active')->default(true);
    $table->timestamps();
    
    $table->unique(['estate_id', 'rule_name']);
    $table->index(['estate_id', 'is_active']);
});
```

### Migration 4: Create validation_baselines table
```php
// database/migrations/2025_07_25_100003_create_validation_baselines_table.php
Schema::create('validation_baselines', function (Blueprint $table) {
    $table->id();
    $table->foreignId('house_id')->constrained()->onDelete('cascade');
    $table->enum('baseline_type', ['daily', 'weekly', 'monthly', 'seasonal']);
    $table->date('period_start');
    $table->date('period_end');
    $table->decimal('average_consumption', 10, 2);
    $table->decimal('standard_deviation', 10, 2)->nullable();
    $table->integer('sample_size');
    $table->json('metadata')->nullable();
    $table->timestamps();
    
    $table->index(['house_id', 'baseline_type']);
    $table->index(['period_start', 'period_end']);
});
```

## Data Integrity Rules

### Foreign Key Constraints
- All validation logs must reference existing meter readings
- Estate rules must reference existing estates
- Validation baselines must reference existing houses
- Validated_by must reference existing users

### Validation Rules
- validation_score must be between 0.00 and 1.00
- period_end must be after period_start in validation_baselines
- sample_size must be positive in validation_baselines
- rule_config must be valid JSON in estate_validation_rules

## Performance Considerations

### Query Optimization
- Use composite indexes for common query patterns
- Partition validation_logs by date for large datasets
- Cache validation baselines in Redis for frequent access
- Use database views for complex validation queries

### Data Retention
- Archive validation_logs older than 2 years
- Recalculate validation baselines monthly
- Clean up orphaned validation data
- Monitor table sizes and query performance