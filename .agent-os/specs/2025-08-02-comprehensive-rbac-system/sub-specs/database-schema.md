# Database Schema Specification

> Spec: Comprehensive Role-Based Access Control (RBAC) System
> Created: 2025-08-02
> Status: Planning

## Schema Overview

The RBAC system requires several new database tables to support granular permissions, estate-based access control, user overrides, and comprehensive audit logging. This specification details all schema changes required for implementation.

## New Tables

### 1. Permissions Table

**Purpose**: Stores all available permissions in the system with metadata for categorization and validation.

```sql
CREATE TABLE permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    category VARCHAR(50) NOT NULL,
    requires_estate_assignment BOOLEAN DEFAULT 1,
    system_level_only BOOLEAN DEFAULT 0,
    allow_user_overrides BOOLEAN DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_permissions_category_active (category, is_active),
    INDEX idx_permissions_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Columns**:
- `id`: Primary key
- `name`: Unique permission identifier (e.g., 'estates.view_assigned')
- `display_name`: Human-readable permission name (e.g., 'View Assigned Estates')
- `description`: Detailed description of what the permission allows
- `category`: Permission category for organization (dashboard, estate, house, contact, reading, billing, reports, users, system, resident)
- `requires_estate_assignment`: Whether permission requires estate assignment validation
- `system_level_only`: Whether permission is only available to system administrators
- `allow_user_overrides`: Whether permission can be overridden at user level
- `is_active`: Whether permission is currently active and available
- `created_at`, `updated_at`: Timestamps

**Sample Data**:
```sql
INSERT INTO permissions (name, display_name, description, category, requires_estate_assignment, system_level_only, allow_user_overrides) VALUES
('estates.view_all', 'View All Estates', 'Access to view all estates in the system', 'estate', 0, 1, 0),
('estates.view_assigned', 'View Assigned Estates', 'Access to view estates assigned to user', 'estate', 1, 0, 1),
('estates.manage_assigned', 'Manage Assigned Estates', 'Full management access to assigned estates', 'estate', 1, 0, 1),
('readings.create_assigned', 'Create Assigned Readings', 'Create meter readings for assigned estates', 'reading', 1, 0, 1),
('invoices.generate_assigned', 'Generate Assigned Invoices', 'Generate invoices for assigned estates', 'billing', 1, 0, 1);
```

### 2. Role Permissions Table

**Purpose**: Maps permissions to user roles, defining the default permission set for each role.

```sql
CREATE TABLE role_permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    role VARCHAR(50) NOT NULL,
    permission_id BIGINT UNSIGNED NOT NULL,
    assigned_by BIGINT UNSIGNED NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    UNIQUE KEY uk_role_permissions_role_permission (role, permission_id),
    INDEX idx_role_permissions_role (role),
    INDEX idx_role_permissions_permission (permission_id),
    
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role) REFERENCES user_roles(value) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Columns**:
- `id`: Primary key
- `role`: User role (references UserRole enum values)
- `permission_id`: Foreign key to permissions table
- `assigned_by`: User who assigned this permission to the role
- `assigned_at`: When the permission was assigned
- `created_at`, `updated_at`: Timestamps

**Sample Data**:
```sql
INSERT INTO role_permissions (role, permission_id, assigned_by) VALUES
('admin', 1, 1),  -- estates.view_all assigned to admin by user 1
('admin', 2, 1),  -- estates.view_assigned assigned to admin by user 1
('manager', 2, 1), -- estates.view_assigned assigned to manager by user 1
('manager', 3, 1), -- estates.manage_assigned assigned to manager by user 1
('caretaker', 4, 1); -- readings.create_assigned assigned to caretaker by user 1
```

### 3. User Permission Overrides Table

**Purpose**: Allows individual users to have permissions that differ from their role defaults.

```sql
CREATE TABLE user_permission_overrides (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    permission_id BIGINT UNSIGNED NOT NULL,
    action ENUM('grant', 'revoke') DEFAULT 'grant',
    granted_by BIGINT UNSIGNED NOT NULL,
    expires_at TIMESTAMP NULL,
    reason TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    UNIQUE KEY uk_user_permission_overrides_user_permission (user_id, permission_id),
    INDEX idx_user_permission_overrides_user_expires (user_id, expires_at),
    INDEX idx_user_permission_overrides_permission (permission_id),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Columns**:
- `id`: Primary key
- `user_id`: Foreign key to users table
- `permission_id`: Foreign key to permissions table
- `action`: Whether to grant or revoke this permission
- `granted_by`: User who created this override
- `expires_at`: When this override expires (optional)
- `reason`: Reason for the override (optional)
- `created_at`, `updated_at`: Timestamps

**Sample Data**:
```sql
INSERT INTO user_permission_overrides (user_id, permission_id, action, granted_by, expires_at, reason) VALUES
(2, 1, 'grant', 1, '2025-12-31 23:59:59', 'Temporary admin access during system migration'),
(3, 5, 'revoke', 2, NULL, 'Revoked invoice generation due to training requirements');
```

### 4. Permission Audit Logs Table

**Purpose**: Comprehensive audit trail for all permission-related changes in the system.

```sql
CREATE TABLE permission_audit_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    action VARCHAR(100) NOT NULL,
    details JSON NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    target_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP NULL,
    
    INDEX idx_permission_audit_logs_user_created (user_id, created_at),
    INDEX idx_permission_audit_logs_target (target_type, target_id),
    INDEX idx_permission_audit_logs_action (action),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Columns**:
- `id`: Primary key
- `user_id`: User who performed the action
- `action`: Type of action performed (role_updated, permission_added, override_created, etc.)
- `details`: JSON object containing detailed change information
- `target_type`: Type of target (role, permission, user_override, estate_assignment)
- `target_id`: Identifier of the target
- `ip_address`: IP address of the user making the change
- `user_agent`: User agent string of the client
- `created_at`: When the action was performed

**Sample Data**:
```sql
INSERT INTO permission_audit_logs (user_id, action, details, target_type, target_id, ip_address, user_agent) VALUES
(1, 'role_permissions_updated', '{"role": "manager", "added_permissions": ["estates.analytics"], "removed_permissions": [], "total_permissions": 15}', 'role', 'manager', '*************', 'Mozilla/5.0...'),
(1, 'user_override_created', '{"user_id": 2, "permission": "estates.view_all", "action": "grant", "expires_at": "2025-12-31 23:59:59", "reason": "Temporary admin access"}', 'user_override', '2', '*************', 'Mozilla/5.0...'),
(2, 'estate_assignment_updated', '{"user_id": 3, "added_estates": [1, 2], "removed_estates": [3], "total_estates": 2}', 'estate_assignment', '3', '*************', 'Mozilla/5.0...');
```

### 5. User Estate Assignments Table

**Purpose**: Manages which estates each user has access to, enabling estate-based access control.

```sql
CREATE TABLE user_estate_assignments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    estate_id BIGINT UNSIGNED NOT NULL,
    assigned_by BIGINT UNSIGNED NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    UNIQUE KEY uk_user_estate_assignments_user_estate (user_id, estate_id),
    INDEX idx_user_estate_assignments_user_estate (user_id, estate_id),
    INDEX idx_user_estate_assignments_estate (estate_id),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (estate_id) REFERENCES estates(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Columns**:
- `id`: Primary key
- `user_id`: Foreign key to users table
- `estate_id`: Foreign key to estates table
- `assigned_by`: User who made this assignment
- `assigned_at`: When the assignment was made
- `created_at`, `updated_at`: Timestamps

**Sample Data**:
```sql
INSERT INTO user_estate_assignments (user_id, estate_id, assigned_by) VALUES
(2, 1, 1),  -- User 2 assigned to estate 1 by user 1
(2, 2, 1),  -- User 2 assigned to estate 2 by user 1
(3, 1, 2),  -- User 3 assigned to estate 1 by user 2
(4, 1, 2),  -- User 4 assigned to estate 1 by user 2
(4, 2, 2);  -- User 4 assigned to estate 2 by user 2
```

### 6. User Management Hierarchy Table

**Purpose**: Defines the management hierarchy between users (managers, reviewers, caretakers).

```sql
CREATE TABLE user_management_hierarchy (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    manager_id BIGINT UNSIGNED NOT NULL,
    subordinate_id BIGINT UNSIGNED NOT NULL,
    relationship ENUM('manages', 'oversees') NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    UNIQUE KEY uk_user_management_hierarchy_manager_subordinate (manager_id, subordinate_id),
    INDEX idx_user_management_hierarchy_manager_relationship (manager_id, relationship),
    INDEX idx_user_management_hierarchy_subordinate (subordinate_id),
    
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subordinate_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Columns**:
- `id`: Primary key
- `manager_id`: Foreign key to users table (manager)
- `subordinate_id`: Foreign key to users table (subordinate)
- `relationship`: Type of relationship (manages = direct management, oversees = oversight)
- `created_at`, `updated_at`: Timestamps

**Sample Data**:
```sql
INSERT INTO user_management_hierarchy (manager_id, subordinate_id, relationship) VALUES
(2, 3, 'manages'),    -- User 2 manages user 3
(2, 4, 'manages'),    -- User 2 manages user 4
(2, 5, 'oversees'),   -- User 2 oversees user 5
(3, 6, 'manages');    -- User 3 manages user 6
```

## Existing Table Modifications

### 1. Users Table Updates

**Purpose**: Add support for the new role structure and relationships.

```sql
-- Add new role enum values (this will be handled by the UserRole enum update)
-- No direct schema changes needed as the role column already exists and uses enum casting

-- The existing role column will automatically support the new enum values:
-- 'admin', 'manager', 'reviewer', 'caretaker', 'resident'
```

### 2. Create User Roles Table (if not exists)

**Purpose**: Support proper foreign key relationships for role enum values.

```sql
CREATE TABLE user_roles (
    value VARCHAR(50) PRIMARY KEY,
    label VARCHAR(255) NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO user_roles (value, label, description) VALUES
('admin', 'System Administrator', 'System-wide administrator with full access to all features and settings'),
('manager', 'Estate Manager', 'Estate manager with oversight of assigned estates and team management'),
('reviewer', 'Reviewer/Accountant', 'Accountant who reviews readings and manages billing for assigned estates'),
('caretaker', 'Caretaker Staff', 'Field staff who enters meter readings and updates contact information'),
('resident', 'Resident/Tenant', 'Tenant or house owner with access to personal billing and usage information');
```

## Migration Files

### Migration 1: Create Core RBAC Tables

```php
// database/migrations/2025_08_02_000001_create_rbac_tables.php
class CreateRbacTables extends Migration
{
    public function up()
    {
        Schema::create('user_roles', function (Blueprint $table) {
            $table->string('value')->primary();
            $table->string('label')->notNullable();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->string('category');
            $table->boolean('requires_estate_assignment')->default(true);
            $table->boolean('system_level_only')->default(false);
            $table->boolean('allow_user_overrides')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['category', 'is_active']);
            $table->index('name');
        });

        Schema::create('role_permissions', function (Blueprint $table) {
            $table->id();
            $table->string('role');
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->foreignId('assigned_by')->constrained('users');
            $table->timestamp('assigned_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamps();
            
            $table->unique(['role', 'permission_id']);
            $table->foreign('role')->references('value')->on('user_roles');
        });

        Schema::create('user_permission_overrides', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->enum('action', ['grant', 'revoke'])->default('grant');
            $table->foreignId('granted_by')->constrained('users');
            $table->timestamp('expires_at')->nullable();
            $table->text('reason')->nullable();
            $table->timestamps();
            
            $table->unique(['user_id', 'permission_id']);
            $table->index(['user_id', 'expires_at']);
            $table->index('permission_id');
        });

        Schema::create('permission_audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('action');
            $table->json('details');
            $table->string('target_type');
            $table->string('target_id');
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'created_at']);
            $table->index(['target_type', 'target_id']);
            $table->index('action');
        });

        Schema::create('user_estate_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->foreignId('assigned_by')->constrained('users');
            $table->timestamp('assigned_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamps();
            
            $table->unique(['user_id', 'estate_id']);
            $table->index(['user_id', 'estate_id']);
            $table->index('estate_id');
        });

        Schema::create('user_management_hierarchy', function (Blueprint $table) {
            $table->id();
            $table->foreignId('manager_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('subordinate_id')->constrained('users')->onDelete('cascade');
            $table->enum('relationship', ['manages', 'oversees']);
            $table->timestamps();
            
            $table->unique(['manager_id', 'subordinate_id']);
            $table->index(['manager_id', 'relationship']);
            $table->index('subordinate_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_management_hierarchy');
        Schema::dropIfExists('user_estate_assignments');
        Schema::dropIfExists('permission_audit_logs');
        Schema::dropIfExists('user_permission_overrides');
        Schema::dropIfExists('role_permissions');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('user_roles');
    }
}
```

### Migration 2: Seed Initial Data

```php
// database/migrations/2025_08_02_000002_seed_rbac_data.php
class SeedRbacData extends Migration
{
    public function up()
    {
        // Seed user roles
        DB::table('user_roles')->insert([
            ['value' => 'admin', 'label' => 'System Administrator', 'description' => 'System-wide administrator with full access to all features and settings', 'created_at' => now(), 'updated_at' => now()],
            ['value' => 'manager', 'label' => 'Estate Manager', 'description' => 'Estate manager with oversight of assigned estates and team management', 'created_at' => now(), 'updated_at' => now()],
            ['value' => 'reviewer', 'label' => 'Reviewer/Accountant', 'description' => 'Accountant who reviews readings and manages billing for assigned estates', 'created_at' => now(), 'updated_at' => now()],
            ['value' => 'caretaker', 'label' => 'Caretaker Staff', 'description' => 'Field staff who enters meter readings and updates contact information', 'created_at' => now(), 'updated_at' => now()],
            ['value' => 'resident', 'label' => 'Resident/Tenant', 'description' => 'Tenant or house owner with access to personal billing and usage information', 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Seed permissions (this would be done programmatically in a seeder)
        // The actual permission seeding would be handled by a dedicated seeder class
    }

    public function down()
    {
        DB::table('user_roles')->truncate();
        DB::table('permissions')->truncate();
        DB::table('role_permissions')->truncate();
    }
}
```

## Performance Considerations

### Indexing Strategy

1. **Foreign Key Indexes**: All foreign keys have automatic indexes
2. **Composite Indexes**: Created for common query patterns
3. **Covering Indexes**: Designed to support common query scenarios
4. **Selective Indexes**: Added for frequently filtered columns

### Query Optimization

1. **Estate-based queries**: Use `user_estate_assignments` table for efficient filtering
2. **Permission validation**: Leverage `role_permissions` and `user_permission_overrides` with proper indexing
3. **Audit log queries**: Optimized with timestamp and target-based indexing
4. **Hierarchy queries**: Efficient manager-subordinate relationship lookups

### Data Volume Considerations

1. **Audit Logs**: Consider partitioning for large installations
2. **Permission Overrides**: Regular cleanup of expired overrides
3. **Estate Assignments**: Monitor for assignment bloat
4. **User Hierarchy**: Keep hierarchy depth reasonable for performance

## Security Considerations

### Data Protection

1. **Audit Logs**: Immutable record of all permission changes
2. **User Overrides**: Expiration dates prevent permanent privilege escalation
3. **Assignment Tracking**: Complete history of who assigned what to whom
4. **IP Logging**: Track source of permission changes

### Access Control

1. **Foreign Key Constraints**: Ensure data integrity
2. **Cascading Deletes**: Proper cleanup when users/estates are removed
3. **Unique Constraints**: Prevent duplicate assignments and overrides
4. **Enum Validation**: Ensure only valid relationship types and actions

This database schema provides a robust foundation for the comprehensive RBAC system with proper performance optimization, security considerations, and data integrity guarantees.