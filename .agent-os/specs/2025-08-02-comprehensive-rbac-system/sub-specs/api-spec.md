# API Specification

> Spec: Comprehensive Role-Based Access Control (RBAC) System
> Created: 2025-08-02
> Status: Planning

## API Overview

The RBAC system provides comprehensive RESTful APIs for managing permissions, roles, estate assignments, and user hierarchy. All APIs require proper authentication and authorization based on the user's role and permissions.

## Authentication & Authorization

### Headers
```
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json
```

### Permission Requirements
All endpoints require specific permissions. Users must have the required permission either through their role or individual overrides.

### Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "meta": {
    "timestamp": "2025-08-02T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "FORBIDDEN",
    "message": "You do not have permission to perform this action",
    "details": {}
  },
  "meta": {
    "timestamp": "2025-08-02T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

## Permission Management APIs

### 1. Permissions Endpoints

#### Get All Permissions
```http
GET /api/admin/permissions
```

**Required Permission**: `system.settings.manage`

**Query Parameters:**
- `category` (optional): Filter by permission category
- `active` (optional): Filter by active status (true/false)
- `search` (optional): Search in name and display_name

**Response:**
```json
{
  "success": true,
  "data": {
    "permissions": [
      {
        "id": 1,
        "name": "estates.view_all",
        "display_name": "View All Estates",
        "description": "Access to view all estates in the system",
        "category": "estate",
        "requires_estate_assignment": false,
        "system_level_only": true,
        "allow_user_overrides": false,
        "is_active": true,
        "created_at": "2025-08-02T10:00:00Z",
        "updated_at": "2025-08-02T10:00:00Z"
      }
    ],
    "meta": {
      "total": 150,
      "per_page": 20,
      "current_page": 1,
      "last_page": 8
    }
  }
}
```

#### Create Permission
```http
POST /api/admin/permissions
```

**Required Permission**: `system.settings.manage`

**Request Body:**
```json
{
  "name": "reports.export_custom",
  "display_name": "Export Custom Reports",
  "description": "Allow users to export custom reports with advanced filtering",
  "category": "reports",
  "requires_estate_assignment": true,
  "system_level_only": false,
  "allow_user_overrides": true,
  "is_active": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "permission": {
      "id": 151,
      "name": "reports.export_custom",
      "display_name": "Export Custom Reports",
      "description": "Allow users to export custom reports with advanced filtering",
      "category": "reports",
      "requires_estate_assignment": true,
      "system_level_only": false,
      "allow_user_overrides": true,
      "is_active": true,
      "created_at": "2025-08-02T10:30:00Z",
      "updated_at": "2025-08-02T10:30:00Z"
    }
  }
}
```

#### Update Permission
```http
PUT /api/admin/permissions/{permission}
```

**Required Permission**: `system.settings.manage`

**Request Body:**
```json
{
  "display_name": "Export Custom Reports (Advanced)",
  "description": "Allow users to export custom reports with advanced filtering and scheduling",
  "is_active": true
}
```

#### Delete Permission
```http
DELETE /api/admin/permissions/{permission}
```

**Required Permission**: `system.settings.manage`

**Response:**
```json
{
  "success": true,
  "message": "Permission deleted successfully"
}
```

### 2. Role Permissions Endpoints

#### Get Role Permissions
```http
GET /api/admin/roles/{role}/permissions
```

**Required Permission**: `system.settings.manage`

**Response:**
```json
{
  "success": true,
  "data": {
    "role": "manager",
    "permissions": [
      {
        "id": 2,
        "name": "estates.view_assigned",
        "display_name": "View Assigned Estates",
        "category": "estate",
        "assigned_at": "2025-08-02T10:00:00Z",
        "assigned_by": {
          "id": 1,
          "name": "System Administrator"
        }
      }
    ],
    "available_permissions": [
      {
        "id": 151,
        "name": "reports.export_custom",
        "display_name": "Export Custom Reports",
        "category": "reports"
      }
    ]
  }
}
```

#### Update Role Permissions
```http
PUT /api/admin/roles/{role}/permissions
```

**Required Permission**: `system.settings.manage`

**Request Body:**
```json
{
  "permission_ids": [2, 3, 4, 151],
  "remove_existing": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "role": "manager",
    "added_permissions": [151],
    "removed_permissions": [],
    "total_permissions": 5,
    "updated_at": "2025-08-02T10:35:00Z"
  }
}
```

### 3. User Permission Overrides Endpoints

#### Get User Overrides
```http
GET /api/admin/users/{user}/permission-overrides
```

**Required Permission**: `users.edit_all` or `users.edit_assigned`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 2,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "manager"
    },
    "overrides": [
      {
        "id": 1,
        "permission": {
          "id": 1,
          "name": "estates.view_all",
          "display_name": "View All Estates"
        },
        "action": "grant",
        "granted_by": {
          "id": 1,
          "name": "System Administrator"
        },
        "expires_at": "2025-12-31T23:59:59Z",
        "reason": "Temporary admin access during system migration",
        "created_at": "2025-08-02T10:00:00Z"
      }
    ]
  }
}
```

#### Create User Override
```http
POST /api/admin/users/{user}/permission-overrides
```

**Required Permission**: `users.edit_all` or `users.edit_assigned`

**Request Body:**
```json
{
  "permission_id": 1,
  "action": "grant",
  "expires_at": "2025-12-31T23:59:59Z",
  "reason": "Temporary admin access during system migration"
}
```

#### Delete User Override
```http
DELETE /api/admin/users/{user}/permission-overrides/{override}
```

**Required Permission**: `users.edit_all` or `users.edit_assigned`

## Estate Assignment APIs

### 1. Estate Assignment Endpoints

#### Get User Estate Assignments
```http
GET /api/admin/users/{user}/estate-assignments
```

**Required Permission**: `users.view_all` or `users.view_assigned`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 2,
      "name": "John Doe",
      "role": "manager"
    },
    "assignments": [
      {
        "id": 1,
        "estate": {
          "id": 1,
          "name": "Green Valley Estate",
          "address": "123 Green Valley Rd"
        },
        "assigned_by": {
          "id": 1,
          "name": "System Administrator"
        },
        "assigned_at": "2025-08-02T10:00:00Z"
      }
    ],
    "available_estates": [
      {
        "id": 3,
        "name": "Blue Mountain Estate",
        "address": "456 Blue Mountain Rd"
      }
    ]
  }
}
```

#### Update User Estate Assignments
```http
PUT /api/admin/users/{user}/estate-assignments
```

**Required Permission**: `users.assign_estates`

**Request Body:**
```json
{
  "estate_ids": [1, 2, 3],
  "remove_existing": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 2,
      "name": "John Doe"
    },
    "added_estates": [3],
    "removed_estates": [],
    "total_estates": 3,
    "updated_at": "2025-08-02T10:40:00Z"
  }
}
```

#### Bulk Estate Assignment
```http
POST /api/admin/estate-assignments/bulk
```

**Required Permission**: `users.assign_estates`

**Request Body:**
```json
{
  "user_ids": [2, 3, 4],
  "estate_ids": [1, 2],
  "remove_existing": false
}
```

### 2. Estate Users Endpoints

#### Get Estate Users
```http
GET /api/admin/estates/{estate}/users
```

**Required Permission**: `estates.view_all` or `estates.view_assigned`

**Query Parameters:**
- `role` (optional): Filter by user role
- `search` (optional): Search in user name and email

**Response:**
```json
{
  "success": true,
  "data": {
    "estate": {
      "id": 1,
      "name": "Green Valley Estate"
    },
    "users": [
      {
        "id": 2,
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "manager",
        "assigned_at": "2025-08-02T10:00:00Z",
        "assigned_by": {
          "id": 1,
          "name": "System Administrator"
        }
      }
    ]
  }
}
```

## Management Hierarchy APIs

### 1. Hierarchy Endpoints

#### Get User Subordinates
```http
GET /api/admin/users/{user}/subordinates
```

**Required Permission**: `users.view_all` or `users.view_assigned`

**Response:**
```json
{
  "success": true,
  "data": {
    "manager": {
      "id": 2,
      "name": "John Doe",
      "role": "manager"
    },
    "subordinates": [
      {
        "id": 3,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "role": "reviewer",
        "relationship": "manages",
        "created_at": "2025-08-02T10:00:00Z"
      },
      {
        "id": 4,
        "name": "Bob Johnson",
        "email": "<EMAIL>",
        "role": "caretaker",
        "relationship": "manages",
        "created_at": "2025-08-02T10:00:00Z"
      }
    ]
  }
}
```

#### Update User Subordinates
```http
PUT /api/admin/users/{user}/subordinates
```

**Required Permission**: `users.assign_roles`

**Request Body:**
```json
{
  "subordinate_ids": [3, 4, 5],
  "relationship": "manages",
  "remove_existing": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "manager": {
      "id": 2,
      "name": "John Doe"
    },
    "added_subordinates": [5],
    "removed_subordinates": [],
    "total_subordinates": 3,
    "relationship": "manages",
    "updated_at": "2025-08-02T10:45:00Z"
  }
}
```

#### Get User Managers
```http
GET /api/admin/users/{user}/managers
```

**Required Permission**: `users.view_all` or `users.view_assigned`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 3,
      "name": "Jane Smith",
      "role": "reviewer"
    },
    "managers": [
      {
        "id": 2,
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "manager",
        "relationship": "manages",
        "created_at": "2025-08-02T10:00:00Z"
      }
    ]
  }
}
```

## Audit Log APIs

### 1. Audit Log Endpoints

#### Get Permission Audit Logs
```http
GET /api/admin/permission-audit-logs
```

**Required Permission**: `audit.logs.view`

**Query Parameters:**
- `user_id` (optional): Filter by user
- `action` (optional): Filter by action type
- `target_type` (optional): Filter by target type
- `target_id` (optional): Filter by target ID
- `start_date` (optional): Filter by start date
- `end_date` (optional): Filter by end date
- `page` (optional): Page number
- `per_page` (optional): Items per page (max 100)

**Response:**
```json
{
  "success": true,
  "data": {
    "audit_logs": [
      {
        "id": 1,
        "user": {
          "id": 1,
          "name": "System Administrator"
        },
        "action": "role_permissions_updated",
        "details": {
          "role": "manager",
          "added_permissions": ["estates.analytics"],
          "removed_permissions": [],
          "total_permissions": 15
        },
        "target_type": "role",
        "target_id": "manager",
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "created_at": "2025-08-02T10:30:00Z"
      }
    ],
    "meta": {
      "total": 1250,
      "per_page": 20,
      "current_page": 1,
      "last_page": 63
    }
  }
}
```

#### Export Audit Logs
```http
POST /api/admin/permission-audit-logs/export
```

**Required Permission**: `audit.logs.export`

**Request Body:**
```json
{
  "filters": {
    "start_date": "2025-08-01T00:00:00Z",
    "end_date": "2025-08-02T23:59:59Z",
    "action": ["role_permissions_updated", "user_override_created"]
  },
  "format": "csv",
  "include_headers": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "export_url": "https://example.com/storage/exports/audit-logs-2025-08-02.csv",
    "expires_at": "2025-08-03T10:30:00Z",
    "record_count": 45
  }
}
```

## Permission Validation APIs

### 1. Validation Endpoints

#### Validate User Permission
```http
POST /api/admin/permissions/validate
```

**Required Permission**: `system.settings.manage`

**Request Body:**
```json
{
  "user_id": 2,
  "permission": "estates.view_all",
  "estate_id": 1
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 2,
      "name": "John Doe",
      "role": "manager"
    },
    "permission": "estates.view_all",
    "estate": {
      "id": 1,
      "name": "Green Valley Estate"
    },
    "has_permission": true,
    "granted_by": "user_override",
    "details": {
      "role_has_permission": false,
      "override_exists": true,
      "override_action": "grant",
      "estate_access_valid": true
    }
  }
}
```

#### Get User Effective Permissions
```http
GET /api/admin/users/{user}/effective-permissions
```

**Required Permission**: `users.view_all` or `users.view_assigned`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 2,
      "name": "John Doe",
      "role": "manager"
    },
    "permissions": [
      {
        "name": "estates.view_all",
        "display_name": "View All Estates",
        "category": "estate",
        "source": "override",
        "granted_at": "2025-08-02T10:00:00Z",
        "expires_at": "2025-12-31T23:59:59Z"
      },
      {
        "name": "estates.view_assigned",
        "display_name": "View Assigned Estates",
        "category": "estate",
        "source": "role",
        "granted_at": "2025-08-02T10:00:00Z"
      }
    ],
    "summary": {
      "total_permissions": 25,
      "role_permissions": 24,
      "override_permissions": 1,
      "expired_overrides": 0
    }
  }
}
```

## Permission Template APIs

### 1. Template Endpoints

#### Get Permission Templates
```http
GET /api/admin/permission-templates
```

**Required Permission**: `system.settings.manage`

**Response:**
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": 1,
        "name": "Senior Manager",
        "description": "Enhanced permissions for senior estate managers",
        "permissions": [
          "estates.view_assigned",
          "estates.manage_assigned",
          "estates.analytics",
          "reports.view_assigned",
          "export.data_assigned"
        ],
        "created_at": "2025-08-02T10:00:00Z",
        "updated_at": "2025-08-02T10:00:00Z"
      }
    ]
  }
}
```

#### Create Permission Template
```http
POST /api/admin/permission-templates
```

**Required Permission**: `system.settings.manage`

**Request Body:**
```json
{
  "name": "Junior Manager",
  "description": "Limited permissions for junior estate managers",
  "permissions": [
    "estates.view_assigned",
    "reports.view_assigned"
  ]
}
```

#### Apply Template to Role
```http
POST /api/admin/permission-templates/{template}/apply
```

**Required Permission**: `system.settings.manage`

**Request Body:**
```json
{
  "role": "manager",
  "replace_existing": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "template": {
      "id": 2,
      "name": "Junior Manager"
    },
    "role": "manager",
    "applied_permissions": 2,
    "skipped_permissions": 0,
    "total_permissions_after": 26,
    "applied_at": "2025-08-02T10:50:00Z"
  }
}
```

## Error Codes

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `FORBIDDEN` | 403 | User lacks required permission |
| `UNAUTHORIZED` | 401 | Authentication required or invalid |
| `NOT_FOUND` | 404 | Resource not found |
| `VALIDATION_ERROR` | 422 | Request validation failed |
| `CONFLICT` | 409 | Resource conflict (e.g., duplicate assignment) |
| `RATE_LIMITED` | 429 | Too many requests |
| `INTERNAL_ERROR` | 500 | Internal server error |

## Rate Limiting

- **Admin APIs**: 100 requests per minute per user
- **Permission Validation**: 1000 requests per minute per user
- **Audit Log Export**: 5 requests per hour per user
- **Bulk Operations**: 10 requests per hour per user

## Webhooks

### Permission Change Events

The system can send webhook notifications when permission changes occur:

```json
{
  "event": "permission.changed",
  "timestamp": "2025-08-02T10:30:00Z",
  "data": {
    "user_id": 2,
    "change_type": "override_created",
    "permission": "estates.view_all",
    "action": "grant",
    "changed_by": 1
  }
}
```

## SDK Integration

### JavaScript SDK Example

```javascript
import { RBACClient } from '@water-management/rbac-sdk';

const client = new RBACClient({
  baseURL: 'https://api.water-management.com',
  token: 'your-api-token'
});

// Get user permissions
const permissions = await client.getUserEffectivePermissions(2);

// Validate permission
const hasAccess = await client.validatePermission({
  userId: 2,
  permission: 'estates.view_all',
  estateId: 1
});

// Create user override
await client.createUserOverride(2, {
  permissionId: 1,
  action: 'grant',
  expiresAt: '2025-12-31T23:59:59Z',
  reason: 'Temporary admin access'
});
```

This API specification provides comprehensive endpoints for managing all aspects of the RBAC system, with proper authentication, authorization, error handling, and rate limiting.