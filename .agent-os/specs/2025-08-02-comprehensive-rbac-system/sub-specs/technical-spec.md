# Technical Specification

> Spec: Comprehensive Role-Based Access Control (RBAC) System
> Created: 2025-08-02
> Status: Planning

## Architecture Overview

The comprehensive RBAC system will be built using <PERSON><PERSON>'s built-in authorization features combined with custom services for estate-based access control and hierarchical management. The system will integrate seamlessly with the existing Livewire-based UI while providing granular permission control.

### Core Components

#### 1. Permission Service Layer
```php
// Services/PermissionValidationService.php
class PermissionValidationService
{
    public function validateUserPermission(User $user, string $permission, ?Estate $estate = null): bool
    {
        // 1. Check user overrides first (highest priority)
        if ($override = $this->getUserOverride($user, $permission)) {
            return $this->evaluateOverride($override);
        }
        
        // 2. Check role-based permissions
        if (!$user->hasPermission($permission)) {
            return false;
        }
        
        // 3. Validate estate assignment if required
        if ($this->permissionRequiresEstate($permission)) {
            return $this->validateEstateAccess($user, $estate);
        }
        
        return true;
    }
    
    private function getUserOverride(User $user, string $permission): ?UserPermissionOverride
    {
        return $user->permissionOverrides()
            ->where('permission.name', $permission)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->first();
    }
    
    private function validateEstateAccess(User $user, ?Estate $estate): bool
    {
        if (!$estate) {
            return false;
        }
        
        // Admin users have access to all estates
        if ($user->hasRole(UserRole::ADMIN)) {
            return true;
        }
        
        return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
    }
}
```

#### 2. Estate Assignment Service
```php
// Services/EstateAssignmentService.php
class EstateAssignmentService
{
    public function assignUserToEstates(User $user, array $estateIds, User $assignedBy): void
    {
        DB::transaction(function () use ($user, $estateIds, $assignedBy) {
            // Remove existing assignments
            $user->assignedEstates()->detach();
            
            // Add new assignments
            $assignments = collect($estateIds)->map(function ($estateId) use ($assignedBy) {
                return [
                    'estate_id' => $estateId,
                    'assigned_by' => $assignedBy->id,
                    'assigned_at' => now(),
                ];
            });
            
            $user->assignedEstates()->attach($assignments);
            
            // Log the assignment changes
            $this->logAssignmentChanges($user, $estateIds, $assignedBy);
        });
    }
    
    public function getAccessibleEstates(User $user): Collection
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return Estate::all();
        }
        
        return $user->assignedEstates()->with('houses')->get();
    }
    
    public function validateEstateAccess(User $user, Estate $estate): bool
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return true;
        }
        
        return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
    }
}
```

#### 3. Management Hierarchy Service
```php
// Services/ManagementHierarchyService.php
class ManagementHierarchyService
{
    public function assignSubordinates(User $manager, array $subordinateIds, string $relationship): void
    {
        DB::transaction(function () use ($manager, $subordinateIds, $relationship) {
            // Remove existing relationships
            $manager->subordinates()->detach();
            
            // Add new relationships
            $relationships = collect($subordinateIds)->map(function ($subordinateId) use ($relationship) {
                return [
                    'subordinate_id' => $subordinateId,
                    'relationship' => $relationship,
                ];
            });
            
            $manager->subordinates()->attach($relationships);
            
            // Ensure estate assignments align with hierarchy
            $this->syncEstateAssignments($manager, $subordinateIds);
        });
    }
    
    public function getManagedUsers(User $manager): Collection
    {
        return $manager->subordinates()->with('assignedEstates')->get();
    }
    
    public function validateManagementRelationship(User $manager, User $subordinate): bool
    {
        return $manager->subordinates()->where('subordinate_id', $subordinate->id)->exists();
    }
}
```

### Database Schema Implementation

#### 1. Permissions Table
```php
// database/migrations/xxxx_xx_xx_xxxxxx_create_permissions_table.php
Schema::create('permissions', function (Blueprint $table) {
    $table->id();
    $table->string('name')->unique();
    $table->string('display_name');
    $table->text('description')->nullable();
    $table->string('category'); // dashboard, estate, house, contact, reading, billing, reports, users, system, resident
    $table->boolean('requires_estate_assignment')->default(true);
    $table->boolean('system_level_only')->default(false);
    $table->boolean('allow_user_overrides')->default(true);
    $table->boolean('is_active')->default(true);
    $table->timestamps();
    
    $table->index(['category', 'is_active']);
    $table->index('name');
});
```

#### 2. Role Permissions Table
```php
// database/migrations/xxxx_xx_xx_xxxxxx_create_role_permissions_table.php
Schema::create('role_permissions', function (Blueprint $table) {
    $table->id();
    $table->string('role'); // Using UserRole enum values
    $table->foreignId('permission_id')->constrained()->onDelete('cascade');
    $table->foreignId('assigned_by')->constrained('users');
    $table->timestamp('assigned_at')->default(DB::raw('CURRENT_TIMESTAMP'));
    $table->timestamps();
    
    $table->unique(['role', 'permission_id']);
    $table->foreign('role')->references('value')->on('user_roles');
});
```

#### 3. User Permission Overrides Table
```php
// database/migrations/xxxx_xx_xx_xxxxxx_create_user_permission_overrides_table.php
Schema::create('user_permission_overrides', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->foreignId('permission_id')->constrained()->onDelete('cascade');
    $table->enum('action', ['grant', 'revoke'])->default('grant');
    $table->foreignId('granted_by')->constrained('users');
    $table->timestamp('expires_at')->nullable();
    $table->text('reason')->nullable();
    $table->timestamps();
    
    $table->unique(['user_id', 'permission_id']);
    $table->index(['user_id', 'expires_at']);
    $table->index('permission_id');
});
```

#### 4. Permission Audit Logs Table
```php
// database/migrations/xxxx_xx_xx_xxxxxx_create_permission_audit_logs_table.php
Schema::create('permission_audit_logs', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->string('action'); // role_updated, permission_added, override_created, etc.
    $table->json('details'); // Store comprehensive change details
    $table->string('target_type'); // role, permission, user_override, estate_assignment
    $table->string('target_id');
    $table->ipAddress('ip_address')->nullable();
    $table->text('user_agent')->nullable();
    $table->timestamps();
    
    $table->index(['user_id', 'created_at']);
    $table->index(['target_type', 'target_id']);
    $table->index('action');
});
```

#### 5. User Estate Assignments Table
```php
// database/migrations/xxxx_xx_xx_xxxxxx_create_user_estate_assignments_table.php
Schema::create('user_estate_assignments', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->foreignId('estate_id')->constrained()->onDelete('cascade');
    $table->foreignId('assigned_by')->constrained('users');
    $table->timestamp('assigned_at')->default(DB::raw('CURRENT_TIMESTAMP'));
    $table->timestamps();
    
    $table->unique(['user_id', 'estate_id']);
    $table->index(['user_id', 'estate_id']);
    $table->index('estate_id');
});
```

#### 6. User Management Hierarchy Table
```php
// database/migrations/xxxx_xx_xx_xxxxxx_create_user_management_hierarchy_table.php
Schema::create('user_management_hierarchy', function (Blueprint $table) {
    $table->id();
    $table->foreignId('manager_id')->constrained('users')->onDelete('cascade');
    $table->foreignId('subordinate_id')->constrained('users')->onDelete('cascade');
    $table->enum('relationship', ['manages', 'oversees']);
    $table->timestamps();
    
    $table->unique(['manager_id', 'subordinate_id']);
    $table->index(['manager_id', 'relationship']);
    $table->index('subordinate_id');
});
```

### Updated UserRole Enum
```php
// Enums/UserRole.php
enum UserRole: string
{
    case ADMIN = 'admin';
    case MANAGER = 'manager';
    case REVIEWER = 'reviewer';
    case CARETAKER = 'caretaker';
    case RESIDENT = 'resident';

    public function label(): string
    {
        return match($this) {
            self::ADMIN => 'System Administrator',
            self::MANAGER => 'Estate Manager',
            self::REVIEWER => 'Reviewer/Accountant',
            self::CARETAKER => 'Caretaker Staff',
            self::RESIDENT => 'Resident/Tenant',
        };
    }

    public function description(): string
    {
        return match($this) {
            self::ADMIN => 'System-wide administrator with full access to all features and settings',
            self::MANAGER => 'Estate manager with oversight of assigned estates and team management',
            self::REVIEWER => 'Accountant who reviews readings and manages billing for assigned estates',
            self::CARETAKER => 'Field staff who enters meter readings and updates contact information',
            self::RESIDENT => 'Tenant or house owner with access to personal billing and usage information',
        };
    }

    public function permissions(): array
    {
        return match($this) {
            self::ADMIN => $this->getAdminPermissions(),
            self::MANAGER => $this->getManagerPermissions(),
            self::REVIEWER => $this->getReviewerPermissions(),
            self::CARETAKER => $this->getCaretakerPermissions(),
            self::RESIDENT => $this->getResidentPermissions(),
        };
    }

    private function getAdminPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-admin-dashboard', 'view-manager-dashboard', 'view-reviewer-dashboard',
            'view-caretaker-dashboard', 'view-resident-dashboard',
            
            // Estate permissions
            'estates.view_all', 'estates.manage_all', 'estates.create', 'estates.edit_all', 'estates.delete',
            
            // House permissions
            'houses.view_all', 'houses.manage_all', 'houses.create', 'houses.edit_all', 'houses.delete',
            
            // Contact permissions
            'contacts.view_all', 'contacts.manage_all', 'contacts.create', 'contacts.delete',
            
            // Reading permissions
            'readings.view_all', 'readings.create_all', 'readings.edit_all', 'readings.delete',
            'readings.review_all', 'readings.approve_all', 'readings.validate',
            
            // Invoice permissions
            'invoices.view_all', 'invoices.generate_all', 'invoices.create_manual', 'invoices.edit_all',
            'invoices.delete', 'invoices.send_all', 'invoices.adjust_all', 'invoices.export_all',
            
            // Rate permissions
            'rates.view_all', 'rates.manage_all', 'rates.create', 'rates.edit_all', 'rates.delete',
            
            // Report permissions
            'reports.view_all', 'reports.generate_all', 'analytics.view_all', 'export.data_all',
            
            // User permissions
            'users.view_all', 'users.create_all', 'users.edit_all', 'users.delete_all',
            'users.assign_estates', 'users.assign_roles',
            
            // System permissions
            'system.settings.view', 'system.settings.manage', 'audit.logs.view', 'audit.logs.export',
            'whatsapp.settings', 'whatsapp.send_all', 'whatsapp.logs.view',
            
            // Resident permissions
            'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
            'resident.messages.view', 'resident.messages.send',
        ];
    }

    private function getManagerPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-manager-dashboard', 'view-reviewer-dashboard', 'view-caretaker-dashboard', 'view-resident-dashboard',
            
            // Estate permissions
            'estates.view_assigned', 'estates.manage_assigned', 'estates.edit_assigned', 'estates.analytics',
            
            // House permissions
            'houses.view_assigned', 'houses.manage_assigned', 'houses.edit_assigned',
            
            // Contact permissions
            'contacts.view_assigned', 'contacts.manage_assigned',
            
            // Reading permissions
            'readings.view_assigned', 'readings.review_assigned', 'readings.validate',
            
            // Invoice permissions
            'invoices.view_assigned', 'invoices.adjust_assigned', 'invoices.export_assigned',
            
            // Rate permissions
            'rates.view_assigned',
            
            // Report permissions
            'reports.view_assigned', 'analytics.view_assigned', 'export.data_assigned',
            
            // User permissions
            'users.view_assigned', 'users.create_assigned', 'users.edit_assigned', 'users.assign_estates',
            
            // WhatsApp permissions
            'whatsapp.send_assigned', 'whatsapp.logs.view',
            
            // Resident permissions
            'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
            'resident.messages.view', 'resident.messages.send',
        ];
    }

    private function getReviewerPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-reviewer-dashboard', 'view-resident-dashboard',
            
            // Estate permissions
            'estates.view_assigned',
            
            // House permissions
            'houses.view_assigned',
            
            // Contact permissions
            'contacts.view_assigned',
            
            // Reading permissions
            'readings.view_assigned', 'readings.approve_assigned', 'readings.validate',
            
            // Invoice permissions
            'invoices.view_assigned', 'invoices.generate_assigned', 'invoices.edit_assigned',
            'invoices.send_assigned', 'invoices.export_assigned',
            
            // Rate permissions
            'rates.view_assigned',
            
            // Report permissions
            'reports.view_assigned', 'export.data_assigned',
            
            // WhatsApp permissions
            'whatsapp.send_assigned', 'whatsapp.logs.view',
            
            // Resident permissions
            'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
            'resident.messages.view', 'resident.messages.send',
        ];
    }

    private function getCaretakerPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-caretaker-dashboard',
            
            // Estate permissions
            'estates.view_assigned',
            
            // House permissions
            'houses.view_assigned',
            
            // Contact permissions
            'contacts.view_assigned', 'contacts.manage_assigned',
            
            // Reading permissions
            'readings.view_assigned', 'readings.create_assigned', 'readings.edit_assigned', 'readings.validate',
            
            // Resident permissions
            'resident.portal.access',
        ];
    }

    private function getResidentPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-resident-dashboard',
            
            // Personal data permissions
            'houses.view_own', 'contacts.view_own', 'readings.view_own', 'invoices.view_own',
            
            // Report permissions
            'reports.view_own', 'analytics.view_own', 'export.data_own',
            
            // Resident permissions
            'resident.portal.access', 'resident.inquiries.create', 'resident.messages.view',
        ];
    }
}
```

### Model Updates

#### 1. User Model Updates
```php
// Models/User.php
class User extends Authenticatable
{
    // ... existing code ...

    public function assignedEstates()
    {
        return $this->belongsToMany(Estate::class, 'user_estate_assignments')
            ->withPivot(['assigned_by', 'assigned_at'])
            ->withTimestamps();
    }

    public function permissionOverrides()
    {
        return $this->belongsToMany(Permission::class, 'user_permission_overrides')
            ->withPivot(['action', 'granted_by', 'expires_at', 'reason'])
            ->withTimestamps();
    }

    public function subordinates()
    {
        return $this->belongsToMany(User::class, 'user_management_hierarchy', 'manager_id', 'subordinate_id')
            ->withPivot(['relationship'])
            ->withTimestamps();
    }

    public function managers()
    {
        return $this->belongsToMany(User::class, 'user_management_hierarchy', 'subordinate_id', 'manager_id')
            ->withPivot(['relationship'])
            ->withTimestamps();
    }

    public function assignedHouses()
    {
        return $this->hasManyThrough(
            House::class,
            Estate::class,
            'id', // Foreign key on estates table
            'estate_id', // Foreign key on houses table
            'id', // Local key on users table
            'id' // Local key on estates table
        )->whereIn('estates.id', function ($query) {
            $query->select('estate_id')
                  ->from('user_estate_assignments')
                  ->where('user_id', $this->id);
        });
    }

    public function hasPermission(string $permission): bool
    {
        // Check user overrides first
        if ($override = $this->permissionOverrides()->where('name', $permission)->first()) {
            if ($override->pivot->expires_at && $override->pivot->expires_at->isPast()) {
                $override->pivot->delete();
            } else {
                return $override->pivot->action === 'grant';
            }
        }

        // Check role-based permissions
        return in_array($permission, $this->role->permissions());
    }

    public function canAccessEstate(Estate $estate): bool
    {
        if ($this->hasRole(UserRole::ADMIN)) {
            return true;
        }

        return $this->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    public function canAccessHouse(House $house): bool
    {
        if ($this->hasRole(UserRole::ADMIN)) {
            return true;
        }

        if ($this->hasRole(UserRole::RESIDENT)) {
            return $this->contacts()->where('house_id', $house->id)->exists();
        }

        return $this->assignedEstates()->where('estates.id', $house->estate_id)->exists();
    }
}
```

#### 2. New Permission Model
```php
// Models/Permission.php
class Permission extends Model
{
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'category',
        'requires_estate_assignment',
        'system_level_only',
        'allow_user_overrides',
        'is_active',
    ];

    protected $casts = [
        'requires_estate_assignment' => 'boolean',
        'system_level_only' => 'boolean',
        'allow_user_overrides' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function roles()
    {
        return $this->belongsToMany(UserRole::class, 'role_permissions', 'permission_id', 'role')
            ->withPivot(['assigned_by', 'assigned_at'])
            ->withTimestamps();
    }

    public function userOverrides()
    {
        return $this->belongsToMany(User::class, 'user_permission_overrides')
            ->withPivot(['action', 'granted_by', 'expires_at', 'reason'])
            ->withTimestamps();
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeSystemLevel($query)
    {
        return $query->where('system_level_only', true);
    }

    public function scopeEstateBased($query)
    {
        return $query->where('requires_estate_assignment', true);
    }
}
```

### Middleware Updates

#### 1. Estate Access Middleware
```php
// Http/Middleware/EstateAccessMiddleware.php
class EstateAccessMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $estateId = $request->route('estate');
        
        if ($estateId) {
            $estate = Estate::findOrFail($estateId);
            $user = $request->user();
            
            if (!$user->canAccessEstate($estate)) {
                abort(403, 'You do not have permission to access this estate.');
            }
        }
        
        return $next($request);
    }
}
```

#### 2. House Access Middleware
```php
// Http/Middleware/HouseAccessMiddleware.php
class HouseAccessMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $houseId = $request->route('house');
        
        if ($houseId) {
            $house = House::findOrFail($houseId);
            $user = $request->user();
            
            if (!$user->canAccessHouse($house)) {
                abort(403, 'You do not have permission to access this house.');
            }
        }
        
        return $next($request);
    }
}
```

### Route Updates

#### 1. Admin Permission Management Routes
```php
// routes/web.php
Route::middleware(['auth', 'verified', 'can:system.settings.manage'])->group(function () {
    Route::get('admin/permissions', \App\Livewire\Admin\PermissionManagement::class)
        ->name('admin.permissions');
    
    Route::get('admin/permissions/roles/{role}', \App\Livewire\Admin\RolePermissionEditor::class)
        ->name('admin.permissions.roles.edit');
    
    Route::get('admin/permissions/audit', \App\Livewire\Admin\PermissionAuditLog::class)
        ->name('admin.permissions.audit');
});

Route::middleware(['auth', 'verified', 'can:users.assign_estates'])->group(function () {
    Route::get('admin/estate-assignments', \App\Livewire\Admin\EstateAssignmentManager::class)
        ->name('admin.estate-assignments');
    
    Route::get('admin/team-management', \App\Livewire\Admin\TeamManagement::class)
        ->name('admin.team-management');
});
```

#### 2. Resident Portal Routes
```php
// routes/web.php
Route::middleware(['auth', 'verified', 'can:resident.portal.access'])->group(function () {
    Route::get('resident/dashboard', \App\Livewire\Resident\ResidentDashboard::class)
        ->name('resident.dashboard');
    
    Route::get('resident/invoices', \App\Livewire\Resident\ResidentInvoiceManager::class)
        ->name('resident.invoices');
    
    Route::get('resident/readings', \App\Livewire\Resident\ResidentReadingHistory::class)
        ->name('resident.readings');
    
    Route::get('resident/inquiries', \App\Livewire\Resident\ResidentInquirySystem::class)
        ->name('resident.inquiries');
});
```

### Performance Optimization

#### 1. Permission Caching
```php
// Services/PermissionCacheService.php
class PermissionCacheService
{
    public function getUserPermissions(User $user): array
    {
        $cacheKey = "user_permissions_{$user->id}";
        
        return Cache::remember($cacheKey, now()->addHours(1), function () use ($user) {
            $permissions = $user->role->permissions();
            
            // Apply user overrides
            foreach ($user->permissionOverrides as $override) {
                if ($override->pivot->expires_at && $override->pivot->expires_at->isPast()) {
                    continue;
                }
                
                if ($override->pivot->action === 'grant') {
                    $permissions[] = $override->name;
                } else {
                    $permissions = array_diff($permissions, [$override->name]);
                }
            }
            
            return array_unique($permissions);
        });
    }
    
    public function invalidateUserCache(User $user): void
    {
        Cache::forget("user_permissions_{$user->id}");
    }
    
    public function invalidateRoleCache(UserRole $role): void
    {
        $users = User::where('role', $role->value)->get();
        foreach ($users as $user) {
            $this->invalidateUserCache($user);
        }
    }
}
```

#### 2. Query Optimization
```php
// Scopes for estate-based queries
trait EstateScopes
{
    public function scopeForUser($query, User $user)
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return $query;
        }
        
        return $query->whereIn('estate_id', $user->assignedEstates()->pluck('id'));
    }
    
    public function scopeForEstate($query, Estate $estate)
    {
        return $query->where('estate_id', $estate->id);
    }
}

// Apply to models
class House extends Model
{
    use EstateScopes;
    
    // ... existing code ...
}
```

This technical specification provides a comprehensive blueprint for implementing the RBAC system with proper separation of concerns, performance optimization, and maintainability.