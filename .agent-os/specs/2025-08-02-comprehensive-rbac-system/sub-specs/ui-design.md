# UI Design Specification

> Spec: Comprehensive Role-Based Access Control (RBAC) System
> Created: 2025-08-02
> Status: Planning

## UI Overview

The RBAC system requires a comprehensive admin interface for managing permissions, roles, estate assignments, and user hierarchy. The UI will be built using Livewire components with Tailwind CSS and Flux UI components for consistency with the existing application.

## Design Principles

1. **Consistency**: Follow existing application design patterns
2. **Clarity**: Make complex permission management intuitive
3. **Performance**: Optimize for large datasets with efficient loading
4. **Accessibility**: Ensure full keyboard navigation and screen reader support
5. **Security**: Prevent accidental permission changes with proper confirmation

## Component Architecture

### 1. PermissionManagement Component

**Route**: `/admin/permissions`

**Purpose**: Main dashboard for all permission management activities

```php
// Livewire/Admin/PermissionManagement.php
class PermissionManagement extends Component
{
    public $activeTab = 'roles';
    public $search = '';
    public $selectedRole = null;
    public $editingPermission = null;
    
    public function render()
    {
        return view('livewire.admin.permission-management', [
            'roles' => UserRole::cases(),
            'totalPermissions' => Permission::count(),
            'activeUsers' => User::count(),
            'customOverrides' => UserPermissionOverride::count(),
        ]);
    }
}
```

**Template Structure**:
```blade
<div>
    <!-- Header -->
    <x-header>
        <x-header.title>Permission Management</x-header.title>
        <x-header.description>Manage roles, permissions, and user access</x-header.description>
    </x-header>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <x-stat-card title="Total Roles" :value="count($roles)" icon="users" />
        <x-stat-card title="Total Permissions" :value="$totalPermissions" icon="key" />
        <x-stat-card title="Active Users" :value="$activeUsers" icon="user-check" />
        <x-stat-card title="Custom Overrides" :value="$customOverrides" icon="shield-alert" />
    </div>

    <!-- Tabs -->
    <x-tabs wire:model="activeTab">
        <x-tab name="roles">Roles</x-tab>
        <x-tab name="permissions">Permissions</x-tab>
        <x-tab name="audit">Audit Log</x-tab>
    </x-tabs>

    <!-- Tab Content -->
    <div class="mt-6">
        @switch($activeTab)
            @case('roles')
                <livewire:admin.role-management :search="$search" />
                @break
            @case('permissions')
                <livewire:admin.permission-list :search="$search" />
                @break
            @case('audit')
                <livewire:admin.permission-audit-log :search="$search" />
                @break
        @endswitch
    </div>
</div>
```

### 2. RoleManagement Component

**Purpose**: Manage user roles and their permissions

```php
// Livewire/Admin/RoleManagement.php
class RoleManagement extends Component
{
    public $search = '';
    public $editingRole = null;
    public $showEditModal = false;
    
    protected $listeners = ['openRoleEditModal'];
    
    public function openRoleEditModal($roleName)
    {
        $this->editingRole = UserRole::from($roleName);
        $this->showEditModal = true;
    }
    
    public function getRolesProperty()
    {
        return collect(UserRole::cases())->map(function ($role) {
            return [
                'value' => $role->value,
                'label' => $role->label(),
                'description' => $role->description(),
                'user_count' => User::where('role', $role->value)->count(),
                'permission_count' => RolePermission::where('role', $role->value)->count(),
            ];
        });
    }
    
    public function render()
    {
        return view('livewire.admin.role-management', [
            'roles' => $this->roles,
        ]);
    }
}
```

**Template Structure**:
```blade
<div>
    <!-- Header with Actions -->
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">Roles Management</h3>
        <div class="flex space-x-2">
            <x-input placeholder="Search roles..." wire:model.live="search" />
        </div>
    </div>

    <!-- Roles Table -->
    <x-table>
        <x-table.head>
            <x-table.row>
                <x.table.header>Role</x.table.header>
                <x.table.header>Users</x.table.header>
                <x.table.header>Permissions</x.table.header>
                <x.table.header>Status</x.table.header>
                <x.table.header>Actions</x.table.header>
            </x.table.row>
        </x-table.head>
        <x-table.body>
            @foreach($roles as $role)
                <x-table.row>
                    <x-table.cell>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                {{ $this->getRoleIcon($role['value']) }}
                            </div>
                            <div>
                                <div class="font-medium">{{ $role['label'] }}</div>
                                <div class="text-sm text-gray-500">{{ $role['description'] }}</div>
                            </div>
                        </div>
                    </x.table.cell>
                    <x-table.cell>
                        <x-badge :value="$role['user_count']" variant="secondary" />
                    </x.table.cell>
                    <x-table.cell>
                        <x-badge :value="$role['permission_count']" variant="primary" />
                    </x.table.cell>
                    <x.table.cell>
                        <x-badge value="Active" variant="success" />
                    </x.table.cell>
                    <x.table.cell>
                        <div class="flex space-x-2">
                            <x-button size="sm" variant="outline" wire:click="openRoleEditModal('{{ $role['value'] }}')">
                                Edit
                            </x-button>
                            <x-button size="sm" variant="outline" icon="bar-chart">
                                Analytics
                            </x-button>
                        </div>
                    </x.table.cell>
                </x-table.row>
            @endforeach
        </x-table.body>
    </x-table>

    <!-- Role Edit Modal -->
    <x-modal wire:model="showEditModal">
        <x-modal.header>
            <x-modal.title>Edit Role: {{ $editingRole?->label() }}</x-modal.title>
        </x-modal.header>
        <x-modal.body>
            @if($editingRole)
                <livewire:admin.role-permission-editor :role="$editingRole" />
            @endif
        </x-modal.body>
    </x-modal>
</div>
```

### 3. RolePermissionEditor Component

**Purpose**: Edit permissions for a specific role

```php
// Livewire/Admin/RolePermissionEditor.php
class RolePermissionEditor extends Component
{
    public UserRole $role;
    public array $selectedPermissions = [];
    public array $userOverrides = [];
    public $showOverrideModal = false;
    
    public function mount(UserRole $role)
    {
        $this->role = $role;
        $this->loadPermissions();
    }
    
    public function loadPermissions()
    {
        $this->selectedPermissions = RolePermission::where('role', $this->role->value)
            ->pluck('permission_id')
            ->toArray();
            
        $this->userOverrides = UserPermissionOverride::with(['user', 'permission'])
            ->whereHas('user', fn($q) => $q->where('role', $this->role->value))
            ->get()
            ->toArray();
    }
    
    public function saveRolePermissions()
    {
        DB::transaction(function () {
            // Clear existing permissions
            RolePermission::where('role', $this->role->value)->delete();
            
            // Add new permissions
            foreach ($this->selectedPermissions as $permissionId) {
                RolePermission::create([
                    'role' => $this->role->value,
                    'permission_id' => $permissionId,
                    'assigned_by' => auth()->id(),
                ]);
            }
            
            // Log the change
            PermissionAuditLog::create([
                'user_id' => auth()->id(),
                'action' => 'role_permissions_updated',
                'details' => [
                    'role' => $this->role->value,
                    'permissions_count' => count($this->selectedPermissions),
                ],
                'target_type' => 'role',
                'target_id' => $this->role->value,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        });
        
        $this->dispatch('permissions-saved');
    }
    
    public function getPermissionsByCategoryProperty()
    {
        return Permission::active()
            ->get()
            ->groupBy('category')
            ->sortKeys();
    }
}
```

**Template Structure**:
```blade
<div>
    <!-- Permission Categories -->
    <div class="space-y-6">
        @foreach($this->permissionsByCategory as $category => $permissions)
            <div class="border rounded-lg">
                <div class="px-4 py-3 bg-gray-50 border-b">
                    <h4 class="font-medium text-gray-900">{{ Str::title($category) }}</h4>
                </div>
                <div class="p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        @foreach($permissions as $permission)
                            <label class="flex items-start space-x-3 p-3 rounded hover:bg-gray-50 cursor-pointer">
                                <input type="checkbox" 
                                       value="{{ $permission->id }}" 
                                       wire:model="selectedPermissions"
                                       class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <div class="flex-1">
                                    <div class="font-medium text-sm">{{ $permission->display_name }}</div>
                                    <div class="text-xs text-gray-500">{{ $permission->description }}</div>
                                    @if($permission->system_level_only)
                                        <x-badge value="System Only" variant="warning" size="xs" class="mt-1" />
                                    @endif
                                </div>
                            </label>
                        @endforeach
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- User Overrides Section -->
    <div class="mt-6 border rounded-lg">
        <div class="px-4 py-3 bg-gray-50 border-b flex justify-between items-center">
            <h4 class="font-medium text-gray-900">User Overrides</h4>
            <x-button size="sm" wire:click="$toggle('showOverrideModal')">
                Add Override
            </x-button>
        </div>
        <div class="p-4">
            @if(count($userOverrides) > 0)
                <div class="space-y-2">
                    @foreach($userOverrides as $override)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <div>
                                <div class="font-medium text-sm">{{ $override['user']['name'] }}</div>
                                <div class="text-xs text-gray-500">
                                    {{ $override['permission']['display_name'] }} 
                                    ({{ $override['action'] }})
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                @if($override['expires_at'])
                                    <span class="text-xs text-gray-500">
                                        Expires: {{ \Carbon\Carbon::parse($override['expires_at'])->diffForHumans() }}
                                    </span>
                                @endif
                                <x-button size="xs" variant="outline" icon="trash">
                                    Remove
                                </x-button>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-center py-4">No user overrides for this role</p>
            @endif
        </div>
    </div>

    <!-- Actions -->
    <div class="mt-6 flex justify-end space-x-3">
        <x-button variant="outline" wire:click="$emit('closeModal')">
            Cancel
        </x-button>
        <x-button wire:click="saveRolePermissions">
            Save Changes
        </x-button>
    </div>
</div>
```

### 4. EstateAssignmentManager Component

**Purpose**: Manage user-estate assignments

```php
// Livewire/Admin/EstateAssignmentManager.php
class EstateAssignmentManager extends Component
{
    public $selectedUser = null;
    public $selectedEstates = [];
    public $search = '';
    public $showAssignmentModal = false;
    
    public function getUsersProperty()
    {
        return User::with('assignedEstates')
            ->whereNotIn('role', ['admin', 'resident'])
            ->when($this->search, fn($q) => $q->where('name', 'like', "%{$this->search}%"))
            ->get();
    }
    
    public function getEstatesProperty()
    {
        return Estate::all();
    }
    
    public function openAssignmentModal($userId)
    {
        $this->selectedUser = User::find($userId);
        $this->selectedEstates = $this->selectedUser->assignedEstates->pluck('id')->toArray();
        $this->showAssignmentModal = true;
    }
    
    public function saveAssignments()
    {
        $this->selectedUser->assignedEstates()->sync($this->selectedEstates);
        
        PermissionAuditLog::create([
            'user_id' => auth()->id(),
            'action' => 'estate_assignment_updated',
            'details' => [
                'user_id' => $this->selectedUser->id,
                'estate_ids' => $this->selectedEstates,
            ],
            'target_type' => 'estate_assignment',
            'target_id' => $this->selectedUser->id,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
        
        $this->showAssignmentModal = false;
        $this->dispatch('assignments-saved');
    }
}
```

**Template Structure**:
```blade
<div>
    <!-- Header -->
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">Estate Assignments</h3>
        <x-input placeholder="Search users..." wire:model.live="search" />
    </div>

    <!-- Users Table -->
    <x-table>
        <x-table.head>
            <x-table.row>
                <x.table.header>User</x.table.header>
                <x.table.header>Role</x.table.header>
                <x.table.header>Assigned Estates</x.table.header>
                <x.table.header>Actions</x.table.header>
            </x.table.row>
        </x-table.head>
        <x-table.body>
            @foreach($users as $user)
                <x-table.row>
                    <x-table.cell>
                        <div class="flex items-center space-x-3">
                            <x-avatar :name="$user->name" />
                            <div>
                                <div class="font-medium">{{ $user->name }}</div>
                                <div class="text-sm text-gray-500">{{ $user->email }}</div>
                            </div>
                        </div>
                    </x.table.cell>
                    <x.table.cell>
                        <x-badge :value="$user->role->label()" variant="secondary" />
                    </x.table.cell>
                    <x.table.cell>
                        <div class="flex flex-wrap gap-1">
                            @foreach($user->assignedEstates as $estate)
                                <x-badge :value="$estate->name" variant="outline" size="sm" />
                            @endforeach
                        </div>
                    </x.table.cell>
                    <x.table.cell>
                        <x-button size="sm" variant="outline" wire:click="openAssignmentModal({{ $user->id }})">
                            Manage Estates
                        </x-button>
                    </x.table.cell>
                </x-table.row>
            @endforeach
        </x.table.body>
    </x-table>

    <!-- Assignment Modal -->
    <x-modal wire:model="showAssignmentModal">
        <x-modal.header>
            <x-modal.title>Manage Estate Assignments</x-modal.title>
            <x-modal.description>{{ $selectedUser?->name }} ({{ $selectedUser?->role->label() }})</x-modal.description>
        </x-modal.header>
        <x-modal.body>
            @if($selectedUser)
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Assigned Estates</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            @foreach($estates as $estate)
                                <label class="flex items-center space-x-3 p-3 border rounded cursor-pointer hover:bg-gray-50">
                                    <input type="checkbox" 
                                           value="{{ $estate->id }}" 
                                           wire:model="selectedEstates"
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <div>
                                        <div class="font-medium text-sm">{{ $estate->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $estate->address }}</div>
                                    </div>
                                </label>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </x-modal.body>
        <x-modal.footer>
            <x-button variant="outline" wire:click="$toggle('showAssignmentModal')">
                Cancel
            </x-button>
            <x-button wire:click="saveAssignments">
                Save Assignments
            </x-button>
        </x-modal.footer>
    </x-modal>
</div>
```

### 5. PermissionAuditLog Component

**Purpose**: View and filter permission audit logs

```php
// Livewire/Admin/PermissionAuditLog.php
class PermissionAuditLog extends Component
{
    public $search = '';
    public $action = '';
    public $targetType = '';
    public $startDate = '';
    public $endDate = '';
    
    public function getAuditLogsProperty()
    {
        return PermissionAuditLog::with('user')
            ->when($this->search, fn($q) => $q->whereHas('user', fn($u) => $u->where('name', 'like', "%{$this->search}%")))
            ->when($this->action, fn($q) => $q->where('action', $this->action))
            ->when($this->targetType, fn($q) => $q->where('target_type', $this->targetType))
            ->when($this->startDate, fn($q) => $q->where('created_at', '>=', $this->startDate))
            ->when($this->endDate, fn($q) => $q->where('created_at', '<=', $this->endDate))
            ->latest()
            ->paginate(20);
    }
    
    public function exportLogs()
    {
        // Export logic here
        $this->dispatch('export-started');
    }
}
```

**Template Structure**:
```blade
<div>
    <!-- Filters -->
    <div class="bg-gray-50 p-4 rounded-lg mb-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <x-input placeholder="Search user..." wire:model.live="search" />
            <x-select placeholder="Filter by action" wire:model="action">
                <option value="">All Actions</option>
                <option value="role_permissions_updated">Role Permissions Updated</option>
                <option value="user_override_created">User Override Created</option>
                <option value="estate_assignment_updated">Estate Assignment Updated</option>
            </x-select>
            <x-input type="date" placeholder="Start date" wire:model="startDate" />
            <x-input type="date" placeholder="End date" wire:model="endDate" />
        </div>
        <div class="mt-4 flex justify-end">
            <x-button variant="outline" wire:click="exportLogs">
                Export Logs
            </x-button>
        </div>
    </div>

    <!-- Audit Logs Table -->
    <x-table>
        <x-table.head>
            <x.table.row>
                <x.table.header>Timestamp</x.table.header>
                <x.table.header>User</x.table.header>
                <x.table.header>Action</x.table.header>
                <x.table.header>Target</x.table.header>
                <x.table.header>Details</x.table.header>
                <x.table.header>IP Address</x.table.header>
            </x.table.row>
        </x.table.head>
        <x-table.body>
            @foreach($auditLogs as $log)
                <x-table.row>
                    <x.table.cell>
                        <div class="text-sm">{{ $log->created_at->format('M j, Y H:i') }}</div>
                    </x.table.cell>
                    <x.table.cell>
                        <div class="flex items-center space-x-2">
                            <x-avatar :name="$log->user->name" size="xs" />
                            <span class="text-sm font-medium">{{ $log->user->name }}</span>
                        </div>
                    </x.table.cell>
                    <x.table.cell>
                        <x-badge :value="Str::title(str_replace('_', ' ', $log->action))" variant="outline" />
                    </x.table.cell>
                    <x.table.cell>
                        <div class="text-sm">
                            <div class="font-medium">{{ $log->target_type }}</div>
                            <div class="text-gray-500">{{ $log->target_id }}</div>
                        </div>
                    </x.table.cell>
                    <x.table.cell>
                        <div class="text-sm max-w-xs truncate" title="{{ json_encode($log->details) }}">
                            {{ json_encode($log->details) }}
                        </div>
                    </x.table.cell>
                    <x.table.cell>
                        <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ $log->ip_address }}</code>
                    </x.table.cell>
                </x-table.row>
            @endforeach
        </x.table.body>
    </x-table>

    <!-- Pagination -->
    <div class="mt-4">
        {{ $auditLogs->links() }}
    </div>
</div>
```

## Resident Portal Components

### 1. ResidentDashboard Component

**Purpose**: Main dashboard for residents

```blade
<div>
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-lg mb-6">
        <h1 class="text-2xl font-bold mb-2">Welcome back, {{ auth()->user()->name }}!</h1>
        <p class="text-blue-100">Here's your water management overview</p>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <x-stat-card title="Current Bill" :value="'$' . number_format($currentBill, 2)" icon="file-text" variant="warning" />
        <x-stat-card title="This Month's Usage" :value="$currentUsage . ' m³'" icon="droplets" variant="info" />
        <x-stat-card title="Last Payment" :value="$lastPayment ? '$' . number_format($lastPayment, 2) : 'None'" icon="credit-card" variant="success" />
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Invoices -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-4 border-b">
                <h3 class="font-medium">Recent Invoices</h3>
            </div>
            <div class="p-4">
                @foreach($recentInvoices as $invoice)
                    <div class="flex justify-between items-center py-2 border-b last:border-b-0">
                        <div>
                            <div class="font-medium">{{ $invoice->invoice_number }}</div>
                            <div class="text-sm text-gray-500">{{ $invoice->created_at->format('M j, Y') }}</div>
                        </div>
                        <div class="text-right">
                            <div class="font-medium">${{ number_format($invoice->amount, 2) }}</div>
                            <x-badge :value="$invoice->status" :variant="$invoice->status === 'paid' ? 'success' : 'warning'" size="sm" />
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Recent Messages -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-4 border-b">
                <h3 class="font-medium">Recent Messages</h3>
            </div>
            <div class="p-4">
                @foreach($recentMessages as $message)
                    <div class="py-2 border-b last:border-b-0">
                        <div class="font-medium text-sm">{{ $message->subject }}</div>
                        <div class="text-sm text-gray-500">{{ $message->created_at->format('M j, Y') }}</div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
```

## Design System Integration

### Color Scheme
- **Primary**: Blue-600 (for main actions and highlights)
- **Success**: Green-600 (for positive indicators)
- **Warning**: Yellow-600 (for attention needed)
- **Danger**: Red-600 (for destructive actions)
- **Info**: Gray-600 (for neutral information)

### Icon Usage
- **Roles**: User icons with role-specific variations
- **Permissions**: Key/shield icons for security
- **Estates**: Building/home icons
- **Audit**: Clock/history icons
- **Actions**: Consistent icon set for CRUD operations

### Responsive Design
- **Mobile**: Single column layout, stacked cards
- **Tablet**: Two-column layout where appropriate
- **Desktop**: Full multi-column layout with sidebars

### Accessibility
- **Keyboard Navigation**: Full tab navigation support
- **Screen Reader**: Proper ARIA labels and descriptions
- **Color Contrast**: WCAG AA compliant contrast ratios
- **Focus States**: Visible focus indicators on all interactive elements

This UI design specification provides a comprehensive guide for building the RBAC admin interface and resident portal components with consistency, usability, and accessibility in mind.