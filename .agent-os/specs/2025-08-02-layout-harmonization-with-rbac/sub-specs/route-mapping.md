# Route Mapping

> Spec: Layout Harmonization with Comprehensive RBAC System
> Created: 2025-08-02
> Status: Planning

## Overview

This document provides a comprehensive mapping of all routes in the application, organized by user role and permission level. The mapping ensures that each user role has access only to the routes they need, with proper middleware and permission checks.

## Current Route Analysis

### Existing Route Structure
The current `routes/web.php` file has the following structure:
- Main dashboard with role-based redirection
- Mixed route organization without clear role separation
- Inconsistent middleware application
- Some routes without proper permission checks

### Issues Identified
1. **Mixed Route Organization**: Routes for different roles are mixed together
2. **Inconsistent Middleware**: Some routes lack proper permission middleware
3. **Missing Role Prefixes**: No clear URL structure for different roles
4. **Permission Gaps**: Some routes don't have appropriate permission checks

## Proposed Route Structure

### Route Organization Principles
1. **Role-Based Prefixing**: Each role gets its own URL prefix
2. **Consistent Middleware**: All routes have appropriate permission middleware
3. **Clear Naming**: Route names follow consistent patterns
4. **Estate Scoping**: Where appropriate, routes are scoped to user's assigned estates

### New Route Structure

```php
// routes/web.php

// Public routes
Route::get('/', \App\Livewire\HomePage::class)->name('home');
Route::get('/contact', \App\Livewire\ContactPage::class)->name('contact');

// Authenticated routes with role-based organization
Route::middleware(['auth', 'verified'])->group(function () {
    
    // Main dashboard - handles role-based redirection
    Route::get('dashboard', function () {
        $user = Auth::user();
        
        return match($user->role) {
            \App\Enums\UserRole::ADMIN => redirect()->route('admin.dashboard'),
            \App\Enums\UserRole::MANAGER => redirect()->route('management.dashboard'),
            \App\Enums\UserRole::REVIEWER => redirect()->route('reviewer.dashboard'),
            \App\Enums\UserRole::CARETAKER => redirect()->route('caretaker.dashboard'),
            \App\Enums\UserRole::RESIDENT => redirect()->route('resident.dashboard'),
            default => redirect()->route('settings.profile'),
        };
    })->name('dashboard');

    // Admin Routes - /admin/*
    Route::prefix('admin')->middleware('can:users.manage_all')->group(function () {
        // Dashboard
        Route::get('dashboard', \App\Livewire\Admin\AdminDashboard::class)->name('admin.dashboard');
        
        // User Management
        Route::get('users', \App\Livewire\Admin\UserManager::class)->name('admin.users');
        Route::get('users/create', \App\Livewire\Admin\UserForm::class)->name('admin.users.create');
        Route::get('users/{user}/edit', \App\Livewire\Admin\UserForm::class)->name('admin.users.edit');
        
        // Permission Management
        Route::get('permissions', \App\Livewire\Admin\PermissionManager::class)->name('admin.permissions');
        Route::get('permissions/create', \App\Livewire\Admin\PermissionForm::class)->name('admin.permissions.create');
        Route::get('permissions/{permission}/edit', \App\Livewire\Admin\PermissionForm::class)->name('admin.permissions.edit');
        
        // System Settings
        Route::get('settings', \App\Livewire\Admin\SystemSettings::class)->name('admin.settings');
        Route::get('settings/general', \App\Livewire\Admin\GeneralSettings::class)->name('admin.settings.general');
        Route::get('settings/water-rates', \App\Livewire\Admin\WaterRateSettings::class)->name('admin.settings.water-rates');
        Route::get('settings/integrations', \App\Livewire\Admin\IntegrationSettings::class)->name('admin.settings.integrations');
        
        // Estate Assignments
        Route::get('estate-assignments', \App\Livewire\Admin\EstateAssignmentManager::class)->name('admin.estate-assignments');
        Route::get('estate-assignments/create', \App\Livewire\Admin\EstateAssignmentForm::class)->name('admin.estate-assignments.create');
        
        // Team Management
        Route::get('team-management', \App\Livewire\Admin\TeamManagement::class)->name('admin.team-management');
        
        // Audit Logs
        Route::get('audit', \App\Livewire\Admin\AuditLogs::class)->name('admin.audit');
        Route::get('audit/export', \App\Livewire\Admin\AuditExport::class)->name('admin.audit.export');
        
        // System Health
        Route::get('system-health', \App\Livewire\Admin\SystemHealth::class)->name('admin.system-health');
    });

    // Manager Routes - /management/*
    Route::prefix('management')->middleware('can:estates.manage_assigned')->group(function () {
        // Dashboard
        Route::get('dashboard', \App\Livewire\ManagementDashboard::class)->name('management.dashboard');
        
        // Estate Management
        Route::get('estates', \App\Livewire\EstateManager::class)->name('management.estates');
        Route::get('estates/create', \App\Livewire\EstateForm::class)->name('management.estates.create');
        Route::get('estates/{estate}/edit', \App\Livewire\EstateForm::class)->name('management.estates.edit');
        Route::get('estates/{estate}', \App\Livewire\EstateShow::class)->name('management.estates.show');
        Route::get('estates/{estate}/analytics', \App\Livewire\EstateAnalytics::class)->name('management.estates.analytics');
        
        // House Management
        Route::get('houses', \App\Livewire\HouseRegistry::class)->name('management.houses');
        Route::get('houses/create', \App\Livewire\HouseForm::class)->name('management.houses.create');
        Route::get('houses/{house}/edit', \App\Livewire\HouseForm::class)->name('management.houses.edit');
        
        // Team Management
        Route::get('team', \App\Livewire\Admin\TeamManagement::class)->name('management.team');
        Route::get('team/assign', \App\Livewire\Management\TeamAssignment::class)->name('management.team.assign');
        
        // Reports
        Route::get('reports', \App\Livewire\ManagementReports::class)->name('management.reports');
        Route::get('reports/estates', \App\Livewire\Management\EstateReports::class)->name('management.reports.estates');
        Route::get('reports/team', \App\Livewire\Management\TeamReports::class)->name('management.reports.team');
        Route::get('reports/financial', \App\Livewire\Management\FinancialReports::class)->name('management.reports.financial');
        
        // Analytics
        Route::get('analytics', \App\Livewire\ManagementAnalytics::class)->name('management.analytics');
    });

    // Reviewer Routes - /reviewer/*
    Route::prefix('reviewer')->middleware('can:invoices.generate_assigned')->group(function () {
        // Dashboard
        Route::get('dashboard', \App\Livewire\ReviewerDashboard::class)->name('reviewer.dashboard');
        
        // Billing Management
        Route::get('billing', \App\Livewire\Invoice\InvoiceList::class)->name('reviewer.billing');
        Route::get('billing/create', \App\Livewire\Invoice\InvoiceCreate::class)->name('reviewer.billing.create');
        Route::get('billing/{invoice}', \App\Livewire\Invoice\InvoiceDetail::class)->name('reviewer.billing.show');
        Route::get('billing/{invoice}/edit', \App\Livewire\Invoice\InvoiceEdit::class)->name('reviewer.billing.edit');
        Route::get('billing/{invoice}/pdf', \App\Livewire\Invoice\InvoicePdf::class)->name('reviewer.billing.pdf');
        
        // Reading Management
        Route::get('readings', \App\Livewire\ReadingReview::class)->name('reviewer.readings');
        Route::get('readings/pending', \App\Livewire\ReadingPendingReview::class)->name('reviewer.readings.pending');
        Route::get('readings/approved', \App\Livewire\ReadingApproved::class)->name('reviewer.readings.approved');
        Route::get('readings/{reading}', \App\Livewire\ReadingDetail::class)->name('reviewer.readings.show');
        
        // Reports
        Route::get('reports', \App\Livewire\ReviewerReports::class)->name('reviewer.reports');
        Route::get('reports/billing', \App\Livewire\Reviewer\BillingReports::class)->name('reviewer.reports.billing');
        Route::get('reports/consumption', \App\Livewire\Reviewer\ConsumptionReports::class)->name('reviewer.reports.consumption');
        
        // Export
        Route::get('export', \App\Livewire\Reviewer\ExportManager::class)->name('reviewer.export');
    });

    // Caretaker Routes - /caretaker/*
    Route::prefix('caretaker')->middleware('can:readings.create_assigned')->group(function () {
        // Dashboard
        Route::get('dashboard', \App\Livewire\CaretakerDashboard::class)->name('caretaker.dashboard');
        
        // Reading Management
        Route::get('readings', \App\Livewire\MeterReadingEntry::class)->name('caretaker.readings');
        Route::get('readings/create', \App\Livewire\MeterReadingEntry::class)->name('caretaker.readings.create');
        Route::get('readings/history', \App\Livewire\Caretaker\ReadingHistory::class)->name('caretaker.readings.history');
        Route::get('readings/{reading}/edit', \App\Livewire\MeterReadingEntry::class)->name('caretaker.readings.edit');
        
        // Contact Management
        Route::get('contacts', \App\Livewire\ContactManager::class)->name('caretaker.contacts');
        Route::get('contacts/create', \App\Livewire\ContactForm::class)->name('caretaker.contacts.create');
        Route::get('contacts/{contact}/edit', \App\Livewire\ContactForm::class)->name('caretaker.contacts.edit');
        
        // Estate Management
        Route::get('estates', \App\Livewire\Caretaker\EstateManager::class)->name('caretaker.estates');
        Route::get('estates/{estate}', \App\Livewire\Caretaker\EstateDetail::class)->name('caretaker.estates.show');
        
        // House Management
        Route::get('houses/{estateId?}', \App\Livewire\Caretaker\HouseManager::class)->name('caretaker.houses');
        Route::get('houses/{house}/contacts', \App\Livewire\Caretaker\HouseContacts::class)->name('caretaker.houses.contacts');
    });

    // Resident Routes - /resident/*
    Route::prefix('resident')->middleware('can:resident.portal.access')->group(function () {
        // Dashboard
        Route::get('dashboard', \App\Livewire\ResidentDashboard::class)->name('resident.dashboard');
        
        // Billing
        Route::get('bills', \App\Livewire\ResidentBills::class)->name('resident.bills');
        Route::get('bills/{invoice}', \App\Livewire\Resident\BillDetail::class)->name('resident.bills.show');
        Route::get('bills/{invoice}/pdf', \App\Livewire\Resident\BillPdf::class)->name('resident.bills.pdf');
        
        // Usage
        Route::get('usage', \App\Livewire\ResidentUsage::class)->name('resident.usage');
        Route::get('usage/history', \App\Livewire\Resident\UsageHistory::class)->name('resident.usage.history');
        Route::get('usage/analytics', \App\Livewire\Resident\UsageAnalytics::class)->name('resident.usage.analytics');
        
        // Communication
        Route::get('messages', \App\Livewire\ResidentMessages::class)->name('resident.messages');
        Route::get('messages/create', \App\Livewire\Resident\MessageCreate::class)->name('resident.messages.create');
        Route::get('messages/{message}', \App\Livewire\Resident\MessageDetail::class)->name('resident.messages.show');
        
        // Account
        Route::get('profile', \App\Livewire\Resident\Profile::class)->name('resident.profile');
        Route::get('profile/edit', \App\Livewire\Resident\ProfileEdit::class)->name('resident.profile.edit');
    });

    // Shared Routes - Available to all authenticated users
    Route::prefix('settings')->group(function () {
        Route::redirect('', 'profile');
        Route::get('profile', \App\Livewire\Settings\Profile::class)->name('settings.profile');
        Route::get('password', \App\Livewire\Settings\Password::class)->name('settings.password');
        Route::get('appearance', \App\Livewire\Settings\Appearance::class)->name('settings.appearance');
        Route::get('notifications', \App\Livewire\Settings\Notifications::class)->name('settings.notifications');
    });

    // API Routes for AJAX operations
    Route::prefix('api')->group(function () {
        // Estate API endpoints
        Route::prefix('estates')->group(function () {
            Route::get('/', [\App\Http\Controllers\EstateController::class, 'index']);
            Route::post('/', [\App\Http\Controllers\EstateController::class, 'store']);
            Route::get('/{estate}', [\App\Http\Controllers\EstateController::class, 'show']);
            Route::put('/{estate}', [\App\Http\Controllers\EstateController::class, 'update']);
            Route::delete('/{estate}', [\App\Http\Controllers\EstateController::class, 'destroy']);
            Route::get('/{estate}/houses', [\App\Http\Controllers\EstateController::class, 'getHouses']);
            Route::get('/{estate}/contacts', [\App\Http\Controllers\EstateController::class, 'getContacts']);
            Route::get('/{estate}/stats', [\App\Http\Controllers\EstateController::class, 'getStats']);
        });

        // House API endpoints
        Route::prefix('houses')->group(function () {
            Route::get('/', [\App\Http\Controllers\HouseController::class, 'index']);
            Route::post('/', [\App\Http\Controllers\HouseController::class, 'store']);
            Route::get('/{house}', [\App\Http\Controllers\HouseController::class, 'show']);
            Route::put('/{house}', [\App\Http\Controllers\HouseController::class, 'update']);
            Route::delete('/{house}', [\App\Http\Controllers\HouseController::class, 'destroy']);
            Route::get('/{house}/contacts', [\App\Http\Controllers\HouseController::class, 'getContacts']);
            Route::get('/{house}/meter-readings', [\App\Http\Controllers\HouseController::class, 'getMeterReadings']);
            Route::get('/{house}/invoices', [\App\Http\Controllers\HouseController::class, 'getInvoices']);
        });

        // Contact API endpoints
        Route::prefix('contacts')->group(function () {
            Route::get('/', [\App\Http\Controllers\ContactController::class, 'index']);
            Route::post('/', [\App\Http\Controllers\ContactController::class, 'store']);
            Route::get('/{contact}', [\App\Http\Controllers\ContactController::class, 'show']);
            Route::put('/{contact}', [\App\Http\Controllers\ContactController::class, 'update']);
            Route::delete('/{contact}', [\App\Http\Controllers\ContactController::class, 'destroy']);
        });
    });
});

// WhatsApp webhook routes - must be public
Route::prefix('api/webhooks/whatsapp')->group(function () {
    Route::get('/', [\App\Http\Controllers\Api\WhatsAppWebhookController::class, 'verify']);
    Route::post('/', [\App\Http\Controllers\Api\WhatsAppWebhookController::class, 'handle']);
});

require __DIR__.'/auth.php';
```

## Permission to Route Mapping

### Admin Role Permissions
| Permission | Route Pattern | Middleware |
|------------|---------------|------------|
| `users.manage_all` | `/admin/*` | `can:users.manage_all` |
| `users.view_all` | `/admin/users` | `can:users.view_all` |
| `system.settings.view` | `/admin/settings` | `can:system.settings.view` |
| `system.settings.manage` | `/admin/settings/*` | `can:system.settings.manage` |
| `audit.logs.view` | `/admin/audit` | `can:audit.logs.view` |
| `users.assign_estates` | `/admin/estate-assignments` | `can:users.assign_estates` |

### Manager Role Permissions
| Permission | Route Pattern | Middleware |
|------------|---------------|------------|
| `estates.manage_assigned` | `/management/*` | `can:estates.manage_assigned` |
| `estates.view_assigned` | `/management/estates` | `can:estates.view_assigned` |
| `houses.manage_assigned` | `/management/houses` | `can:houses.manage_assigned` |
| `users.view_assigned` | `/management/team` | `can:users.view_assigned` |
| `reports.view_assigned` | `/management/reports` | `can:reports.view_assigned` |
| `analytics.view_assigned` | `/management/analytics` | `can:analytics.view_assigned` |

### Reviewer Role Permissions
| Permission | Route Pattern | Middleware |
|------------|---------------|------------|
| `invoices.generate_assigned` | `/reviewer/*` | `can:invoices.generate_assigned` |
| `invoices.view_assigned` | `/reviewer/billing` | `can:invoices.view_assigned` |
| `readings.approve_assigned` | `/reviewer/readings` | `can:readings.approve_assigned` |
| `reports.view_assigned` | `/reviewer/reports` | `can:reports.view_assigned` |
| `export.data_assigned` | `/reviewer/export` | `can:export.data_assigned` |

### Caretaker Role Permissions
| Permission | Route Pattern | Middleware |
|------------|---------------|------------|
| `readings.create_assigned` | `/caretaker/*` | `can:readings.create_assigned` |
| `readings.view_assigned` | `/caretaker/readings` | `can:readings.view_assigned` |
| `contacts.manage_assigned` | `/caretaker/contacts` | `can:contacts.manage_assigned` |
| `estates.view_assigned` | `/caretaker/estates` | `can:estates.view_assigned` |
| `houses.view_assigned` | `/caretaker/houses` | `can:houses.view_assigned` |

### Resident Role Permissions
| Permission | Route Pattern | Middleware |
|------------|---------------|------------|
| `resident.portal.access` | `/resident/*` | `can:resident.portal.access` |
| `invoices.view_own` | `/resident/bills` | `can:invoices.view_own` |
| `readings.view_own` | `/resident/usage` | `can:readings.view_own` |
| `resident.messages.view` | `/resident/messages` | `can:resident.messages.view` |

## Route Migration Strategy

### Phase 1: Admin Routes
1. Create `/admin` prefix structure
2. Move all admin-related routes under admin prefix
3. Apply proper middleware
4. Update route names
5. Test with admin users

### Phase 2: Manager Routes
1. Create `/management` prefix structure
2. Move manager-related routes under management prefix
3. Apply proper middleware
4. Update route names
5. Test with manager users

### Phase 3: Reviewer Routes
1. Create `/reviewer` prefix structure
2. Move reviewer-related routes under reviewer prefix
3. Apply proper middleware
4. Update route names
5. Test with reviewer users

### Phase 4: Caretaker Routes
1. Create `/caretaker` prefix structure
2. Move caretaker-related routes under caretaker prefix
3. Apply proper middleware
4. Update route names
5. Test with caretaker users

### Phase 5: Resident Routes
1. Create `/resident` prefix structure
2. Move resident-related routes under resident prefix
3. Apply proper middleware
4. Update route names
5. Test with resident users

### Phase 6: Shared Routes
1. Organize shared routes under `/settings` prefix
2. Ensure proper accessibility for all authenticated users
3. Test with all user roles

## Route Testing Strategy

### Unit Tests
- Test route registration
- Test middleware application
- Test route name generation
- Test route parameter binding

### Integration Tests
- Test role-based route access
- Test permission-based route protection
- Test route redirection logic
- Test estate-scoped routing

### End-to-End Tests
- Test complete user journeys
- Test navigation between routes
- Test mobile responsiveness
- Test accessibility

## Route Performance Considerations

### Route Caching
- Enable route caching in production
- Use route model binding where appropriate
- Implement proper route grouping

### Middleware Optimization
- Use middleware groups for common middleware
- Implement permission caching
- Optimize middleware execution order

### URL Structure
- Use clean, readable URLs
- Implement proper HTTP verbs
- Follow REST conventions where appropriate

## Route Security Considerations

### Authentication
- All protected routes require authentication
- Proper session management
- Secure cookie handling

### Authorization
- Server-side permission checks
- Proper middleware application
- Estate-scoped data access

### Input Validation
- Validate all route parameters
- Sanitize user input
- Prevent mass assignment

## Route Documentation

### Route Listing
- Maintain comprehensive route documentation
- Include permission requirements
- Document route parameters

### API Documentation
- Document all API endpoints
- Include request/response examples
- Document authentication requirements

### Migration Guide
- Document route changes
- Provide migration instructions
- Include breaking changes notice