# Technical Specification

> Spec: Layout Harmonization with Comprehensive RBAC System
> Created: 2025-08-02
> Status: Planning

## Architecture Overview

The layout harmonization will implement a role-based navigation system that dynamically loads appropriate sidebar components based on user roles and permissions. The architecture follows a component-based approach with proper separation of concerns.

### Component Hierarchy

```
Main Layout (app.blade.php)
├── Header (header.blade.php)
│   ├── Logo & Title
│   ├── Breadcrumbs
│   ├── Search Button
│   ├── Notifications
│   ├── Theme Toggle
│   └── User Dropdown (with Logout)
└── Sidebar Container (sidebar.blade.php)
    └── Role-Specific Sidebar Component
        ├── AdminSidebar
        ├── ManagerSidebar
        ├── ReviewerSidebar
        ├── CaretakerSidebar
        └── ResidentSidebar
            └── NavLink Components
```

## Technical Implementation Details

### 1. NavLink Component

#### File Structure
```
resources/views/components/navigation/nav-link.blade.php
```

#### Component Props
```php
@props([
    'href' => '',           // Required: Route URL
    'icon' => '',           // Optional: SVG icon markup
    'title' => '',          // Required: Link text
    'permission' => null,   // Optional: Permission required to view link
    'badge' => null,        // Optional: Badge count or text
    'activeRoutes' => [],   // Optional: Array of route patterns for active state
    'external' => false,    // Optional: Whether link is external
])
```

#### Implementation
```blade
@php
    $isVisible = !$permission || auth()->user()->can($permission);
    $isActive = false;
    
    if (!empty($activeRoutes)) {
        foreach ($activeRoutes as $pattern) {
            if (request()->routeIs($pattern) || str_starts_with(request()->path(), $pattern)) {
                $isActive = true;
                break;
            }
        }
    } elseif (request()->url() === $href) {
        $isActive = true;
    }
@endphp

@if($isVisible)
    <a href="{{ $href }}" 
       @if(!$external) target="_self" @endif
       class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
              {{ $isActive ? 
                 'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
       aria-current="{{ $isActive ? 'page' : 'false' }}">
        
        @if($icon)
            <span class="mr-3 h-5 w-5 flex-shrink-0">
                {!! $icon !!}
            </span>
        @endif
        
        <span class="flex-1">{{ $title }}</span>
        
        @if($badge)
            <span class="ml-auto inline-flex items-center rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-200">
                {{ $badge }}
            </span>
        @endif
    </a>
@endif
```

### 2. Role-Specific Sidebar Components

#### Base Structure Pattern
Each sidebar component follows a consistent structure:

```blade
@props(['title' => null])

<aside x-data="{ open: false }"
       x-show="open"
       @sidebar-toggle.window="open = !open"
       class="fixed inset-y-0 left-0 z-30 w-64 border-r border-gray-200 bg-white pt-4 transition-transform duration-200 ease-in-out dark:border-gray-800 dark:bg-gray-900 lg:static lg:block lg:translate-x-0"
       :class="{ '-translate-x-full': !open }">
    
    <!-- Logo Section -->
    <div class="flex items-center justify-between px-4">
        <a href="{{ route('dashboard') }}" class="flex items-center space-x-3">
            <x-application-logo class="h-8 w-auto" />
            <span class="text-lg font-semibold text-gray-800 dark:text-white/90">
                {{ config('app.name', 'Water Management') }}
            </span>
        </a>
        <button @click="open = false" class="lg:hidden">
            <svg class="h-6 w-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    <!-- Navigation Section -->
    <nav class="mt-6">
        <div class="space-y-1 px-2">
            <!-- Role-specific navigation items -->
        </div>
    </nav>

    <!-- User Menu Section -->
    <div class="mt-auto px-4 py-4">
        <!-- User info and logout -->
    </div>
</aside>
```

#### Admin Sidebar Implementation
```blade
<!-- resources/views/components/navigation/sidebar/admin-sidebar.blade.php -->
<div class="space-y-1 px-2">
    <!-- Dashboard -->
    <x-nav-link href="{{ route('admin.dashboard') }}" 
                icon='<svg>...</svg>' 
                title="Admin Dashboard" 
                permission="users.manage_all"
                :activeRoutes="['admin.dashboard*']" />
    
    <!-- User Management -->
    <div class="px-3 pt-4">
        <h3 class="text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
            {{ __('User Management') }}
        </h3>
    </div>
    
    <x-nav-link href="{{ route('admin.users') }}" 
                icon='<svg>...</svg>' 
                title="Users" 
                permission="users.view_all"
                :activeRoutes="['admin.users*']" />
    
    <x-nav-link href="{{ route('admin.permissions') }}" 
                icon='<svg>...</svg>' 
                title="Permissions" 
                permission="users.manage_all"
                :activeRoutes="['admin.permissions*']" />
    
    <x-nav-link href="{{ route('admin.estate-assignments') }}" 
                icon='<svg>...</svg>' 
                title="Estate Assignments" 
                permission="users.assign_estates"
                :activeRoutes="['admin.estate-assignments*']" />
    
    <!-- System Configuration -->
    <div class="px-3 pt-4">
        <h3 class="text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
            {{ __('System Configuration') }}
        </h3>
    </div>
    
    <x-nav-link href="{{ route('admin.settings') }}" 
                icon='<svg>...</svg>' 
                title="System Settings" 
                permission="system.settings.view"
                :activeRoutes="['admin.settings*']" />
    
    <!-- Additional admin sections... -->
</div>
```

### 3. Dynamic Sidebar Loading

#### Updated Main Layout
```blade
<!-- resources/views/layouts/app/sidebar.blade.php -->
@php
    $user = auth()->user();
    $sidebarComponent = 'navigation.sidebar.resident-sidebar'; // Default fallback
    
    if ($user->hasRole(\App\Enums\UserRole::ADMIN)) {
        $sidebarComponent = 'navigation.sidebar.admin-sidebar';
    } elseif ($user->hasRole(\App\Enums\UserRole::MANAGER)) {
        $sidebarComponent = 'navigation.sidebar.manager-sidebar';
    } elseif ($user->hasRole(\App\Enums\UserRole::REVIEWER)) {
        $sidebarComponent = 'navigation.sidebar.reviewer-sidebar';
    } elseif ($user->hasRole(\App\Enums\UserRole::CARETAKER)) {
        $sidebarComponent = 'navigation.sidebar.caretaker-sidebar';
    }
@endphp

<x-dynamic-component :component="$sidebarComponent" :title="$title" />
```

### 4. Admin Dashboard Component

#### Livewire Component
```php
<?php

namespace App\Livewire\Admin;

use App\Models\User;
use App\Models\Estate;
use App\Models\House;
use App\Models\ActivityLog;
use Livewire\Component;

class AdminDashboard extends Component
{
    public $totalUsers;
    public $totalEstates;
    public $totalHouses;
    public $recentActivity;
    public $systemStats;

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        $this->totalUsers = User::count();
        $this->totalEstates = Estate::count();
        $this->totalHouses = House::count();
        $this->recentActivity = ActivityLog::with('user')
            ->latest()
            ->take(10)
            ->get();
        
        $this->systemStats = [
            'activeUsers' => User::where('last_login_at', '>=', now()->subDays(7))->count(),
            'pendingApprovals' => \App\Models\MeterReading::where('status', 'pending')->count(),
            'overdueInvoices' => \App\Models\Invoice::where('due_date', '<', now())->where('status', '!=', 'paid')->count(),
        ];
    }

    public function render()
    {
        return view('livewire.admin.admin-dashboard');
    }
}
```

#### Dashboard View
```blade
<!-- resources/views/livewire/admin/admin-dashboard.blade.php -->
<div class="space-y-6">
    <!-- Stats Grid -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <x-dashboard.stats-card 
            title="Total Users" 
            value="{{ $totalUsers }}" 
            icon='<svg>...</svg>' 
            trend="up" 
            trendValue="12%" />
        
        <x-dashboard.stats-card 
            title="Total Estates" 
            value="{{ $totalEstates }}" 
            icon='<svg>...</svg>' 
            trend="up" 
            trendValue="5%" />
        
        <x-dashboard.stats-card 
            title="Total Houses" 
            value="{{ $totalHouses }}" 
            icon='<svg>...</svg>' 
            trend="up" 
            trendValue="8%" />
        
        <x-dashboard.stats-card 
            title="Active Users" 
            value="{{ $systemStats['activeUsers'] }}" 
            icon='<svg>...</svg>' 
            trend="up" 
            trendValue="15%" />
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div class="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Actions</h3>
            <div class="mt-4 space-y-3">
                <a href="{{ route('admin.users') }}" class="flex items-center justify-between rounded-lg bg-gray-50 p-3 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Manage Users</span>
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
                
                <a href="{{ route('admin.settings') }}" class="flex items-center justify-between rounded-lg bg-gray-50 p-3 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">System Settings</span>
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
                
                <a href="{{ route('admin.estate-assignments') }}" class="flex items-center justify-between rounded-lg bg-gray-50 p-3 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Estate Assignments</span>
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recent Activity</h3>
            <div class="mt-4 space-y-3">
                @foreach($recentActivity as $activity)
                    <div class="flex items-center space-x-3">
                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            {{ $activity->user->initials() }}
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-700 dark:text-gray-300">{{ $activity->description }}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ $activity->created_at->diffForHumans() }}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
```

### 5. Route Structure Updates

#### Updated Web Routes
```php
// routes/web.php
Route::middleware(['auth', 'verified'])->group(function () {
    // Main dashboard - handles role-based redirection
    Route::get('dashboard', function () {
        $user = Auth::user();
        
        return match($user->role) {
            \App\Enums\UserRole::ADMIN => redirect()->route('admin.dashboard'),
            \App\Enums\UserRole::MANAGER => redirect()->route('management.dashboard'),
            \App\Enums\UserRole::REVIEWER => redirect()->route('reviewer.dashboard'),
            \App\Enums\UserRole::CARETAKER => redirect()->route('caretaker.dashboard'),
            \App\Enums\UserRole::RESIDENT => redirect()->route('resident.dashboard'),
            default => redirect()->route('settings.profile'),
        };
    })->name('dashboard');

    // Admin routes
    Route::prefix('admin')->middleware('can:users.manage_all')->group(function () {
        Route::get('dashboard', \App\Livewire\Admin\AdminDashboard::class)->name('admin.dashboard');
        Route::get('users', \App\Livewire\Admin\UserManager::class)->name('admin.users');
        Route::get('permissions', \App\Livewire\Admin\PermissionManager::class)->name('admin.permissions');
        Route::get('settings', \App\Livewire\Admin\SystemSettings::class)->name('admin.settings');
        Route::get('estate-assignments', \App\Livewire\Admin\EstateAssignmentManager::class)->name('admin.estate-assignments');
        Route::get('team-management', \App\Livewire\Admin\TeamManagement::class)->name('admin.team-management');
        Route::get('audit', \App\Livewire\Admin\AuditLogs::class)->name('admin.audit');
    });

    // Manager routes
    Route::prefix('management')->middleware('can:estates.manage_assigned')->group(function () {
        Route::get('dashboard', \App\Livewire\ManagementDashboard::class)->name('management.dashboard');
        Route::get('estates', \App\Livewire\EstateManager::class)->name('management.estates');
        Route::get('team', \App\Livewire\Admin\TeamManagement::class)->name('management.team');
        Route::get('reports', \App\Livewire\ManagementReports::class)->name('management.reports');
    });

    // Reviewer routes
    Route::prefix('reviewer')->middleware('can:invoices.generate_assigned')->group(function () {
        Route::get('dashboard', \App\Livewire\ReviewerDashboard::class)->name('reviewer.dashboard');
        Route::get('billing', \App\Livewire\Invoice\InvoiceList::class)->name('reviewer.billing');
        Route::get('readings', \App\Livewire\ReadingReview::class)->name('reviewer.readings');
        Route::get('reports', \App\Livewire\ReviewerReports::class)->name('reviewer.reports');
    });

    // Caretaker routes
    Route::prefix('caretaker')->middleware('can:readings.create_assigned')->group(function () {
        Route::get('dashboard', \App\Livewire\CaretakerDashboard::class)->name('caretaker.dashboard');
        Route::get('readings', \App\Livewire\MeterReadingEntry::class)->name('caretaker.readings');
        Route::get('contacts', \App\Livewire\ContactManager::class)->name('caretaker.contacts');
    });

    // Resident routes
    Route::prefix('resident')->middleware('can:resident.portal.access')->group(function () {
        Route::get('dashboard', \App\Livewire\ResidentDashboard::class)->name('resident.dashboard');
        Route::get('bills', \App\Livewire\ResidentBills::class)->name('resident.bills');
        Route::get('usage', \App\Livewire\ResidentUsage::class)->name('resident.usage');
        Route::get('messages', \App\Livewire\ResidentMessages::class)->name('resident.messages');
    });

    // Settings routes (available to all authenticated users)
    Route::redirect('settings', 'settings/profile');
    Route::get('settings/profile', \App\Livewire\Settings\Profile::class)->name('settings.profile');
    Route::get('settings/password', \App\Livewire\Settings\Password::class)->name('settings.password');
    Route::get('settings/appearance', \App\Livewire\Settings\Appearance::class)->name('settings.appearance');
});
```

## Performance Considerations

### 1. Permission Caching
```php
// In AppServiceProvider
public function boot()
{
    // Cache user permissions to improve performance
    Blade::directive('can', function ($expression) {
        return "<?php if (auth()->user()?->can({$expression})): ?>";
    });
}
```

### 2. Component Optimization
- Use `@memo` for expensive computations in components
- Implement proper caching for dashboard statistics
- Lazy load non-critical navigation sections

### 3. Asset Loading
- Load role-specific CSS/JS only when needed
- Implement code splitting for large components
- Use proper caching headers for static assets

## Security Considerations

### 1. Permission Validation
- Server-side permission checking in addition to UI hiding
- Proper authorization middleware on all routes
- Audit logging for permission changes

### 2. XSS Prevention
- Proper escaping of all dynamic content
- Use of Laravel's Blade templating security features
- Validation of all user inputs

### 3. CSRF Protection
- CSRF tokens on all state-changing operations
- Proper form handling for logout functionality
- Secure session management

## Accessibility Considerations

### 1. Keyboard Navigation
- Proper tab order for navigation elements
- Keyboard shortcuts for common actions
- Focus management for dynamic content

### 2. Screen Reader Support
- Proper ARIA labels and roles
- Screen reader announcements for dynamic changes
- Semantic HTML structure

### 3. Visual Accessibility
- Sufficient color contrast
- Clear visual indicators for interactive elements
- Responsive design for all screen sizes

## Testing Strategy

### 1. Unit Tests
- Test individual component functionality
- Permission checking logic
- Active state detection
- Mobile responsiveness

### 2. Integration Tests
- Test role-based navigation loading
- Test permission-based link visibility
- Test route accessibility
- Test user flow across different roles

### 3. End-to-End Tests
- Test complete user journeys
- Test mobile responsiveness
- Test accessibility features
- Test performance characteristics

## Monitoring and Maintenance

### 1. Performance Monitoring
- Track navigation load times
- Monitor permission checking performance
- Track mobile performance metrics

### 2. Error Monitoring
- Track navigation-related errors
- Monitor permission-related issues
- Track user-reported problems

### 3. Usage Analytics
- Track navigation usage patterns
- Monitor feature adoption
- Track user engagement metrics