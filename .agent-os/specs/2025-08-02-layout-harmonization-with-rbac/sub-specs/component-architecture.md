# Component Architecture

> Spec: Layout Harmonization with Comprehensive RBAC System
> Created: 2025-08-02
> Status: Planning

## Overview

This document outlines the detailed component architecture for the layout harmonization project. The architecture follows a modular, role-based approach with proper separation of concerns and reusability.

## Component Structure

### 1. Core Navigation Components

#### 1.1 NavLink Component
**File**: `resources/views/components/navigation/nav-link.blade.php`
**Purpose**: Reusable navigation link component with permission checking and active state management

**Props**:
```php
@props([
    'href' => '',           // Required: Route URL
    'icon' => '',           // Optional: SVG icon markup
    'title' => '',          // Required: Link text
    'permission' => null,   // Optional: Permission required to view link
    'badge' => null,        // Optional: Badge count or text
    'activeRoutes' => [],   // Optional: Array of route patterns for active state
    'external' => false,    // Optional: Whether link is external
    'method' => 'GET',      // Optional: HTTP method for form submissions
])
```

**Features**:
- Permission-based visibility
- Active state detection
- Badge support for notifications
- Icon support with SVG markup
- External link handling
- Form submission support (for POST/PUT/DELETE)
- Accessibility attributes
- Dark mode support

**Usage Examples**:
```blade
<!-- Basic link -->
<x-nav-link href="{{ route('dashboard') }}" 
            icon='<svg>...</svg>' 
            title="Dashboard" />

<!-- Link with permission -->
<x-nav-link href="{{ route('admin.users') }}" 
            icon='<svg>...</svg>' 
            title="Users" 
            permission="users.view_all" />

<!-- Link with badge -->
<x-nav-link href="{{ route('readings.review') }}" 
            icon='<svg>...</svg>' 
            title="Review Readings" 
            permission="readings.review_assigned"
            :badge="$pendingReviews" />

<!-- Link with multiple active routes -->
<x-nav-link href="{{ route('estates') }}" 
            icon='<svg>...</svg>' 
            title="Estates" 
            permission="estates.view_assigned"
            :activeRoutes="['estates*', 'estates.show*']" />
```

#### 1.2 Navigation Group Component
**File**: `resources/views/components/navigation/nav-group.blade.php`
**Purpose**: Groups related navigation items with a section header

**Props**:
```php
@props([
    'title' => '',          // Required: Group title
    'permission' => null,   // Optional: Permission required to view group
    'icon' => '',           // Optional: Group icon
])
```

**Features**:
- Permission-based group visibility
- Consistent styling for section headers
- Icon support for groups
- Accessibility attributes

**Usage Example**:
```blade
<x-nav-group title="User Management" permission="users.view_all">
    <x-nav-link href="{{ route('admin.users') }}" 
                icon='<svg>...</svg>' 
                title="Users" 
                permission="users.view_all" />
    
    <x-nav-link href="{{ route('admin.permissions') }}" 
                icon='<svg>...</svg>' 
                title="Permissions" 
                permission="users.manage_all" />
</x-nav-group>
```

### 2. Role-Specific Sidebar Components

#### 2.1 Base Sidebar Component
**File**: `resources/views/components/navigation/sidebar/base-sidebar.blade.php`
**Purpose**: Base sidebar component with common structure and functionality

**Props**:
```php
@props([
    'title' => null,        // Optional: Page title
    'user' => null,         // Optional: User object (defaults to auth user)
])
```

**Features**:
- Responsive design with mobile toggle
- Logo and branding
- User menu with logout
- Dark mode support
- Consistent layout structure
- Accessibility attributes

#### 2.2 Admin Sidebar Component
**File**: `resources/views/components/navigation/sidebar/admin-sidebar.blade.php`
**Purpose**: Sidebar for system administrators with full system access

**Navigation Groups**:
1. **Dashboard**
   - Admin Dashboard
   - System Overview

2. **User Management**
   - Users
   - Permissions
   - Role Management
   - Estate Assignments
   - Team Management

3. **System Configuration**
   - System Settings
   - Water Rates
   - Templates
   - Integration Settings

4. **Monitoring & Audit**
   - Audit Logs
   - System Health
   - Error Logs
   - Performance Metrics

5. **Communication**
   - WhatsApp Settings
   - Email Templates
   - Notification Settings

**Features**:
- Complete system management access
- Advanced monitoring tools
- User and permission management
- System configuration options
- Audit and logging capabilities

#### 2.3 Manager Sidebar Component
**File**: `resources/views/components/navigation/sidebar/manager-sidebar.blade.php`
**Purpose**: Sidebar for estate managers with oversight of assigned estates

**Navigation Groups**:
1. **Dashboard**
   - Management Dashboard
   - Estate Overview

2. **Estate Management**
   - My Estates
   - Estate Analytics
   - Estate Settings

3. **Team Management**
   - My Team
   - Team Assignments
   - Team Performance

4. **Operations**
   - Houses
   - Contacts
   - Service Requests

5. **Reports & Analytics**
   - Management Reports
   - Performance Analytics
   - Export Data

**Features**:
- Estate-focused management tools
- Team oversight capabilities
- Operational management
- Reporting and analytics
- Estate-scoped data access

#### 2.4 Reviewer Sidebar Component
**File**: `resources/views/components/navigation/sidebar/reviewer-sidebar.blade.php`
**Purpose**: Sidebar for reviewers with billing and reading review focus

**Navigation Groups**:
1. **Dashboard**
   - Reviewer Dashboard
   - Billing Overview

2. **Billing Operations**
   - Invoices
   - Invoice Generation
   - Payment Tracking
   - Billing Reports

3. **Reading Management**
   - Review Readings
   - Approve Readings
   - Reading Validation
   - Reading Analytics

4. **Reporting**
   - Financial Reports
   - Consumption Reports
   - Export Data

5. **Communication**
   - WhatsApp Messages
   - Email Notifications
   - Resident Inquiries

**Features**:
- Billing and invoice management
- Reading review and approval
- Financial reporting
- Communication tools
- Estate-scoped access

#### 2.5 Caretaker Sidebar Component
**File**: `resources/views/components/navigation/sidebar/caretaker-sidebar.blade.php`
**Purpose**: Sidebar for caretakers with data entry focus

**Navigation Groups**:
1. **Dashboard**
   - Caretaker Dashboard
   - My Assignments

2. **Data Entry**
   - Record Readings
   - My Readings
   - Reading History

3. **Contact Management**
   - Contacts
   - Add Contact
   - Contact History

4. **My Estates**
   - Assigned Estates
   - Estate Houses
   - Estate Contacts

**Features**:
- Simplified navigation
- Focus on data entry
- Contact management
- Estate-specific access
- Mobile-optimized design

#### 2.6 Resident Sidebar Component
**File**: `resources/views/components/navigation/sidebar/resident-sidebar.blade.php`
**Purpose**: Sidebar for residents with self-service focus

**Navigation Groups**:
1. **Dashboard**
   - My Dashboard
   - Account Overview

2. **Billing**
   - My Bills
   - Payment History
   - Make Payment

3. **Usage**
   - My Usage
   - Usage History
   - Usage Analytics

4. **Communication**
   - Messages
   - Submit Inquiry
   - Contact Management

5. **Account**
   - Profile Settings
   - Notification Preferences
   - Change Password

**Features**:
- Clean, simple interface
- Personal data access only
- Self-service capabilities
- Communication tools
- Mobile-first design

### 3. Dashboard Components

#### 3.1 Stats Card Component
**File**: `resources/views/components/dashboard/stats-card.blade.php`
**Purpose**: Reusable statistics card for dashboard displays

**Props**:
```php
@props([
    'title' => '',          // Required: Stat title
    'value' => '',          // Required: Stat value
    'icon' => '',           // Optional: Icon markup
    'trend' => null,        // Optional: 'up', 'down', or 'stable'
    'trendValue' => '',     // Optional: Trend percentage or value
    'color' => 'blue',      // Optional: Color theme
    'loading' => false,     // Optional: Loading state
])
```

**Features**:
- Consistent styling for statistics
- Trend indicators
- Icon support
- Loading states
- Multiple color themes
- Responsive design

**Usage Example**:
```blade
<x-dashboard.stats-card 
    title="Total Users" 
    value="{{ $totalUsers }}" 
    icon='<svg>...</svg>' 
    trend="up" 
    trendValue="12%" 
    color="blue" />
```

#### 3.2 Activity Feed Component
**File**: `resources/views/components/dashboard/activity-feed.blade.php`
**Purpose**: Displays recent system or user activity

**Props**:
```php
@props([
    'activities' => [],     // Required: Collection of activities
    'title' => 'Recent Activity', // Optional: Feed title
    'maxItems' => 10,       // Optional: Maximum items to display
])
```

**Features**:
- Activity timeline display
- User avatars/initials
- Timestamp formatting
- Activity type indicators
- Expandable/collapsible sections

#### 3.3 Quick Actions Component
**File**: `resources/views/components/dashboard/quick-actions.blade.php`
**Purpose**: Displays quick action buttons for common tasks

**Props**:
```php
@props([
    'actions' => [],        // Required: Array of action definitions
    'title' => 'Quick Actions', // Optional: Section title
])
```

**Features**:
- Grid layout for actions
- Icon and text display
- Hover effects
- Permission-based visibility
- Responsive design

### 4. Layout Components

#### 4.1 Main Layout Component
**File**: `resources/views/layouts/app.blade.php`
**Purpose**: Main application layout with dynamic sidebar loading

**Features**:
- Dynamic sidebar loading based on user role
- Responsive header with navigation
- Proper content container
- Dark mode support
- Accessibility attributes

#### 4.2 Header Component
**File**: `resources/views/layouts/app/header.blade.php`
**Purpose**: Application header with navigation and user controls

**Features**:
- Logo and branding
- Breadcrumb navigation
- Search functionality
- Notifications
- Theme toggle
- User dropdown with logout
- Mobile menu toggle

#### 4.3 Sidebar Container Component
**File**: `resources/views/layouts/app/sidebar.blade.php`
**Purpose**: Container that loads the appropriate sidebar based on user role

**Features**:
- Role-based component loading
- Fallback handling for unknown roles
- Proper state management
- Mobile responsiveness

## Component Relationships

### Dependency Graph
```
Main Layout
├── Header Component
│   ├── Logo Component
│   ├── Breadcrumbs Component
│   ├── Search Component
│   ├── Notifications Component
│   ├── Theme Toggle Component
│   └── User Dropdown Component
└── Sidebar Container
    └── Role-Specific Sidebar (Admin/Manager/Reviewer/Caretaker/Resident)
        ├── NavLink Component (multiple)
        ├── NavGroup Component (multiple)
        └── User Menu Component
```

### Data Flow
1. **User Authentication**: User logs in and role is determined
2. **Layout Loading**: Main layout loads and determines user role
3. **Sidebar Selection**: Sidebar container loads appropriate role-based sidebar
4. **Permission Checking**: Each NavLink checks permissions before rendering
5. **Active State**: Components determine active state based on current route
6. **User Interaction**: User interacts with navigation, triggering route changes

### State Management
- **User Role**: Determined at authentication, stored in session
- **Permissions**: Cached per user session, validated server-side
- **Active State**: Computed based on current route and component props
- **Mobile State**: Managed via Alpine.js for sidebar toggle
- **Theme State**: Managed via Alpine.js for dark mode toggle

## Performance Considerations

### 1. Component Optimization
- **Lazy Loading**: Load role-specific components only when needed
- **Memoization**: Cache expensive computations within components
- **Conditional Rendering**: Use permission checks to avoid rendering unnecessary components

### 2. Asset Management
- **Code Splitting**: Separate CSS/JS for different roles
- **Caching**: Implement proper caching headers for component assets
- **Minification**: Minify component assets for production

### 3. Permission Caching
- **User Permissions**: Cache user permissions to reduce database queries
- **Role Permissions**: Cache role-based permission sets
- **Menu Structure**: Cache generated menu structures per role

## Security Considerations

### 1. Permission Validation
- **Server-Side Checks**: All permissions validated server-side, not just UI hidden
- **Component-Level**: Each component validates its own permissions
- **Route-Level**: All routes protected by proper middleware

### 2. Input Sanitization
- **Props Validation**: All component props properly validated and sanitized
- **Output Escaping**: All dynamic content properly escaped
- **CSRF Protection**: Forms include proper CSRF tokens

### 3. Access Control
- **Role Isolation**: Users can only access components for their role
- **Data Scoping**: Components only show data user has permission to access
- **Audit Trail**: All permission-sensitive actions logged

## Accessibility Considerations

### 1. Keyboard Navigation
- **Tab Order**: Logical tab order through navigation elements
- **Shortcuts**: Keyboard shortcuts for common actions
- **Focus Management**: Proper focus management for dynamic content

### 2. Screen Reader Support
- **ARIA Labels**: Proper ARIA labels and roles
- **Live Regions**: Screen reader announcements for dynamic changes
- **Semantic HTML**: Proper HTML5 semantic structure

### 3. Visual Accessibility
- **Color Contrast**: Sufficient contrast ratios for all text
- **Visual Indicators**: Clear visual indicators for interactive elements
- **Responsive Design**: All components work on all screen sizes

## Testing Strategy

### 1. Unit Tests
- **Component Rendering**: Test components render correctly with different props
- **Permission Logic**: Test permission checking logic
- **Active State**: Test active state detection
- **Event Handling**: Test user interaction handling

### 2. Integration Tests
- **Role-Based Loading**: Test correct sidebar loads for each role
- **Permission-Based Visibility**: Test links show/hide based on permissions
- **Route Navigation**: Test navigation between different routes
- **User Flow**: Test complete user journeys

### 3. Accessibility Tests
- **Keyboard Navigation**: Test all functionality via keyboard
- **Screen Reader**: Test with popular screen readers
- **Color Contrast**: Test contrast ratios meet WCAG standards
- **Mobile Responsiveness**: Test on various mobile devices

## Maintenance Strategy

### 1. Documentation
- **Component Docs**: Comprehensive documentation for each component
- **Usage Examples**: Clear examples of component usage
- **Prop Reference**: Complete prop reference with types and descriptions
- **Best Practices**: Guidelines for component usage and extension

### 2. Version Control
- **Semantic Versioning**: Follow semantic versioning for component changes
- **Change Log**: Maintain detailed change log
- **Breaking Changes**: Clear communication of breaking changes
- **Deprecation Policy**: Clear deprecation policy for outdated components

### 3. Monitoring
- **Performance**: Monitor component performance metrics
- **Error Tracking**: Track component-related errors
- **Usage Analytics**: Track component usage patterns
- **User Feedback**: Collect and analyze user feedback

## Future Extensibility

### 1. New Roles
- **Component Structure**: Easy to add new role-specific sidebar components
- **Permission System**: Flexible permission system supports new roles
- **Route Organization**: Clear route structure supports new role additions

### 2. New Features
- **Modular Design**: Easy to add new navigation groups and links
- **Component Library**: Reusable components for consistent UI
- **Permission Integration**: Easy to integrate new features with permission system

### 3. Technology Changes
- **Framework Agnostic**: Components designed to work with Laravel/Livewire
- **CSS Framework**: Uses Tailwind CSS for easy theming
- **JavaScript**: Minimal JavaScript dependencies for easy maintenance