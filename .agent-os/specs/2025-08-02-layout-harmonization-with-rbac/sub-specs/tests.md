# Testing Specification

> Spec: Layout Harmonization with Comprehensive RBAC System
> Created: 2025-08-02
> Status: Planning

## Overview

This document outlines the comprehensive testing strategy for the layout harmonization project. The testing approach covers unit tests, integration tests, end-to-end tests, and accessibility tests to ensure the new navigation system works correctly across all user roles and devices.

## Testing Objectives

1. **Functionality**: Verify all navigation components work as expected
2. **Permission-Based Access**: Ensure users only see links they have permission to access
3. **Role-Based Navigation**: Confirm each role loads the correct sidebar
4. **Mobile Responsiveness**: Test navigation works properly on all device sizes
5. **Accessibility**: Ensure navigation meets WCAG 2.1 AA standards
6. **Performance**: Verify navigation doesn't impact page load times
7. **Cross-Browser Compatibility**: Test across major browsers

## Test Environment Setup

### Testing Tools
- **PHPUnit**: For unit and integration tests
- **Pest**: For enhanced testing syntax
- **Laravel Dusk**: For end-to-end browser testing
- **Axios**: For accessibility testing
- **Lighthouse**: For performance and SEO testing
- **BrowserStack**: For cross-browser testing

### Test Data
```php
// factories/UserFactory.php
public function definition()
{
    return [
        'name' => $this->faker->name(),
        'email' => $this->faker->unique()->safeEmail(),
        'password' => bcrypt('password'),
        'role' => $this->faker->randomElement([
            UserRole::ADMIN,
            UserRole::MANAGER,
            UserRole::REVIEWER,
            UserRole::CARETAKER,
            UserRole::RESIDENT,
        ]),
    ];
}

// Test users with specific roles
$adminUser = User::factory()->create(['role' => UserRole::ADMIN]);
$managerUser = User::factory()->create(['role' => UserRole::MANAGER]);
$reviewerUser = User::factory()->create(['role' => UserRole::REVIEWER]);
$caretakerUser = User::factory()->create(['role' => UserRole::CARETAKER]);
$residentUser = User::factory()->create(['role' => UserRole::RESIDENT]);
```

## Unit Tests

### 1. NavLink Component Tests

**File**: `tests/Unit/Components/NavLinkTest.php`

```php
<?php

use Tests\TestCase;
use Illuminate\Support\Facades\Auth;

class NavLinkTest extends TestCase
{
    public function test_renders_link_with_basic_props()
    {
        $component = $this->blade('<x-nav-link href="/test" title="Test Link" />');
        
        $component->assertSee('Test Link')
                 ->assertSee('href="/test"', false);
    }

    public function test_respects_permission_checking()
    {
        $user = User::factory()->create(['role' => UserRole::CARETAKER]);
        Auth::login($user);
        
        // Link with permission user doesn't have
        $component = $this->blade(
            '<x-nav-link href="/admin" title="Admin" permission="users.manage_all" />'
        );
        
        $component->assertDontSee('Admin');
    }

    public function test_shows_link_with_valid_permission()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        Auth::login($user);
        
        $component = $this->blade(
            '<x-nav-link href="/admin" title="Admin" permission="users.manage_all" />'
        );
        
        $component->assertSee('Admin');
    }

    public function test_highlights_active_route()
    {
        $component = $this->blade(
            '<x-nav-link href="/dashboard" title="Dashboard" :activeRoutes="["dashboard*"]" />',
            ['currentRoute' => 'dashboard']
        );
        
        $component->assertSee('bg-brand-100');
    }

    public function test_displays_badge_when_provided()
    {
        $component = $this->blade(
            '<x-nav-link href="/test" title="Test" badge="5" />'
        );
        
        $component->assertSee('5')
                 ->assertSee('bg-red-100');
    }

    public function test_handles_external_links()
    {
        $component = $this->blade(
            '<x-nav-link href="https://example.com" title="External" external="true" />'
        );
        
        $component->assertSee('target="_self"');
    }
}
```

### 2. Sidebar Component Tests

**File**: `tests/Unit/Components/SidebarTest.php`

```php
<?php

use Tests\TestCase;
use Illuminate\Support\Facades\Auth;

class SidebarTest extends TestCase
{
    public function test_admin_sidebar_loads_for_admin_user()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        Auth::login($user);
        
        $component = $this->blade('<x-navigation.sidebar.admin-sidebar />');
        
        $component->assertSee('Admin Dashboard')
                 ->assertSee('User Management')
                 ->assertSee('System Settings');
    }

    public function test_manager_sidebar_loads_for_manager_user()
    {
        $user = User::factory()->create(['role' => UserRole::MANAGER]);
        Auth::login($user);
        
        $component = $this->blade('<x-navigation.sidebar.manager-sidebar />');
        
        $component->assertSee('Management Dashboard')
                 ->assertSee('Estate Management')
                 ->assertSee('Team Management');
    }

    public function test_reviewer_sidebar_loads_for_reviewer_user()
    {
        $user = User::factory()->create(['role' => UserRole::REVIEWER]);
        Auth::login($user);
        
        $component = $this->blade('<x-navigation.sidebar.reviewer-sidebar />');
        
        $component->assertSee('Reviewer Dashboard')
                 ->assertSee('Billing Management')
                 ->assertSee('Reading Management');
    }

    public function test_caretaker_sidebar_loads_for_caretaker_user()
    {
        $user = User::factory()->create(['role' => UserRole::CARETAKER]);
        Auth::login($user);
        
        $component = $this->blade('<x-navigation.sidebar.caretaker-sidebar />');
        
        $component->assertSee('Caretaker Dashboard')
                 ->assertSee('Reading Management')
                 ->assertSee('Contact Management');
    }

    public function test_resident_sidebar_loads_for_resident_user()
    {
        $user = User::factory()->create(['role' => UserRole::RESIDENT]);
        Auth::login($user);
        
        $component = $this->blade('<x-navigation.sidebar.resident-sidebar />');
        
        $component->assertSee('Resident Dashboard')
                 ->assertSee('Billing')
                 ->assertSee('Usage');
    }
}
```

### 3. Dashboard Component Tests

**File**: `tests/Unit/Livewire/AdminDashboardTest.php`

```php
<?php

use Tests\TestCase;
use App\Livewire\Admin\AdminDashboard;
use Illuminate\Support\Facades\Auth;
use Livewire\Livewire;

class AdminDashboardTest extends TestCase
{
    public function test_admin_dashboard_loads_system_metrics()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        Auth::login($user);
        
        Livewire::test(AdminDashboard::class)
            ->assertSee('Total Users')
            ->assertSee('Total Estates')
            ->assertSee('Total Houses')
            ->assertSee('Recent Activity');
    }

    public function test_admin_dashboard_shows_quick_actions()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        Auth::login($user);
        
        Livewire::test(AdminDashboard::class)
            ->assertSee('Manage Users')
            ->assertSee('System Settings')
            ->assertSee('Estate Assignments');
    }

    public function test_admin_dashboard_requires_admin_permission()
    {
        $user = User::factory()->create(['role' => UserRole::MANAGER]);
        Auth::login($user);
        
        $this->get('/admin/dashboard')->assertForbidden();
    }
}
```

## Integration Tests

### 1. Role-Based Navigation Tests

**File**: `tests/Feature/RoleBasedNavigationTest.php`

```php
<?php

use Tests\TestCase;
use App\Enums\UserRole;
use Illuminate\Support\Facades\Auth;

class RoleBasedNavigationTest extends TestCase
{
    public function test_admin_user_sees_admin_navigation()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertRedirect('/admin/dashboard');
        
        $response = $this->actingAs($user)->get('/admin/dashboard');
        $response->assertSee('Admin Dashboard')
                 ->assertSee('User Management')
                 ->assertSee('System Settings');
    }

    public function test_manager_user_sees_manager_navigation()
    {
        $user = User::factory()->create(['role' => UserRole::MANAGER]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertRedirect('/management/dashboard');
        
        $response = $this->actingAs($user)->get('/management/dashboard');
        $response->assertSee('Management Dashboard')
                 ->assertSee('Estate Management')
                 ->assertSee('Team Management');
    }

    public function test_reviewer_user_sees_reviewer_navigation()
    {
        $user = User::factory()->create(['role' => UserRole::REVIEWER]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertRedirect('/reviewer/dashboard');
        
        $response = $this->actingAs($user)->get('/reviewer/dashboard');
        $response->assertSee('Reviewer Dashboard')
                 ->assertSee('Billing Management')
                 ->assertSee('Reading Management');
    }

    public function test_caretaker_user_sees_caretaker_navigation()
    {
        $user = User::factory()->create(['role' => UserRole::CARETAKER]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertRedirect('/caretaker/dashboard');
        
        $response = $this->actingAs($user)->get('/caretaker/dashboard');
        $response->assertSee('Caretaker Dashboard')
                 ->assertSee('Reading Management')
                 ->assertSee('Contact Management');
    }

    public function test_resident_user_sees_resident_navigation()
    {
        $user = User::factory()->create(['role' => UserRole::RESIDENT]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertRedirect('/resident/dashboard');
        
        $response = $this->actingAs($user)->get('/resident/dashboard');
        $response->assertSee('Resident Dashboard')
                 ->assertSee('Billing')
                 ->assertSee('Usage');
    }

    public function test_users_cannot_access_other_role_dashboards()
    {
        $manager = User::factory()->create(['role' => UserRole::MANAGER]);
        $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
        
        // Manager cannot access admin dashboard
        $this->actingAs($manager)->get('/admin/dashboard')->assertForbidden();
        
        // Reviewer cannot access manager dashboard
        $this->actingAs($reviewer)->get('/management/dashboard')->assertForbidden();
    }
}
```

### 2. Permission-Based Navigation Tests

**File**: `tests/Feature/PermissionBasedNavigationTest.php`

```php
<?php

use Tests\TestCase;
use App\Enums\UserRole;
use Illuminate\Support\Facades\Auth;

class PermissionBasedNavigationTest extends TestCase
{
    public function test_admin_sees_all_navigation_links()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        
        $response = $this->actingAs($user)->get('/admin/dashboard');
        
        $response->assertSee('Users')
                 ->assertSee('Permissions')
                 ->assertSee('System Settings')
                 ->assertSee('Audit Logs')
                 ->assertSee('Estate Assignments');
    }

    public function test_manager_sees_only_assigned_navigation_links()
    {
        $user = User::factory()->create(['role' => UserRole::MANAGER]);
        
        $response = $this->actingAs($user)->get('/management/dashboard');
        
        $response->assertSee('Estate Management')
                 ->assertSee('Team Management')
                 ->assertSee('Reports')
                 ->assertDontSee('System Settings')
                 ->assertDontSee('Audit Logs');
    }

    public function test_reviewer_sees_only_billing_and_review_links()
    {
        $user = User::factory()->create(['role' => UserRole::REVIEWER]);
        
        $response = $this->actingAs($user)->get('/reviewer/dashboard');
        
        $response->assertSee('Billing Management')
                 ->assertSee('Reading Management')
                 ->assertSee('Reports')
                 ->assertDontSee('User Management')
                 ->assertDontSee('System Settings');
    }

    public function test_caretaker_sees_only_data_entry_links()
    {
        $user = User::factory()->create(['role' => UserRole::CARETAKER]);
        
        $response = $this->actingAs($user)->get('/caretaker/dashboard');
        
        $response->assertSee('Reading Management')
                 ->assertSee('Contact Management')
                 ->assertDontSee('Billing Management')
                 ->assertDontSee('User Management');
    }

    public function test_resident_sees_only_self_service_links()
    {
        $user = User::factory()->create(['role' => UserRole::RESIDENT]);
        
        $response = $this->actingAs($user)->get('/resident/dashboard');
        
        $response->assertSee('Billing')
                 ->assertSee('Usage')
                 ->assertSee('Messages')
                 ->assertDontSee('User Management')
                 ->assertDontSee('System Settings');
    }
}
```

### 3. Route Protection Tests

**File**: `tests/Feature/RouteProtectionTest.php`

```php
<?php

use Tests\TestCase;
use App\Enums\UserRole;
use Illuminate\Support\Facades\Auth;

class RouteProtectionTest extends TestCase
{
    public function test_admin_routes_protected_by_admin_permission()
    {
        $admin = User::factory()->create(['role' => UserRole::ADMIN]);
        $manager = User::factory()->create(['role' => UserRole::MANAGER]);
        
        // Admin can access admin routes
        $this->actingAs($admin)->get('/admin/dashboard')->assertOk();
        $this->actingAs($admin)->get('/admin/users')->assertOk();
        $this->actingAs($admin)->get('/admin/settings')->assertOk();
        
        // Manager cannot access admin routes
        $this->actingAs($manager)->get('/admin/dashboard')->assertForbidden();
        $this->actingAs($manager)->get('/admin/users')->assertForbidden();
        $this->actingAs($manager)->get('/admin/settings')->assertForbidden();
    }

    public function test_manager_routes_protected_by_manager_permission()
    {
        $manager = User::factory()->create(['role' => UserRole::MANAGER]);
        $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
        
        // Manager can access manager routes
        $this->actingAs($manager)->get('/management/dashboard')->assertOk();
        $this->actingAs($manager)->get('/management/estates')->assertOk();
        $this->actingAs($manager)->get('/management/team')->assertOk();
        
        // Reviewer cannot access manager routes
        $this->actingAs($reviewer)->get('/management/dashboard')->assertForbidden();
        $this->actingAs($reviewer)->get('/management/estates')->assertForbidden();
        $this->actingAs($reviewer)->get('/management/team')->assertForbidden();
    }

    public function test_reviewer_routes_protected_by_reviewer_permission()
    {
        $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
        $caretaker = User::factory()->create(['role' => UserRole::CARETAKER]);
        
        // Reviewer can access reviewer routes
        $this->actingAs($reviewer)->get('/reviewer/dashboard')->assertOk();
        $this->actingAs($reviewer)->get('/reviewer/billing')->assertOk();
        $this->actingAs($reviewer)->get('/reviewer/readings')->assertOk();
        
        // Caretaker cannot access reviewer routes
        $this->actingAs($caretaker)->get('/reviewer/dashboard')->assertForbidden();
        $this->actingAs($caretaker)->get('/reviewer/billing')->assertForbidden();
        $this->actingAs($caretaker)->get('/reviewer/readings')->assertForbidden();
    }

    public function test_caretaker_routes_protected_by_caretaker_permission()
    {
        $caretaker = User::factory()->create(['role' => UserRole::CARETAKER]);
        $resident = User::factory()->create(['role' => UserRole::RESIDENT]);
        
        // Caretaker can access caretaker routes
        $this->actingAs($caretaker)->get('/caretaker/dashboard')->assertOk();
        $this->actingAs($caretaker)->get('/caretaker/readings')->assertOk();
        $this->actingAs($caretaker)->get('/caretaker/contacts')->assertOk();
        
        // Resident cannot access caretaker routes
        $this->actingAs($resident)->get('/caretaker/dashboard')->assertForbidden();
        $this->actingAs($resident)->get('/caretaker/readings')->assertForbidden();
        $this->actingAs($resident)->get('/caretaker/contacts')->assertForbidden();
    }

    public function test_resident_routes_protected_by_resident_permission()
    {
        $resident = User::factory()->create(['role' => UserRole::RESIDENT]);
        $caretaker = User::factory()->create(['role' => UserRole::CARETAKER]);
        
        // Resident can access resident routes
        $this->actingAs($resident)->get('/resident/dashboard')->assertOk();
        $this->actingAs($resident)->get('/resident/bills')->assertOk();
        $this->actingAs($resident)->get('/resident/usage')->assertOk();
        
        // Caretaker cannot access resident routes
        $this->actingAs($caretaker)->get('/resident/dashboard')->assertForbidden();
        $this->actingAs($caretaker)->get('/resident/bills')->assertForbidden();
        $this->actingAs($caretaker)->get('/resident/usage')->assertForbidden();
    }
}
```

## End-to-End Tests

### 1. User Journey Tests

**File**: `tests/Browser/UserJourneyTest.php`

```php
<?php

use Tests\DuskTestCase;
use Laravel\Dusk\Browser;
use App\Enums\UserRole;

class UserJourneyTest extends DuskTestCase
{
    public function test_admin_complete_journey()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        
        $this->browse(function (Browser $browser) use ($user) {
            $browser->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Log in')
                    ->assertPathIs('/admin/dashboard')
                    ->assertSee('Admin Dashboard')
                    ->clickLink('Users')
                    ->assertPathIs('/admin/users')
                    ->assertSee('User Management')
                    ->clickLink('System Settings')
                    ->assertPathIs('/admin/settings')
                    ->assertSee('System Settings')
                    ->clickLink('Audit Logs')
                    ->assertPathIs('/admin/audit')
                    ->assertSee('Audit Logs');
        });
    }

    public function test_manager_complete_journey()
    {
        $user = User::factory()->create(['role' => UserRole::MANAGER]);
        
        $this->browse(function (Browser $browser) use ($user) {
            $browser->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Log in')
                    ->assertPathIs('/management/dashboard')
                    ->assertSee('Management Dashboard')
                    ->clickLink('Estates')
                    ->assertPathIs('/management/estates')
                    ->assertSee('Estate Management')
                    ->clickLink('Team')
                    ->assertPathIs('/management/team')
                    ->assertSee('Team Management')
                    ->clickLink('Reports')
                    ->assertPathIs('/management/reports')
                    ->assertSee('Reports');
        });
    }

    public function test_mobile_responsive_navigation()
    {
        $user = User::factory()->create(['role' => UserRole::MANAGER]);
        
        $this->browse(function (Browser $browser) use ($user) {
            $browser->resize(375, 667) // Mobile size
                    ->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Log in')
                    ->assertPathIs('/management/dashboard')
                    ->assertSee('Management Dashboard')
                    ->click('@mobile-menu-toggle') // Mobile menu toggle
                    ->waitFor('@mobile-menu')
                    ->assertVisible('@mobile-menu')
                    ->clickLink('Estates')
                    ->assertPathIs('/management/estates');
        });
    }

    public function test_logout_functionality()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        
        $this->browse(function (Browser $browser) use ($user) {
            $browser->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Log in')
                    ->assertPathIs('/admin/dashboard')
                    ->click('@user-dropdown')
                    ->clickLink('Log Out')
                    ->assertPathIs('/login')
                    ->assertSee('Please sign in');
        });
    }
}
```

### 2. Accessibility Tests

**File**: `tests/Browser/AccessibilityTest.php`

```php
<?php

use Tests\DuskTestCase;
use Laravel\Dusk\Browser;
use App\Enums\UserRole;

class AccessibilityTest extends DuskTestCase
{
    public function test_keyboard_navigation()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        
        $this->browse(function (Browser $browser) use ($user) {
            $browser->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Log in')
                    ->assertPathIs('/admin/dashboard')
                    ->keys('', '{tab}') // Tab to first focusable element
                    ->assertFocused('@first-focusable-element')
                    ->keys('', '{tab}') // Tab to next element
                    ->assertFocused('@second-focusable-element')
                    ->keys('', '{enter}') // Activate link with Enter key
                    ->assertPathIs('/admin/users'); // Should navigate to users
        });
    }

    public function test_screen_reader_compatibility()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        
        $this->browse(function (Browser $browser) use ($user) {
            $browser->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Log in')
                    ->assertPathIs('/admin/dashboard')
                    ->assertPresent('[aria-label="Admin Dashboard"]')
                    ->assertPresent('[role="navigation"]')
                    ->assertPresent('[aria-current="page"]');
        });
    }

    public function test_color_contrast()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        
        $this->browse(function (Browser $browser) use ($user) {
            $browser->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Log in')
                    ->assertPathIs('/admin/dashboard')
                    ->assertSee('Admin Dashboard');
            
            // This would typically use a tool like Axe to check contrast
            // For now, we'll just verify the elements are visible
            $browser->assertVisible('.text-brand-700')
                    ->assertVisible('.text-gray-700');
        });
    }
}
```

## Performance Tests

### 1. Navigation Performance Tests

**File**: `tests/Browser/PerformanceTest.php**

```php
<?php

use Tests\DuskTestCase;
use Laravel\Dusk\Browser;
use App\Enums\UserRole;

class PerformanceTest extends DuskTestCase
{
    public function test_navigation_load_time()
    {
        $user = User::factory()->create(['role' => UserRole::ADMIN]);
        
        $this->browse(function (Browser $browser) use ($user) {
            $browser->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Log in')
                    ->assertPathIs('/admin/dashboard');
            
            // Measure load time for navigation
            $startTime = microtime(true);
            $browser->clickLink('Users')
                    ->assertPathIs('/admin/users');
            $endTime = microtime(true);
            
            $loadTime = $endTime - $startTime;
            $this->assertLessThan(2, $loadTime, 'Navigation load time should be less than 2 seconds');
        });
    }

    public function test_mobile_navigation_performance()
    {
        $user = User::factory()->create(['role' => UserRole::MANAGER]);
        
        $this->browse(function (Browser $browser) use ($user) {
            $browser->resize(375, 667) // Mobile size
                    ->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Log in')
                    ->assertPathIs('/management/dashboard');
            
            // Measure mobile navigation performance
            $startTime = microtime(true);
            $browser->click('@mobile-menu-toggle')
                    ->waitFor('@mobile-menu')
                    ->clickLink('Estates')
                    ->assertPathIs('/management/estates');
            $endTime = microtime(true);
            
            $loadTime = $endTime - $startTime;
            $this->assertLessThan(3, $loadTime, 'Mobile navigation load time should be less than 3 seconds');
        });
    }
}
```

## Test Coverage Requirements

### Coverage Targets
- **Unit Tests**: 90%+ code coverage
- **Integration Tests**: 80%+ feature coverage
- **End-to-End Tests**: 100% critical user journey coverage
- **Accessibility Tests**: 100% WCAG 2.1 AA compliance

### Critical Test Scenarios
1. **Role-Based Navigation**: All 5 roles must be tested
2. **Permission-Based Access**: All major permissions must be tested
3. **Route Protection**: All protected routes must be tested
4. **Mobile Responsiveness**: All components must work on mobile
5. **Accessibility**: All navigation must be accessible
6. **Performance**: Navigation must meet performance targets

### Test Data Management
- Use factories for test data creation
- Clean up test data after each test
- Use database transactions for data isolation
- Seed necessary test data (permissions, roles, etc.)

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Layout Harmonization Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: water_management_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql
        coverage: xdebug
    
    - name: Install Dependencies
      run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
    
    - name: Copy Environment File
      run: cp .env.example .env
    
    - name: Generate Application Key
      run: php artisan key:generate
    
    - name: Run Migrations
      run: php artisan migrate --force
    
    - name: Run Unit Tests
      run: vendor/bin/pest --coverage=coverage.xml --coverage-xml=coverage-xml
    
    - name: Run Integration Tests
      run: vendor/bin/pest --testsuite=Integration
    
    - name: Run Dusk Tests
      run: php artisan dusk:chrome-driver && php artisan dusk
    
    - name: Upload Coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

## Test Reporting

### Metrics to Track
- Test execution time
- Test pass/fail rate
- Code coverage percentage
- Accessibility violations
- Performance metrics
- Bug discovery rate

### Reporting Tools
- **GitHub Actions**: For CI/CD pipeline results
- **Codecov**: For code coverage reporting
- **Lighthouse**: For performance and accessibility reports
- **Sauce Labs**: For cross-browser testing results

## Test Maintenance

### Test Data Updates
- Regularly update test factories to match schema changes
- Add new test scenarios for new features
- Remove obsolete tests for deprecated features

### Performance Monitoring
- Monitor test execution times
- Identify and fix slow tests
- Optimize test data setup

### Accessibility Monitoring
- Regular accessibility audits
- Update tests for new accessibility requirements
- Monitor WCAG guideline changes

This comprehensive testing strategy ensures that the layout harmonization project meets all functional, performance, and accessibility requirements while maintaining high code quality and user experience across all roles and devices.