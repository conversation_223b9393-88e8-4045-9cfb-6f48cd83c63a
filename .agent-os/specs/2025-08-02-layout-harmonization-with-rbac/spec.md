# Spec Requirements Document

> Spec: Layout Harmonization with Comprehensive RBAC System
> Created: 2025-08-02
> Status: In Progress

## Overview

Harmonize the application layouts, navigation, and routing system to work seamlessly with the new comprehensive RBAC system. This includes creating role-specific sidebars, adding missing logout functionality, implementing an admin dashboard, refactoring repeating link components, and ensuring all new features are properly accessible through the navigation structure.

## Implementation Progress

**Completed Tasks:**
- ✅ **Route Conflict Resolution**: Removed conflicting legacy routes and updated navigation sidebars to use role-based routes, eliminating 403 errors for management users
- ✅ **Navigation Sidebar Updates**: Updated manager, reviewer, and caretaker sidebars to use proper role-based routes with correct permissions
- ✅ **Permission Harmonization**: Fixed permission checking across navigation components to align with RBAC structure

**In Progress:**

## Problem Statement

The current layout system has several issues that need to be addressed:

1. **Missing Logout Button**: The main public-facing layout lacks a logout button next to the dashboard link
2. **403 Errors on Sidebar**: New features with complex role-based permissions are leading to 403 pages because the sidebar shows links users can't access
3. **Single Sidebar Architecture**: The app uses a single sidebar with complex conditional logic instead of role-specific sidebars
4. **Repeating Link Components**: The sidebar has repetitive link components that need refactoring for maintainability
5. **Missing Admin Dashboard**: No dedicated admin dashboard exists for system administrators
6. **Route Inconsistencies**: Web routes need harmonization with the new RBAC permission structure

## Implementation Goals

1. **Role-Specific Sidebars**: Create separate sidebar components for each user role (Admin, Manager, Reviewer, Caretaker, Resident)
2. **Permission-Based Navigation**: Ensure sidebar links only show when users have the required permissions
3. **Admin Dashboard**: Implement a comprehensive admin dashboard for system administrators
4. **Component Refactoring**: Extract reusable navigation link components to reduce code duplication
5. **Logout Accessibility**: Add logout functionality to all layouts and headers
6. **Route Harmonization**: Update web routes to align with the new RBAC permission structure

## User Stories

### As a System Administrator
I want a dedicated admin dashboard and sidebar that provides access to all system management features, so that I can efficiently manage users, permissions, estates, and system settings without encountering 403 errors.

### As an Estate Manager
I want a manager-specific sidebar that shows only the features I have access to within my assigned estates, so that I can navigate efficiently without seeing irrelevant or restricted options.

### As a Reviewer
I want a reviewer-focused sidebar that provides quick access to billing, reading reviews, and reporting features for my assigned estates, so that I can perform my accounting duties efficiently.

### As a Caretaker
I want a simplified sidebar that only shows meter reading entry and contact management features for my assigned estates, so that I can focus on my field data collection tasks.

### As a Resident
I want a clean, simple sidebar that provides access only to my personal billing information, usage history, and communication tools, so that I can manage my account without confusion.

### As a Developer
I want well-structured, maintainable navigation components with proper separation of concerns, so that I can easily add new features and modify the navigation structure without breaking existing functionality.

## Spec Scope

### Phase 1: Layout Architecture Refactoring
1. **Role-Specific Sidebar Components**
   - Create `AdminSidebar` component with full system management access
   - Create `ManagerSidebar` component with estate management features
   - Create `ReviewerSidebar` component with billing and review features
   - Create `CaretakerSidebar` component with data entry features
   - Create `ResidentSidebar` component with self-service features

2. **Navigation Link Component**
   - Create reusable `NavLink` component for consistent link styling
   - Implement permission checking within the component
   - Add support for icons, badges, and active states
   - Include proper accessibility attributes

3. **Layout Structure Updates**
   - Update main layout to dynamically load role-specific sidebar
   - Ensure logout functionality is available in all layouts
   - Add proper mobile responsiveness to all navigation components

### Phase 2: Admin Dashboard Implementation
1. **Admin Dashboard Component**
   - Create comprehensive admin dashboard with system overview
   - Include key metrics: total users, estates, houses, recent activity
   - Add quick access to critical admin functions
   - Implement system health monitoring widgets

2. **Admin Navigation Structure**
   - Organize admin features into logical groups
   - User Management (users, roles, permissions, estate assignments)
   - System Configuration (settings, rates, templates)
   - Monitoring & Audit (logs, reports, analytics)
   - Communication (WhatsApp, email templates)

### Phase 3: Route and Permission Harmonization
1. **Web Route Updates**
   - Review and update all route permissions to match new RBAC structure
   - Implement proper middleware for role-based access
   - Add estate-scoped routing where appropriate
   - Ensure consistent route naming conventions

2. **Permission Validation**
   - Update all `@can` directives in layouts to use new permission structure
   - Implement proper permission checking in navigation components
   - Add fallback handling for missing permissions
   - Ensure graceful degradation when permissions change

### Phase 4: Component Refactoring and Testing
1. **Code Quality Improvements**
   - Extract common navigation patterns into reusable components
   - Implement proper TypeScript/PHP type hints
   - Add comprehensive documentation for all components
   - Ensure consistent coding standards across all navigation components

2. **Testing and Validation**
   - Write unit tests for all navigation components
   - Create integration tests for role-based navigation
   - Test permission-based link visibility
   - Validate mobile responsiveness across all layouts

## Technical Implementation Details

### Component Architecture
```
resources/views/components/navigation/
├── nav-link.blade.php           # Reusable navigation link component
├── sidebar/
│   ├── admin-sidebar.blade.php  # Admin-specific sidebar
│   ├── manager-sidebar.blade.php # Manager-specific sidebar
│   ├── reviewer-sidebar.blade.php # Reviewer-specific sidebar
│   ├── caretaker-sidebar.blade.php # Caretaker-specific sidebar
│   └── resident-sidebar.blade.php # Resident-specific sidebar
└── dashboard/
    ├── admin-dashboard.blade.php # Admin dashboard widgets
    └── dashboard-stats.blade.php # Reusable stats components
```

### Permission Mapping
The navigation system will use the following permission mapping:

| Role | Dashboard Route | Sidebar Component | Key Permissions |
|------|----------------|-------------------|-----------------|
| Admin | `admin.dashboard` | `AdminSidebar` | `users.manage_all`, `system.settings.manage` |
| Manager | `management.dashboard` | `ManagerSidebar` | `estates.manage_assigned`, `users.view_assigned` |
| Reviewer | `reviewer.dashboard` | `ReviewerSidebar` | `invoices.generate_assigned`, `readings.approve_assigned` |
| Caretaker | `caretaker.dashboard` | `CaretakerSidebar` | `readings.create_assigned`, `contacts.manage_assigned` |
| Resident | `resident.dashboard` | `ResidentSidebar` | `resident.portal.access`, `invoices.view_own` |

### Route Structure Updates
```php
// Updated route structure with proper RBAC middleware
Route::middleware(['auth', 'verified'])->group(function () {
    // Admin routes
    Route::prefix('admin')->middleware('can:users.manage_all')->group(function () {
        Route::get('dashboard', AdminDashboard::class)->name('admin.dashboard');
        Route::get('users', UserManager::class)->name('admin.users');
        Route::get('permissions', PermissionManager::class)->name('admin.permissions');
        Route::get('settings', SystemSettings::class)->name('admin.settings');
        Route::get('audit', AuditLogs::class)->name('admin.audit');
    });

    // Manager routes
    Route::prefix('management')->middleware('can:estates.manage_assigned')->group(function () {
        Route::get('dashboard', ManagementDashboard::class)->name('management.dashboard');
        Route::get('estates', EstateManager::class)->name('management.estates');
        Route::get('team', TeamManagement::class)->name('management.team');
        Route::get('reports', ManagementReports::class)->name('management.reports');
    });

    // Reviewer routes
    Route::prefix('reviewer')->middleware('can:invoices.generate_assigned')->group(function () {
        Route::get('dashboard', ReviewerDashboard::class)->name('reviewer.dashboard');
        Route::get('billing', BillingManagement::class)->name('reviewer.billing');
        Route::get('readings', ReadingReview::class)->name('reviewer.readings');
        Route::get('reports', ReviewerReports::class)->name('reviewer.reports');
    });

    // Caretaker routes
    Route::prefix('caretaker')->middleware('can:readings.create_assigned')->group(function () {
        Route::get('dashboard', CaretakerDashboard::class)->name('caretaker.dashboard');
        Route::get('readings', MeterReadingEntry::class)->name('caretaker.readings');
        Route::get('contacts', ContactManager::class)->name('caretaker.contacts');
    });

    // Resident routes
    Route::prefix('resident')->middleware('can:resident.portal.access')->group(function () {
        Route::get('dashboard', ResidentDashboard::class)->name('resident.dashboard');
        Route::get('bills', ResidentBills::class)->name('resident.bills');
        Route::get('usage', ResidentUsage::class)->name('resident.usage');
        Route::get('messages', ResidentMessages::class)->name('resident.messages');
    });
});
```

## Out of Scope

- Complete redesign of the UI/UX (focusing on functionality, not visual design)
- Implementation of new features beyond navigation and layout improvements
- Database schema changes (using existing RBAC structure)
- External API integrations for navigation
- Advanced personalization features beyond role-based customization

## Expected Deliverables

1. **Role-Specific Sidebar Components**
   - Five distinct sidebar components, one for each user role
   - Proper permission-based link visibility
   - Consistent styling and behavior across all components

2. **Admin Dashboard**
   - Comprehensive admin dashboard with system overview
   - Quick access to critical admin functions
   - Real-time system health monitoring

3. **Reusable Navigation Components**
   - `NavLink` component for consistent link styling
   - Proper permission checking and accessibility
   - Support for icons, badges, and active states

4. **Updated Route Structure**
   - Harmonized web routes aligned with RBAC permissions
   - Proper middleware for role-based access control
   - Consistent naming conventions and organization

5. **Logout Functionality**
   - Logout button available in all layouts and headers
   - Proper CSRF protection and form handling
   - Consistent user experience across all roles

## Success Criteria

- **Role Separation**: Each user role sees only navigation links relevant to their permissions
- **No 403 Errors**: Users never encounter 403 errors from navigation links
- **Admin Access**: System administrators have comprehensive access to all management features
- **Code Quality**: Navigation components are well-structured, reusable, and maintainable
- **Mobile Responsiveness**: All navigation components work properly on mobile devices
- **Performance**: Navigation rendering doesn't impact page load times
- **Accessibility**: All navigation components meet WCAG 2.1 AA standards
- **Test Coverage**: All navigation components have comprehensive test coverage

## Spec Documentation

- Tasks: @.agent-os/specs/2025-08-02-layout-harmonization-with-rbac/tasks.md
- Technical Specification: @.agent-os/specs/2025-08-02-layout-harmonization-with-rbac/sub-specs/technical-spec.md
- Component Architecture: @.agent-os/specs/2025-08-02-layout-harmonization-with-rbac/sub-specs/component-architecture.md
- Route Mapping: @.agent-os/specs/2025-08-02-layout-harmonization-with-rbac/sub-specs/route-mapping.md
- Testing Specification: @.agent-os/specs/2025-08-02-layout-harmonization-with-rbac/sub-specs/tests.md