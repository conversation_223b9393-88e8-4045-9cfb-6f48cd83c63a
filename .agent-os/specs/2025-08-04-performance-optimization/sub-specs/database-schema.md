# Database Schema Specification

This is the database schema specification for @.agent-os/specs/2025-08-04-performance-optimization/spec.md

> Created: 2025-08-04
> Version: 1.0.0

## New Database Indexes

### invoices Table
```php
// Composite index for status and due_date queries
$table->index(['status', 'due_date'], 'invoices_status_due_date_index');

// Index for approval workflow queries
$table->index('submitted_at', 'invoices_submitted_at_index');
$table->index('approved_at', 'invoices_approved_at_index');

// Composite index for house and status queries
$table->index(['house_id', 'status'], 'invoices_house_status_index');
```

### meter_readings Table
```php
// Composite index for house and reading_date queries
$table->index(['house_id', 'reading_date'], 'meter_readings_house_date_index');

// Index for reading validation queries
$table->index('status', 'meter_readings_status_index');
```

### contacts Table
```php
// Composite index for primary contact lookups
$table->index(['house_id', 'is_primary'], 'contacts_house_primary_index');

// Index for notification queries
$table->index(['receive_notifications', 'has_whatsapp'], 'contacts_notification_index');

// Index for contact search
$table->index('phone_number', 'contacts_phone_index');
```

### houses Table
```php
// Index for estate queries
$table->index('estate_id', 'houses_estate_index');

// Index for house number searches
$table->index('house_number', 'houses_number_index');
```

### users Table
```php
// Index for role-based queries
$table->index('role', 'users_role_index');

// Composite index for active users
$table->index(['role', 'active'], 'users_role_active_index');
```

## Migration Structure
```php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // invoices table indexes
        Schema::table('invoices', function (Blueprint $table) {
            $table->index(['status', 'due_date'], 'invoices_status_due_date_index');
            $table->index('submitted_at', 'invoices_submitted_at_index');
            $table->index('approved_at', 'invoices_approved_at_index');
            $table->index(['house_id', 'status'], 'invoices_house_status_index');
        });

        // meter_readings table indexes
        Schema::table('meter_readings', function (Blueprint $table) {
            $table->index(['house_id', 'reading_date'], 'meter_readings_house_date_index');
            $table->index('status', 'meter_readings_status_index');
        });

        // contacts table indexes
        Schema::table('contacts', function (Blueprint $table) {
            $table->index(['house_id', 'is_primary'], 'contacts_house_primary_index');
            $table->index(['receive_notifications', 'has_whatsapp'], 'contacts_notification_index');
            $table->index('phone_number', 'contacts_phone_index');
        });

        // houses table indexes
        Schema::table('houses', function (Blueprint $table) {
            $table->index('estate_id', 'houses_estate_index');
            $table->index('house_number', 'houses_number_index');
        });

        // users table indexes
        Schema::table('users', function (Blueprint $table) {
            $table->index('role', 'users_role_index');
            $table->index(['role', 'active'], 'users_role_active_index');
        });
    }

    public function down()
    {
        // Drop all added indexes
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropIndex('invoices_status_due_date_index');
            $table->dropIndex('invoices_submitted_at_index');
            $table->dropIndex('invoices_approved_at_index');
            $table->dropIndex('invoices_house_status_index');
        });

        Schema::table('meter_readings', function (Blueprint $table) {
            $table->dropIndex('meter_readings_house_date_index');
            $table->dropIndex('meter_readings_status_index');
        });

        Schema::table('contacts', function (Blueprint $table) {
            $table->dropIndex('contacts_house_primary_index');
            $table->dropIndex('contacts_notification_index');
            $table->dropIndex('contacts_phone_index');
        });

        Schema::table('houses', function (Blueprint $table) {
            $table->dropIndex('houses_estate_index');
            $table->dropIndex('houses_number_index');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('users_role_index');
            $table->dropIndex('users_role_active_index');
        });
    }
};
```

## Performance Impact Analysis
- **Query Performance:** Expected 60-80% improvement in common query patterns
- **Insert Performance:** Minimal impact (5-10% slower due to index maintenance)
- **Storage Impact:** Approximately 5-10% increase in database size
- **Memory Usage:** Improved query efficiency will reduce memory usage

## Index Usage Monitoring
```php
// Add to AppServiceProvider for monitoring
DB::listen(function ($query) {
    if (str_contains($query->sql, 'where')) {
        Log::channel('index-usage')->info('Query executed', [
            'sql' => $query->sql,
            'bindings' => $query->bindings,
            'time' => $query->time
        ]);
    }
});
```