# Tests Specification

This is the tests specification for @.agent-os/specs/2025-08-04-performance-optimization/spec.md

> Created: 2025-08-04
> Version: 1.0.0

## Unit Tests

### Model Performance Tests
```php
// tests/Unit/InvoicePerformanceTest.php
class InvoicePerformanceTest extends TestCase
{
    public function test_invoice_model_loads_with_optimized_queries()
    {
        // Create test data
        $estate = Estate::factory()->create();
        $house = House::factory()->create(['estate_id' => $estate->id]);
        $invoices = Invoice::factory()->count(10)->create(['house_id' => $house->id]);
        
        // Test query count with eager loading
        $queryCount = 0;
        DB::listen(function () use (&$queryCount) {
            $queryCount++;
        });
        
        $loadedInvoices = Invoice::with(['house.estate', 'payments'])->get();
        
        // Should only execute 3 queries: invoices, houses, estates, payments
        $this->assertLessThanOrEqual(4, $queryCount);
        $this->assertCount(10, $loadedInvoices);
    }
    
    public function test_house_primary_contact_relationship()
    {
        $house = House::factory()->create();
        $contact = Contact::factory()->create([
            'house_id' => $house->id,
            'is_primary' => true
        ]);
        
        $queryCount = 0;
        DB::listen(function () use (&$queryCount) {
            $queryCount++;
        });
        
        $primaryContact = $house->primaryContact;
        
        // Should only execute 1 query for the relationship
        $this->assertEquals(1, $queryCount);
        $this->assertEquals($contact->id, $primaryContact->id);
    }
}
```

### Database Index Tests
```php
// tests/Unit/DatabaseIndexTest.php
class DatabaseIndexTest extends TestCase
{
    public function test_invoices_table_has_required_indexes()
    {
        $indexes = Schema::getIndexListing('invoices');
        
        $expectedIndexes = [
            'invoices_status_due_date_index',
            'invoices_submitted_at_index',
            'invoices_approved_at_index',
            'invoices_house_status_index'
        ];
        
        foreach ($expectedIndexes as $index) {
            $this->assertContains($index, $indexes);
        }
    }
    
    public function test_query_performance_with_indexes()
    {
        // Create test data
        $estate = Estate::factory()->create();
        $house = House::factory()->create(['estate_id' => $estate->id]);
        Invoice::factory()->count(100)->create([
            'house_id' => $house->id,
            'status' => 'pending'
        ]);
        
        // Test query performance
        $startTime = microtime(true);
        $invoices = Invoice::where('status', 'pending')
            ->where('due_date', '>', now())
            ->get();
        $endTime = microtime(true);
        
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        
        // Query should execute in under 100ms
        $this->assertLessThan(100, $executionTime);
        $this->assertGreaterThan(0, $invoices->count());
    }
}
```

## Integration Tests

### API Performance Tests
```php
// tests/Feature/ApiPerformanceTest.php
class ApiPerformanceTest extends TestCase
{
    public function test_estates_api_pagination()
    {
        // Create test data
        Estate::factory()->count(60)->create();
        
        $response = $this->getJson('/api/estates?per_page=50');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [['id', 'name', 'house_count', 'active_invoices']],
            'meta' => ['current_page', 'per_page', 'total', 'last_page']
        ]);
        
        // Check pagination metadata
        $this->assertEquals(1, $response->json('meta.current_page'));
        $this->assertEquals(50, $response->json('meta.per_page'));
        $this->assertEquals(60, $response->json('meta.total'));
        $this->assertEquals(2, $response->json('meta.last_page'));
    }
    
    public function test_invoices_api_with_eager_loading()
    {
        $estate = Estate::factory()->create();
        $house = House::factory()->create(['estate_id' => $estate->id]);
        $invoices = Invoice::factory()->count(20)->create(['house_id' => $house->id]);
        
        $queryCount = 0;
        DB::listen(function () use (&$queryCount) {
            $queryCount++;
        });
        
        $response = $this->getJson('/api/invoices?include=house,house.estate');
        
        // Should execute minimal queries due to eager loading
        $this->assertLessThanOrEqual(3, $queryCount);
        $response->assertStatus(200);
    }
    
    public function test_financial_reports_api_performance()
    {
        $estate = Estate::factory()->create();
        $house = House::factory()->create(['estate_id' => $estate->id]);
        Invoice::factory()->count(50)->create(['house_id' => $house->id]);
        
        $startTime = microtime(true);
        $response = $this->getJson('/api/reports/financial');
        $endTime = microtime(true);
        
        $executionTime = ($endTime - $startTime) * 1000;
        
        // Report should generate in under 500ms
        $this->assertLessThan(500, $executionTime);
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'summary',
                'by_status',
                'aging_report'
            ],
            'meta' => ['generated_at', 'query_time']
        ]);
    }
}
```

### Livewire Component Performance Tests
```php
// tests/Feature/Livewire/PerformanceTest.php
class LivewirePerformanceTest extends TestCase
{
    public function test_billing_manager_component_performance()
    {
        Estate::factory()->count(30)->create();
        
        $startTime = microtime(true);
        Livewire::test(BillingManager::class)
            ->assertStatus(200);
        $endTime = microtime(true);
        
        $executionTime = ($endTime - $startTime) * 1000;
        
        // Component should render in under 200ms
        $this->assertLessThan(200, $executionTime);
    }
    
    public function test_invoice_list_component_with_pagination()
    {
        $estate = Estate::factory()->create();
        $house = House::factory()->create(['estate_id' => $estate->id]);
        Invoice::factory()->count(75)->create(['house_id' => $house->id]);
        
        Livewire::test(InvoiceList::class)
            ->assertSet('perPage', 50)
            ->assertViewHas('invoices', function ($invoices) {
                return $invoices->count() === 50;
            });
    }
}
```

## Feature Tests

### Performance Benchmark Tests
```php
// tests/Feature/PerformanceBenchmarkTest.php
class PerformanceBenchmarkTest extends TestCase
{
    public function test_n_plus_one_queries_resolved()
    {
        // Create test data
        $estates = Estate::factory()->count(10)->create();
        foreach ($estates as $estate) {
            $houses = House::factory()->count(5)->create(['estate_id' => $estate->id]);
            foreach ($houses as $house) {
                Invoice::factory()->count(3)->create(['house_id' => $house->id]);
            }
        }
        
        // Test without eager loading (should be slow)
        $queryCount = 0;
        DB::listen(function () use (&$queryCount) {
            $queryCount++;
        });
        
        $invoices = Invoice::all();
        foreach ($invoices as $invoice) {
            $invoice->house->estate;
        }
        
        $queriesWithoutEager = $queryCount;
        
        // Test with eager loading (should be fast)
        $queryCount = 0;
        $invoices = Invoice::with(['house.estate'])->get();
        foreach ($invoices as $invoice) {
            $invoice->house->estate;
        }
        
        $queriesWithEager = $queryCount;
        
        // Eager loading should significantly reduce query count
        $this->assertLessThan($queriesWithoutEager / 10, $queriesWithEager);
    }
    
    public function test_database_index_performance_improvement()
    {
        // Create large dataset
        $estate = Estate::factory()->create();
        $house = House::factory()->create(['estate_id' => $estate->id]);
        Invoice::factory()->count(1000)->create(['house_id' => $house->id]);
        
        // Test query performance with indexed columns
        $startTime = microtime(true);
        $invoices = Invoice::where('status', 'pending')
            ->where('due_date', '>', now())
            ->orderBy('due_date')
            ->get();
        $indexedTime = microtime(true) - $startTime;
        
        // Test query performance with non-indexed columns (if any)
        $startTime = microtime(true);
        $invoices = Invoice::where('notes', 'like', '%test%')
            ->get();
        $nonIndexedTime = microtime(true) - $startTime;
        
        // Indexed queries should be significantly faster
        $this->assertLessThan($nonIndexedTime * 0.1, $indexedTime);
    }
}
```

## Mocking Requirements

### External Services Mocking
```php
// tests/Mock/QueryLoggerMock.php
class QueryLoggerMock
{
    public function __invoke()
    {
        return [
            'sql' => 'SELECT * FROM test_table',
            'bindings' => [],
            'time' => 10.5
        ];
    }
}

// Usage in tests
DB::listen(new QueryLoggerMock());
```

### Database Performance Mocking
```php
// tests/Mock/DatabasePerformanceMock.php
class DatabasePerformanceMock
{
    public function mockSlowQuery()
    {
        return [
            'execution_time' => 150.5,
            'rows_affected' => 1000,
            'index_used' => true
        ];
    }
}
```

## Test Data Generation
```php
// tests/Database/Seeders/PerformanceTestSeeder.php
class PerformanceTestSeeder extends Seeder
{
    public function run()
    {
        // Create realistic test data for performance testing
        $estates = Estate::factory()->count(20)->create();
        
        foreach ($estates as $estate) {
            $houses = House::factory()->count(25)->create(['estate_id' => $estate->id]);
            
            foreach ($houses as $house) {
                // Create contacts
                Contact::factory()->count(2)->create(['house_id' => $house->id]);
                
                // Create meter readings
                $readings = MeterReading::factory()->count(12)->create(['house_id' => $house->id]);
                
                // Create invoices
                Invoice::factory()->count(6)->create(['house_id' => $house->id]);
            }
        }
    }
}
```

## Test Execution Order
1. Run unit tests first (fastest)
2. Run integration tests
3. Run feature tests with performance benchmarks
4. Run database migration tests
5. Run API performance tests

## Performance Test Metrics
- Query execution time (should be < 100ms for common queries)
- Query count reduction (should be > 80% improvement for N+1 queries)
- Memory usage (should not increase significantly)
- API response time (should be < 500ms for standard endpoints)