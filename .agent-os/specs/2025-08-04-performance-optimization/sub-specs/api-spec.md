# API Specification

This is the API specification for @.agent-os/specs/2025-08-04-performance-optimization/spec.md

> Created: 2025-08-04
> Version: 1.0.0

## Performance Monitoring API

### Query Performance Endpoint
**GET** `/api/admin/query-performance`

**Description:** Retrieve query performance metrics for monitoring

**Response:**
```json
{
  "data": {
    "slow_queries": [
      {
        "sql": "SELECT * FROM invoices WHERE status = ?",
        "bindings": ["pending"],
        "time": 150.5,
        "timestamp": "2025-08-04T10:30:00Z"
      }
    ],
    "query_count": 1250,
    "average_time": 45.2,
    "slow_query_count": 3
  },
  "meta": {
    "timestamp": "2025-08-04T10:30:00Z"
  }
}
```

## Modified API Endpoints

### Estates Index (Paginated)
**GET** `/api/estates`

**Changes:** Added pagination parameters

**Parameters:**
- `page` (optional, default: 1) - Page number
- `per_page` (optional, default: 50, max: 100) - Items per page
- `search` (optional) - Search term for estate names

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Green Valley Estate",
      "house_count": 45,
      "active_invoices": 12
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 50,
    "total": 5,
    "last_page": 1
  }
}
```

### Invoices Index (Optimized)
**GET** `/api/invoices`

**Changes:** Added optimized filtering and pagination

**Parameters:**
- `page` (optional, default: 1) - Page number
- `per_page` (optional, default: 50, max: 100) - Items per page
- `status` (optional) - Filter by status (pending, approved, paid, overdue)
- `estate_id` (optional) - Filter by estate
- `from_date` (optional) - Filter by date range start
- `to_date` (optional) - Filter by date range end
- `include` (optional) - Relationships to include (house, house.estate, payments)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "invoice_number": "INV-2025-001",
      "status": "pending",
      "total_amount": 1500.00,
      "due_date": "2025-08-15",
      "house": {
        "id": 1,
        "house_number": "A101",
        "estate": {
          "id": 1,
          "name": "Green Valley Estate"
        }
      },
      "payments": []
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 50,
    "total": 125,
    "last_page": 3,
    "filters": {
      "status": "pending",
      "estate_id": null
    }
  }
}
```

### Houses Index (Optimized)
**GET** `/api/houses`

**Changes:** Added eager loading and pagination

**Parameters:**
- `page` (optional, default: 1) - Page number
- `per_page` (optional, default: 50, max: 100) - Items per page
- `estate_id` (optional) - Filter by estate
- `include` (optional) - Relationships to include (estate, primary_contact, current_reading)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "house_number": "A101",
      "estate_id": 1,
      "estate": {
        "id": 1,
        "name": "Green Valley Estate"
      },
      "primary_contact": {
        "id": 1,
        "name": "John Doe",
        "phone_number": "+254712345678"
      },
      "current_reading": {
        "id": 1,
        "reading": 1250,
        "reading_date": "2025-08-01"
      }
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 50,
    "total": 200,
    "last_page": 4
  }
}
```

## Financial Reports API (Optimized)
**GET** `/api/reports/financial`

**Changes:** Replaced in-memory operations with database aggregates

**Parameters:**
- `estate_id` (optional) - Filter by estate
- `from_date` (optional) - Report start date
- `to_date` (optional) - Report end date

**Response:**
```json
{
  "data": {
    "summary": {
      "total_invoices": 125,
      "total_amount": 187500.00,
      "total_paid": 165000.00,
      "total_outstanding": 22500.00
    },
    "by_status": {
      "pending": {
        "count": 15,
        "amount": 22500.00
      },
      "approved": {
        "count": 8,
        "amount": 12000.00
      },
      "paid": {
        "count": 102,
        "amount": 153000.00
      }
    },
    "aging_report": {
      "current": {
        "count": 45,
        "amount": 67500.00
      },
      "30_days": {
        "count": 12,
        "amount": 18000.00
      },
      "60_days": {
        "count": 8,
        "amount": 12000.00
      },
      "90_days": {
        "count": 5,
        "amount": 7500.00
      }
    }
  },
  "meta": {
    "generated_at": "2025-08-04T10:30:00Z",
    "query_time": 25.5
  }
}
```

## Error Handling

### Performance Error Responses
```json
{
  "error": {
    "code": "QUERY_TIMEOUT",
    "message": "Query execution exceeded timeout limit",
    "details": {
      "query": "SELECT * FROM invoices",
      "timeout": 5000
    }
  }
}
```

```json
{
  "error": {
    "code": "PAGINATION_INVALID",
    "message": "Invalid pagination parameters",
    "details": {
      "per_page": "Must be between 1 and 100",
      "page": "Must be a positive integer"
    }
  }
}
```

## Rate Limiting
- Performance monitoring endpoints: 60 requests per minute
- Report endpoints: 30 requests per minute
- Standard API endpoints: 120 requests per minute