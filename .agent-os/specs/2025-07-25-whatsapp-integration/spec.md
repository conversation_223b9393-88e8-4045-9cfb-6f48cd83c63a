# WhatsApp Integration Technical Specification

## Project Overview
Complete WhatsApp Business API integration for automated invoice delivery, payment reminders, customer support, and two-way communication between estate management and tenants.

## Technical Architecture

### Core Components
- **WhatsAppService**: Main service for sending messages and handling API interactions
- **WhatsAppWebhookController**: Handles incoming webhooks from WhatsApp
- **WhatsAppTemplates**: Manages message templates and variables
- **MeterReadingImageProcessor**: Processes tenant-submitted meter reading photos
- **WhatsAppAnalyticsService**: Tracks engagement and performance metrics

### Database Schema
- **WhatsAppMessage**: Stores all sent/received messages with status tracking
- **Enhanced relationships**: Links messages to houses, invoices, and estates

## Implementation Plan

### Phase 1: Core Infrastructure (Week 1-2)
1. **WhatsApp Business API Setup**
   - Configure API credentials and environment variables
   - Set up webhook endpoints and verification
   - Implement security measures (signature verification, rate limiting)

2. **Database Models**
   - Create WhatsAppMessage model with proper relationships
   - Add migration for message tracking
   - Implement status management (sent/delivered/read/failed)

3. **Base Service Classes**
   - Implement WhatsAppService for API interactions
   - Create WhatsAppTemplates for message template management
   - Set up error handling and retry mechanisms

### Phase 2: Outbound Messaging (Week 3-4)
1. **Invoice Delivery**
   - Implement automated invoice sending after generation
   - Add PDF attachment support
   - Create delivery confirmation tracking

2. **Payment Reminders**
   - Build reminder scheduling system
   - Implement customizable reminder timing
   - Add multi-language support

3. **Message Queue System**
   - Create queued jobs for reliable message delivery
   - Implement exponential backoff for retries
   - Add rate limiting per phone number

### Phase 3: Interactive Features (Week 5-6)
1. **Webhook Handling** ✅ **COMPLETED**
   - Implement comprehensive webhook controller ✅
   - Handle text, image, and interactive messages ✅
   - Add proper signature verification ✅
   - Add auto-replies for common keywords ✅
   - Implement meter reading submission via WhatsApp ✅
   - Create comprehensive test coverage ✅

2. **Two-way Communication**
   - Build chat flow handlers for common interactions
   - Implement meter reading submission via WhatsApp
   - Create customer support response system

3. **Media Processing**
   - Implement image download and processing
   - Add OCR for meter reading extraction
   - Create document upload management

### Phase 4: Analytics & Monitoring (Week 7-8)
1. **Analytics Service**
   - Build engagement tracking system
   - Create performance metrics dashboard
   - Implement delivery and response rate monitoring

2. **Admin Interface**
   - Add WhatsApp message logs to management dashboard
   - Create analytics views for estate managers
   - Implement message retry controls

## API Endpoints

### Webhook Endpoints
```
POST /webhooks/whatsapp - Handle incoming messages
GET /webhooks/whatsapp - Webhook verification
```

### Internal API
```php
// WhatsAppService methods
sendInvoice(House $house, Invoice $invoice): WhatsAppMessage
sendPaymentReminder(House $house, Invoice $invoice): WhatsAppMessage
sendPaymentConfirmation(House $house, Payment $payment): WhatsAppMessage
handleIncomingMessage(array $message): array
uploadMedia(string $filePath): string
```

## Message Templates

### Pre-approved Templates
1. **Invoice Delivery** - Personalized invoice with PDF attachment
2. **Payment Reminder** - Friendly reminder with payment options
3. **Overdue Notice** - Urgent notice with late fees
4. **Payment Confirmation** - Thank you message with receipt

### Interactive Templates
1. **Main Menu** - Navigation options for tenants
2. **Reading Submission** - Instructions for meter reading photos
3. **Payment Options** - Available payment methods

## Security Implementation

### Data Protection
- Phone number encryption at rest
- GDPR compliance with data deletion rights
- Consent tracking for messaging
- Role-based access to WhatsApp data

### API Security
- Webhook signature verification
- Rate limiting (100 messages/day per number)
- Input validation and sanitization
- Secure credential storage

## Testing Strategy

### Unit Tests
- Message template rendering
- Webhook signature verification
- Media processing pipeline
- Rate limiting logic

### Integration Tests
- Full message sending flow
- Webhook handling with real payloads
- Database transaction integrity
- Error handling and retries

### End-to-End Tests
- Complete tenant interaction flows
- Invoice delivery with attachments
- Payment reminder sequences
- Multi-language support

## Environment Configuration

### Required Environment Variables
```bash
WHATSAPP_BUSINESS_ACCOUNT_ID=
WHATSAPP_ACCESS_TOKEN=
WHATSAPP_PHONE_NUMBER_ID=
WHATSAPP_WEBHOOK_VERIFY_TOKEN=
WHATSAPP_APP_SECRET=
```

### Optional Configuration
```bash
WHATSAPP_RATE_LIMIT_PER_DAY=100
WHATSAPP_RETRY_ATTEMPTS=3
WHATSAPP_MEDIA_MAX_SIZE=********
```

## Deployment Checklist

### Pre-deployment
- [ ] WhatsApp Business API account approved
- [ ] Phone number verified and approved
- [ ] Message templates approved by WhatsApp
- [ ] Webhook URL configured and SSL enabled
- [ ] Environment variables set

### Post-deployment
- [ ] Webhook verification successful
- [ ] Test message delivery
- [ ] Verify media upload/download
- [ ] Test interactive flows
- [ ] Monitor error logs

## Monitoring & Alerts

### Key Metrics to Track
- Message delivery rate (>95% target)
- Response rate from tenants
- Payment conversion via WhatsApp
- Average response time
- Error rate (<1% target)

### Alert Conditions
- Failed message delivery >5%
- Webhook endpoint errors
- Rate limit approaching
- Media processing failures
- Database connection issues

## Future Enhancements
- Voice message support
- Advanced AI chatbot responses
- Group messaging for announcements
- Rich media interactive buttons
- Integration with other messaging platforms