# Database Schema Specification

This is the database schema specification for @.agent-os/specs/2025-08-04-comprehensive-billing-system/spec.md

> Created: 2025-08-04
> Version: 1.0.0

## New Tables

### house_accounts
```php
Schema::create('house_accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('house_id')->unique()->constrained()->onDelete('cascade');
    $table->decimal('current_balance', 10, 2)->default(0);
    $table->decimal('total_credit', 10, 2)->default(0);
    $table->decimal('total_debit', 10, 2)->default(0);
    $table->timestamp('last_transaction_date')->nullable();
    $table->timestamps();
    
    $table->index('house_id');
    $table->index('current_balance');
});
```

### account_transactions
```php
Schema::create('account_transactions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('house_account_id')->constrained()->onDelete('cascade');
    $table->string('transaction_type'); // invoice, payment, adjustment, credit_note
    $table->string('reference_type'); // Invoice, Payment, Adjustment
    $table->foreignId('reference_id'); // ID of referenced model
    $table->decimal('amount', 10, 2);
    $table->decimal('balance_before', 10, 2);
    $table->decimal('balance_after', 10, 2);
    $table->text('description')->nullable();
    $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
    $table->timestamps();
    
    $table->index(['house_account_id', 'created_at']);
    $table->index('transaction_type');
    $table->index('reference_type');
});
```

### payment_plans
```php
Schema::create('payment_plans', function (Blueprint $table) {
    $table->id();
    $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
    $table->decimal('total_amount', 10, 2);
    $table->integer('installments');
    $table->decimal('installment_amount', 10, 2);
    $table->date('start_date');
    $table->date('end_date');
    $table->enum('status', ['active', 'completed', 'defaulted'])->default('active');
    $table->text('notes')->nullable();
    $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
    $table->timestamps();
    
    $table->index(['invoice_id', 'status']);
    $table->index('start_date');
});
```

### payment_plan_installments
```php
Schema::create('payment_plan_installments', function (Blueprint $table) {
    $table->id();
    $table->foreignId('payment_plan_id')->constrained()->onDelete('cascade');
    $table->integer('installment_number');
    $table->decimal('amount', 10, 2);
    $table->date('due_date');
    $table->date('paid_date')->nullable();
    $table->enum('status', ['pending', 'paid', 'overdue'])->default('pending');
    $table->text('notes')->nullable();
    $table->timestamps();
    
    $table->index(['payment_plan_id', 'status']);
    $table->index('due_date');
});
```

## Table Modifications

### invoices table enhancements ✅ COMPLETED
```php
Schema::table('invoices', function (Blueprint $table) {
    // Note: SQLite doesn't support enum modifications easily, handled in model
    $table->foreignId('submitted_by')->nullable()->constrained('users')->onDelete('set null');
    $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
    $table->timestamp('submitted_at')->nullable();
    $table->timestamp('approved_at')->nullable();
    $table->integer('reminder_count')->default(0);
    $table->timestamp('last_reminder_at')->nullable();
    $table->date('disconnection_scheduled')->nullable();
    $table->decimal('previous_balance_brought_forward', 10, 2)->default(0);
    
    $table->index(['status', 'submitted_at']);
    $table->index(['status', 'approved_at']);
    $table->index('reminder_count');
});
```

### houses table enhancement ⏳ PENDING
```php
Schema::table('houses', function (Blueprint $table) {
    $table->decimal('opening_balance', 10, 2)->default(0)->after('status');
    $table->date('balance_as_of_date')->nullable()->after('opening_balance');
});
```

## Relationships and Constraints

### HouseAccount Model
```php
public function house(): BelongsTo
{
    return $this->belongsTo(House::class);
}

public function transactions(): HasMany
{
    return $this->hasMany(AccountTransaction::class);
}

public function getCurrentBalanceAttribute(): float
{
    return $this->current_balance;
}
```

### AccountTransaction Model
```php
public function houseAccount(): BelongsTo
{
    return $this->belongsTo(HouseAccount::class);
}

public function user(): BelongsTo
{
    return $this->belongsTo(User::class);
}

public function reference(): MorphTo
{
    return $this->morphTo();
}
```

### Enhanced Invoice Model
```php
public function submittedBy(): BelongsTo
{
    return $this->belongsTo(User::class, 'submitted_by');
}

public function approvedBy(): BelongsTo
{
    return $this->belongsTo(User::class, 'approved_by');
}

public function paymentPlan(): HasOne
{
    return $this->hasOne(PaymentPlan::class);
}
```

## Data Migration Strategy

### Phase 1: Create new tables ✅ COMPLETED
- ✅ Create house_accounts table
- ✅ Create account_transactions table
- ⏳ Create payment_plans and payment_plan_installments tables

### Phase 2: Modify existing tables 🔄 IN PROGRESS
- ✅ Add new columns to invoices table
- ⏳ Add opening balance columns to houses table

### Phase 3: Data migration ⏳ PENDING
- ⏳ Create house accounts for existing houses
- ⏳ Migrate existing invoice data to account transactions
- ⏳ Calculate and set opening balances
- ⏳ Backfill approval workflow data where applicable

### Phase 4: Index optimization ✅ COMPLETED
- ✅ Add performance indexes for frequently queried fields
- ✅ Add composite indexes for common query patterns