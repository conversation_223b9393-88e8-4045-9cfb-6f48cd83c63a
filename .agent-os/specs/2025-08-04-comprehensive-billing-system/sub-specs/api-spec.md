# API Specification

This is the API specification for @.agent-os/specs/2025-08-04-comprehensive-billing-system/spec.md

> Created: 2025-08-04
> Version: 1.1.0

## House Account Management APIs ✅ COMPLETED

### Get House Account Balance
**GET** `/api/houses/{houseId}/account`

**Response:**
```json
{
  "data": {
    "id": 1,
    "house_id": 1,
    "current_balance": 2500.00,
    "total_credit": 15000.00,
    "total_debit": 12500.00,
    "last_transaction_date": "2025-08-04T10:30:00Z",
    "created_at": "2025-08-01T00:00:00Z",
    "updated_at": "2025-08-04T10:30:00Z"
  }
}
```

### Get Account Transaction History
**GET** `/api/houses/{houseId}/transactions`

**Parameters:**
- `page` (integer, optional): Page number for pagination
- `per_page` (integer, optional): Items per page (default: 20)
- `date_from` (date, optional): Filter transactions from date
- `date_to` (date, optional): Filter transactions to date
- `type` (string, optional): Filter by transaction type

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "transaction_type": "invoice",
      "reference_type": "Invoice",
      "reference_id": 123,
      "amount": 1500.00,
      "balance_before": 1000.00,
      "balance_after": 2500.00,
      "description": "Invoice #INV-2025-08-001",
      "created_at": "2025-08-04T10:30:00Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 20,
    "total": 1
  }
}
```

### Get Account Statement
**GET** `/api/houses/{houseId}/statement`

**Parameters:**
- `date_from` (date, required): Statement start date
- `date_to` (date, required): Statement end date
- `format` (string, optional): Response format (json, pdf)

**Response:**
```json
{
  "data": {
    "house_id": 1,
    "period": {
      "start": "2025-08-01",
      "end": "2025-08-04"
    },
    "opening_balance": 1000.00,
    "closing_balance": 2500.00,
    "transactions": [...],
    "summary": {
      "total_invoices": 1500.00,
      "total_payments": 0.00,
      "total_adjustments": 0.00
    }
  }
}
```

## Billing Management APIs ✅ COMPLETED

### Bulk Generate Invoices
**POST** `/api/billing/generate-invoices`

**Request:**
```json
{
  "estate_id": 1,
  "billing_period": {
    "start": "2025-08-01",
    "end": "2025-08-31"
  },
  "include_previous_balance": true,
  "send_automatically": false
}
```

**Response:**
```json
{
  "data": {
    "batch_id": "batch_123456",
    "total_invoices": 50,
    "successful_invoices": 48,
    "failed_invoices": 2,
    "errors": [
      {
        "house_id": 25,
        "error": "No active water rate found"
      }
    ]
  }
}
```

### Submit Invoice for Approval
**POST** `/api/invoices/{invoiceId}/submit`

**Response:**
```json
{
  "data": {
    "id": 123,
    "status": "submitted",
    "submitted_at": "2025-08-04T10:30:00Z",
    "submitted_by": {
      "id": 1,
      "name": "John Doe"
    }
  }
}
```

### Approve Invoice
**POST** `/api/invoices/{invoiceId}/approve`

**Request:**
```json
{
  "notes": "Approved after review"
}
```

**Response:**
```json
{
  "data": {
    "id": 123,
    "status": "approved",
    "approved_at": "2025-08-04T11:00:00Z",
    "approved_by": {
      "id": 2,
      "name": "Jane Smith"
    }
  }
}
```

### Bulk Send Reminders
**POST** `/api/billing/send-reminders`

**Request:**
```json
{
  "estate_id": 1,
  "overdue_days": [7, 14, 30],
  "method": "whatsapp"
}
```

**Response:**
```json
{
  "data": {
    "total_recipients": 25,
    "successful_sends": 23,
    "failed_sends": 2,
    "sent_at": "2025-08-04T10:30:00Z"
  }
}
```

## Financial Reporting APIs ⏳ PENDING

### Get Aging Report
**GET** `/api/reports/aging`

**Parameters:**
- `estate_id` (integer, optional): Filter by estate
- `as_of_date` (date, optional): Report date (default: today)

**Response:**
```json
{
  "data": {
    "as_of_date": "2025-08-04",
    "summary": {
      "current": 45000.00,
      "days_1_30": 12000.00,
      "days_31_60": 8000.00,
      "days_61_90": 5000.00,
      "over_90": 3000.00,
      "total_outstanding": 73000.00
    },
    "estates": [
      {
        "id": 1,
        "name": "Estate A",
        "current": 25000.00,
        "days_1_30": 8000.00,
        "days_31_60": 5000.00,
        "days_61_90": 2000.00,
        "over_90": 1000.00,
        "total": 41000.00
      }
    ]
  }
}
```

### Get Revenue Report
**GET** `/api/reports/revenue`

**Parameters:**
- `period` (string, optional): monthly, quarterly, yearly (default: monthly)
- `year` (integer, optional): Report year (default: current year)
- `estate_id` (integer, optional): Filter by estate

**Response:**
```json
{
  "data": {
    "period": "monthly",
    "year": 2025,
    "summary": {
      "total_revenue": 125000.00,
      "total_invoices": 150,
      "average_invoice_amount": 833.33,
      "collection_rate": 0.85
    },
    "breakdown": [
      {
        "month": "August",
        "invoices_count": 50,
        "total_billed": 45000.00,
        "total_collected": 38250.00,
        "collection_rate": 0.85
      }
    ]
  }
}
```

### Create Payment Plan ⏳ PENDING
**POST** `/api/invoices/{invoiceId}/payment-plan`

**Request:**
```json
{
  "installments": 3,
  "start_date": "2025-08-15",
  "notes": "Customer requested payment plan"
}
```

**Response:**
```json
{
  "data": {
    "id": 1,
    "invoice_id": 123,
    "total_amount": 4500.00,
    "installments": 3,
    "installment_amount": 1500.00,
    "start_date": "2025-08-15",
    "end_date": "2025-10-15",
    "status": "active",
    "installments": [
      {
        "installment_number": 1,
        "amount": 1500.00,
        "due_date": "2025-08-15",
        "status": "pending"
      }
    ]
  }
}
```

## Implementation Status

### ✅ COMPLETED APIs
- House Account Management APIs (backend implemented, ready for endpoint creation)
- Billing Management APIs (backend services implemented, ready for endpoint creation)

### 🔄 IN PROGRESS APIs  
- Invoice Approval Workflow APIs (model and methods implemented, endpoint creation pending)

### ⏳ PENDING APIs
- Financial Reporting APIs (awaiting FinancialReportService implementation)
- Payment Plan APIs (awaiting PaymentPlan model implementation)

## Error Handling

### Standard Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The given data was invalid.",
    "details": {
      "estate_id": ["The estate_id field is required."]
    }
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR` (422): Request validation failed
- `NOT_FOUND` (404): Resource not found
- `UNAUTHORIZED` (401): Authentication required
- `FORBIDDEN` (403): Insufficient permissions
- `BUSINESS_RULE_VIOLATION` (422): Business rule violation
- `INTERNAL_ERROR` (500): Server internal error