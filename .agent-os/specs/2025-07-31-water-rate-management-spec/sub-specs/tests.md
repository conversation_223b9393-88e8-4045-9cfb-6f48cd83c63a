# Tests Specification

This is the tests specification for @.agent-os/specs/2025-07-31-water-rate-management-spec/spec.md

> Created: 2025-07-31
> Version: 1.0.0

## Unit Tests
- **Models:**
    - `WaterRate`: Test attribute casting, relationships (`estate`), and any custom methods (e.g., for checking effective date overlaps *per estate*).
- **Services:**
    - `WaterRateService` (new service if created for business logic): Test methods for creating, updating, and deleting water rates, including validation for effective dates and `estate_id` association.

## Integration Tests
- **Livewire Components:**
    - `WaterRateList` (or similar): Test data display, pagination, and filtering by estate.
    - `WaterRateForm` (or similar): Test form submission, validation (especially for effective dates *within an estate*), and successful creation/update.
- **Routes:**
    - Ensure authenticated users with appropriate roles can access water rate management routes, specifically within the context of an estate.

## Feature Tests
- **End-to-End Scenarios:**
    - As an Administrator, I can navigate to an Estate's detail view, access the Water Rate tab, view a list of rates for that estate, and create a new rate with a non-overlapping effective date for that estate.
    - As an Administrator, I can edit an existing water rate for a specific estate and verify the changes.
    - As an Administrator, I can attempt to create a water rate with an overlapping effective date for the *same estate* and verify that validation prevents it.
    - As an Administrator, I can attempt to create a water rate with an overlapping effective date for a *different estate* and verify that it is allowed.
    - As an Administrator, I can delete a water rate (if not in use by invoices) for a specific estate and verify its removal.

## Mocking Requirements
- **Date/Time:** Mock `now()` or `Carbon::now()` for consistent date-based testing, especially for effective dates.
</content>

## Mocking Requirements
- **Date/Time:** Mock `now()` or `Carbon::now()` for consistent date-based testing, especially for effective dates.
