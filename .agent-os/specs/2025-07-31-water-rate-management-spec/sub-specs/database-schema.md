# Database Schema

This is the database schema specification for @.agent-os/specs/2025-07-31-water-rate-management-spec/spec.md

> Created: 2025-07-31
> Version: 1.0.0

## Existing Models/Tables to Utilize
- `water_rates` table (Model: `App\Models\WaterRate`)
- `estates` table (Model: `App\Models\Estate`)

## Database Changes Needed
The `water_rates` table needs to be updated to include an `estate_id` column to associate water rates with specific estates.

**Migration (Example):**
```php
Schema::table('water_rates', function (Blueprint $table) {
    $table->foreignId('estate_id')->constrained()->onDelete('cascade');
});
```
This change ensures that each water rate is explicitly linked to an estate, allowing for estate-specific rate management.
</content>
