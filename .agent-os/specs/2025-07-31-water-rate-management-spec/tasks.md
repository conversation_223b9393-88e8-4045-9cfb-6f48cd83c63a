# Spec Tasks

> Created: 2025-07-31
> Status: Completed

## Tasks

- [x] 1. Database Migration for `estate_id`
  - [x] 1.1 Create a new migration to add `estate_id` to the `water_rates` table.
  - [x] 1.2 Run the migration.
  - [x] 1.3 Update `WaterRate` model to define `estate` relationship.
  - [x] 1.4 Verify database schema update.

- [x] 2. Implement Estate-Specific Water Rate Listing View
  - [x] 2.1 Write feature tests for `WaterRateList` component (data display, pagination, estate association).
  - [x] 2.2 Create `app/Livewire/Estate/WaterRateList.php` Livewire component.
  - [x] 2.3 Create `resources/views/livewire/estate/water-rate-list.blade.php` view.
  - [x] 2.4 Integrate `WaterRateList` component as a tab/section within the existing Estate detail view.
  - [x] 2.5 Implement pagination for water rates.
  - [x] 2.6 Ensure proper data display for each water rate entry.
  - [x] 2.7 Verify all tests pass.

- [x] 3. Implement Estate-Specific Water Rate Creation
  - [x] 3.1 Write feature tests for `WaterRateForm` component (submission, validation, estate association).
  - [x] 3.2 Create `app/Livewire/Estate/WaterRateForm.php` Livewire component (can be reused for edit).
  - [x] 3.3 Integrate `WaterRateForm` into the `WaterRateList` view (e.g., as a modal or inline form).
  - [x] 3.4 Implement form validation for effective date and rate per unit, ensuring no overlaps *within the same estate*.
  - [x] 3.5 Implement logic to create new water rates associated with the current estate.
  - [x] 3.6 Verify all tests pass.

- [x] 4. Implement Estate-Specific Water Rate Editing
  - [x] 4.1 Write feature tests for `WaterRateForm` component (editing existing rates).
  - [x] 4.2 Reuse `app/Livewire/Estate/WaterRateForm.php` for editing functionality.
  - [x] 4.3 Implement logic to populate the form with existing water rate data.
  - [x] 4.4 Implement logic to update existing water rates, with validation for effective dates.
  - [x] 4.5 Verify all tests pass.

- [x] 5. Implement Estate-Specific Water Rate Deletion
  - [x] 5.1 Write feature tests for water rate deletion.
  - [x] 5.2 Implement deletion logic within `WaterRateList` or a dedicated action.
  - [x] 5.3 Add confirmation step before deletion.
  - [x] 5.4 Implement safeguards to prevent deletion if the rate is in use by generated invoices.
  - [x] 5.5 Verify all tests pass.
