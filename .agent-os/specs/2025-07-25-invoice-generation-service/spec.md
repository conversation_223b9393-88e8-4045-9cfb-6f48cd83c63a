# Invoice Generation Service Specification

## Overview
Comprehensive invoice generation system that automatically creates water bills based on meter readings, applies tiered pricing, generates PDF invoices, and manages the complete billing lifecycle.

## Core Features

### 1. Automated Invoice Generation
- **Batch Processing**: Generate invoices for all houses in an estate
- **Individual Generation**: Create single invoice for specific house
- **Scheduled Generation**: Monthly automated billing cycles
- **Manual Override**: Allow manual invoice creation/adjustment
- **Pro-rated Billing**: Handle mid-cycle rate changes

### 2. Billing Calculations
- **Tiered Pricing**: Support complex water rate structures
- **Base Charges**: Fixed monthly/connection fees
- **Consumption Charges**: Variable rates based on usage tiers
- **Tax Calculations**: Apply relevant taxes and fees
- **Previous Balance**: Include outstanding amounts
- **Payments/Credits**: Apply payments and adjustments
- **Late Fees**: Calculate and apply late payment penalties

### 3. PDF Generation
- **Professional Templates**: Branded invoice templates
- **Multi-format Support**: PDF, HTML, and print-friendly versions
- **QR Codes**: Include payment QR codes
- **Barcodes**: Unique invoice identifiers
- **Digital Signatures**: Secure invoice validation
- **Watermarks**: Security and branding elements

### 4. Invoice Lifecycle Management
- **Draft Status**: Preview before finalization
- **Sent Status**: Track delivery confirmations
- **Paid Status**: Payment reconciliation
- **Overdue Status**: Automatic aging and follow-up
- **Dispute Handling**: Manage disputed invoices
- **Credit Notes**: Handle refunds and adjustments

## Technical Architecture

### Core Service Classes

#### 1. Invoice Generation Service ✅ **COMPLETED**
```php
// app/Services/InvoiceGenerationService.php
class InvoiceGenerationService
{
    public function generateForEstate(Estate $estate, Carbon $billingPeriod): InvoiceBatch
    public function generateForHouse(House $house, Carbon $billingPeriod): Invoice ✅
    public function calculateBill(House $house, $consumption, WaterRate $rate): BillingCalculation
    public function applyAdjustments(Invoice $invoice, array $adjustments): Invoice
}
```

#### 2. Billing Calculation Engine ✅ **COMPLETED**
```php
// app/Services/BillingCalculationService.php
class BillingCalculationService
{
    public function calculateConsumptionCharges($consumption, WaterRate $rate): array
    public function calculateBaseCharges(House $house, WaterRate $rate): float
    public function calculateTaxes(float $subtotal, array $taxRates): array
    public function calculateLateFees(Invoice $invoice): float
    public function applyProRatedCharges(Invoice $invoice, Carbon $rateChangeDate): Invoice
}
```

**Note**: Basic billing calculations are implemented within the Invoice model accessors and payment/adjustment methods. Enhanced billing operations are handled through BillingOperationService for bulk operations and complex calculations.

#### 3. PDF Generation Service ✅ **COMPLETED**
```php
// app/Services/PdfGenerationService.php
class PdfGenerationService
{
    public function generateInvoicePdf(Invoice $invoice): string ✅
    public function generateStatementPdf(House $house, array $transactions): string ✅ **COMPLETED**
    public function generateBatchPdf(array $invoices): string ✅ **COMPLETED**
    public function createPaymentQrCode(Invoice $invoice): string ✅ **COMPLETED**
    public function addDigitalSignature(string $pdfPath, Invoice $invoice): string ✅ **COMPLETED**
}
```

## Data Models

### Invoice Model Extensions
```php
// app/Models/Invoice.php
class Invoice extends Model
{
    // Status constants
    const STATUS_DRAFT = 'draft';
    const STATUS_SENT = 'sent';
    const STATUS_PAID = 'paid';
    const STATUS_OVERDUE = 'overdue';
    const STATUS_DISPUTED = 'disputed';
    const STATUS_CANCELLED = 'cancelled';
    
    // Relationships
    public function house()
    public function estate()
    public function meterReadings()
    public function waterRate()
    public function payments()
    public function adjustments()
    
    // Scopes
    public function scopeForPeriod($query, $startDate, $endDate)
    public function scopeOverdue($query)
    public function scopeUnpaid($query)
    public function scopeByEstate($query, $estateId)
    
    // Calculation methods
    public function calculateTotalDue()
    public function calculateOutstandingAmount()
    public function isOverdue()
    public function getDueDate()
    public function getAgingDays()
    public function getSubtotalAttribute() ✅ **COMPLETED**
    public function getBalanceDueAttribute() ✅ **COMPLETED**
    public function getTotalAmountAttribute() ✅ **COMPLETED**
    public function getPaidAmountAttribute() ✅ **COMPLETED**
    public function recordPayment() ✅ **COMPLETED**
    public function addAdjustment() ✅ **COMPLETED**
    public function generatePdf() ✅ **COMPLETED**
    public function regeneratePdf() ✅ **COMPLETED**
}
```

### Billing Line Items
```php
// app/Models/InvoiceLineItem.php
class InvoiceLineItem extends Model
{
    const TYPE_CONSUMPTION = 'consumption';
    const TYPE_BASE_CHARGE = 'base_charge';
    const TYPE_TAX = 'tax';
    const TYPE_LATE_FEE = 'late_fee';
    const TYPE_ADJUSTMENT = 'adjustment';
    const TYPE_PREVIOUS_BALANCE = 'previous_balance';
    
    public function invoice()
    public function getAmount()
    public function getDescription()
}
```

## Rate Structure Support

### Tiered Pricing Model
```php
// app/Services/RateCalculationService.php
class RateCalculationService
{
    public function calculateTieredRate($consumption, array $tiers): array
    {
        // Example tiers:
        // 0-1000 gallons: $0.50/gallon
        // 1001-5000 gallons: $0.75/gallon  
        // 5001+ gallons: $1.00/gallon
        
        $charges = [];
        $remaining = $consumption;
        
        foreach ($tiers as $tier) {
            if ($remaining <= 0) break;
            
            $tierConsumption = min($remaining, $tier['max'] - $tier['min']);
            $charges[] = [
                'tier' => $tier['name'],
                'consumption' => $tierConsumption,
                'rate' => $tier['rate'],
                'amount' => $tierConsumption * $tier['rate']
            ];
            
            $remaining -= $tierConsumption;
        }
        
        return $charges;
    }
}
```

### Complex Rate Structures
- **Seasonal Rates**: Different rates for dry/wet seasons
- **Time-of-use Rates**: Peak/off-peak pricing
- **Commercial vs Residential**: Different rate structures
- **Bulk Discounts**: Reduced rates for high-volume users
- **Conservation Incentives**: Lower rates for reduced usage

## PDF Template System ✅ **COMPLETED**

### Template Structure
```php
// resources/views/pdf/ ✅ **COMPLETED**
// ├── invoice.blade.php ✅ **COMPLETED**
// ├── statement.blade.php ✅ **COMPLETED**
// ├── aging-report.blade.php ✅ **COMPLETED**
// ├── revenue-report.blade.php ✅ **COMPLETED**
// └── templates/ (future)
//     ├── standard.blade.php
//     ├── detailed.blade.php
//     ├── summary.blade.php
//     └── custom/
//         ├── estate-1.blade.php
//         └── estate-2.blade.php

// Standard template includes: ✅ **COMPLETED**
// - Header with estate branding
// - Customer information
// - Usage summary with chart
// - Detailed charges breakdown
// - Payment information
// - Terms and conditions
// - QR code for online payment
// - Professional styling with Tailadmin design system
// - Multi-sheet Excel export support
```

### Dynamic Content
- **Usage Charts**: Bar/line charts showing usage trends
- **Comparative Data**: Usage vs previous period
- **Conservation Tips**: Personalized water-saving recommendations
- **Payment History**: Recent payments and credits
- **Contact Information**: Estate-specific contact details

## Billing Cycle Management

### Automated Scheduling
```php
// app/Console/Commands/GenerateMonthlyInvoices.php
class GenerateMonthlyInvoices extends Command
{
    protected $signature = 'invoices:generate-monthly {--estate= : Specific estate ID}';
    
    public function handle()
    {
        $estates = $this->getEstatesToProcess();
        
        foreach ($estates as $estate) {
            $billingPeriod = $this->getBillingPeriod($estate);
            
            try {
                $batch = $this->invoiceService->generateForEstate($estate, $billingPeriod);
                $this->notifyEstateManagers($estate, $batch);
            } catch (Exception $e) {
                $this->error("Failed to generate invoices for estate {$estate->id}: {$e->getMessage()}");
            }
        }
    }
}
```

### Pro-rated Billing
```php
// Handle rate changes mid-billing period
public function calculateProRatedCharges($consumption, $oldRate, $newRate, $changeDate, $billingPeriod)
{
    $daysInPeriod = $billingPeriod->days;
    $daysAtOldRate = $changeDate->diffInDays($billingPeriod->start);
    $daysAtNewRate = $daysInPeriod - $daysAtOldRate;
    
    $consumptionAtOldRate = ($consumption * $daysAtOldRate) / $daysInPeriod;
    $consumptionAtNewRate = $consumption - $consumptionAtOldRate;
    
    return [
        'old_rate_consumption' => $consumptionAtOldRate,
        'new_rate_consumption' => $consumptionAtNewRate,
        'old_rate_amount' => $consumptionAtOldRate * $oldRate->getRate(),
        'new_rate_amount' => $consumptionAtNewRate * $newRate->getRate(),
    ];
}
```

## Payment Integration

### Payment Methods
- **Bank Transfer**: ACH/wire transfer details
- **Mobile Money**: M-Pesa, Airtel Money integration
- **Online Payment**: Credit/debit card processing
- **Cash Payment**: Physical payment locations
- **Check Payment**: Traditional check processing

### QR Code Generation
```php
public function generatePaymentQrCode(Invoice $invoice): string
{
    $paymentData = [
        'invoice_number' => $invoice->invoice_number,
        'amount' => $invoice->total_due,
        'account_number' => $invoice->house->account_number,
        'estate_code' => $invoice->estate->code,
    ];
    
    return QrCode::format('png')
        ->size(200)
        ->generate(json_encode($paymentData));
}
```

## Error Handling & Recovery

### Validation Errors
- **Missing Readings**: Handle houses without current readings
- **Rate Not Found**: Default rate handling
- **Calculation Errors**: Graceful degradation with error logging
- **PDF Generation Failures**: Fallback to basic text invoices

### Retry Mechanisms
```php
class InvoiceGenerationJob implements ShouldQueue
{
    public $tries = 3;
    public $backoff = [60, 300, 900]; // 1min, 5min, 15min
    
    public function handle()
    {
        try {
            $this->invoiceService->generateForHouse($this->house, $this->period);
        } catch (RateNotFoundException $e) {
            $this->useDefaultRate($this->house);
        } catch (PdfGenerationException $e) {
            $this->generateTextInvoice($this->house, $this->period);
        }
    }
}
```

## Testing Strategy

### Unit Tests
- Test billing calculations for all rate structures
- Test PDF generation with various data scenarios
- Test error handling and edge cases
- Test pro-rated billing calculations

### Integration Tests
- Test full invoice generation workflow
- Test payment integration endpoints
- Test batch processing with large datasets
- Test email delivery and PDF attachments

### Performance Tests
- Test PDF generation speed for large invoices
- Test batch processing performance
- Test memory usage with many line items
- Test concurrent invoice generation

## Monitoring & Analytics

### Key Metrics
- **Generation Success Rate**: Percentage of successful generations
- **Processing Time**: Average time per invoice/batch
- **Error Rates**: Types and frequency of errors
- **PDF Quality**: Validation of generated PDFs
- **Payment Conversion**: Invoice-to-payment conversion rates

### Alert System
- **Generation Failures**: Immediate alerts for failed generations
- **Rate Issues**: Alerts for missing or invalid rates
- **Performance Degradation**: Monitoring for slow processing
- **Storage Issues**: Alerts for disk space or file system issues

## Security & Compliance

### Data Protection
- **Invoice Encryption**: Encrypt sensitive invoice data
- **Access Control**: Role-based access to invoice data
- **Audit Trail**: Track all invoice modifications
- **Data Retention**: Compliant data retention policies

### Financial Compliance
- **Tax Compliance**: Ensure accurate tax calculations
- **Audit Requirements**: Maintain audit trails
- **Regulatory Reporting**: Generate required reports
- **Currency Handling**: Multi-currency support if needed

## Future Enhancements
- **Smart Contracts**: Blockchain-based invoice verification
- **AI Optimization**: ML-based rate optimization
- **Real-time Billing**: IoT meter integration
- **Subscription Billing**: Recurring billing automation
- **Multi-language Support**: Localized invoice templates