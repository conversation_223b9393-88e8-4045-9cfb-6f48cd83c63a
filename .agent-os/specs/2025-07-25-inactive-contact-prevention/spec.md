# Inactive Contact Prevention Specification

## Overview
Prevent sending emails or WhatsApp messages to inactive contacts by implementing a comprehensive contact status management system that respects contact preferences and active status.

## Problem Statement
Currently, the system can send messages to contacts regardless of their active status, leading to:
- Messages sent to inactive/unreachable contacts
- Wasted API calls and resources
- Poor user experience for estate managers
- Potential compliance issues with contact preferences

## Technical Requirements

### 1. Contact Status Management
**Current State**: Contacts have `receive_invoices` and `receive_notifications` boolean flags
**Required Enhancement**: Add explicit `is_active` field and validation logic

### 2. Message Delivery Prevention
**Email Messages**: Prevent sending when `is_active = false` OR `receive_notifications = false`
**WhatsApp Messages**: Prevent sending when `is_active = false` OR `receive_notifications = false`
**Invoice Delivery**: Prevent sending when `is_active = false` OR `receive_invoices = false`

### 3. Validation Layer
Implement validation at multiple levels:
- Service layer validation before message creation
- Factory validation for test data
- API endpoint validation
- UI form validation

## Database Changes

### Contacts Table Enhancement
```sql
-- Add is_active column to contacts table
ALTER TABLE contacts ADD COLUMN is_active BOOLEAN DEFAULT true;

-- Add index for performance
CREATE INDEX idx_contacts_active ON contacts(is_active);
```

### WhatsApp Messages Enhancement
```sql
-- Add validation fields to whatsapp_messages
ALTER TABLE whatsapp_messages ADD COLUMN validation_passed BOOLEAN DEFAULT true;
ALTER TABLE whatsapp_messages ADD COLUMN validation_reason TEXT NULL;
```

## Model Updates

### Contact Model
```php
// Add to fillable
protected $fillable = [
    // ... existing fields
    'is_active',
];

// Add scopes
public function scopeActive($query)
{
    return $query->where('is_active', true);
}

public function scopeCanReceiveMessages($query)
{
    return $query->active()->where('receive_notifications', true);
}

public function scopeCanReceiveInvoices($query)
{
    return $query->active()->where('receive_invoices', true);
}

// Add validation method
public function canReceiveMessages(): bool
{
    return $this->is_active && $this->receive_notifications;
}

public function canReceiveInvoices(): bool
{
    return $this->is_active && $this->receive_invoices;
}
```

### WhatsAppMessage Model
```php
// Add validation before sending
public static function boot()
{
    parent::boot();
    
    static::creating(function ($message) {
        if (!$message->recipientContact->canReceiveMessages()) {
            $message->status = 'failed';
            $message->failed_reason = 'Contact is inactive or has disabled notifications';
            $message->validation_passed = false;
            return false; // Prevent creation
        }
    });
}
```

## Service Layer Updates

### WhatsAppService
```php
public function sendMessage(Contact $contact, string $message, array $options = []): ?WhatsAppMessage
{
    // Validate contact can receive messages
    if (!$contact->canReceiveMessages()) {
        Log::warning('Attempted to send WhatsApp to inactive contact', [
            'contact_id' => $contact->id,
            'reason' => $contact->is_active ? 'notifications disabled' : 'contact inactive'
        ]);
        return null;
    }
    
    // Proceed with message sending...
}
```

### EmailService
```php
public function sendInvoice(Contact $contact, Invoice $invoice): bool
{
    if (!$contact->canReceiveInvoices()) {
        Log::warning('Attempted to send invoice to inactive contact', [
            'contact_id' => $contact->id,
            'invoice_id' => $invoice->id
        ]);
        return false;
    }
    
    // Proceed with email sending...
}
```

## Factory Updates

### ContactFactory
```php
public function definition(): array
{
    return [
        // ... existing fields
        'is_active' => true,
        'receive_invoices' => true,
        'receive_notifications' => true,
    ];
}

public function inactive(): static
{
    return $this->state(fn (array $attributes) => [
        'is_active' => false,
    ]);
}

public function noNotifications(): static
{
    return $this->state(fn (array $attributes) => [
        'receive_notifications' => false,
    ]);
}

public function noInvoices(): static
{
    return $this->state(fn (array $attributes) => [
        'receive_invoices' => false,
    ]);
}
```

## Test Updates

### WhatsAppMessageTest
```php
public function test_cannot_send_to_inactive_contact()
{
    $contact = Contact::factory()->inactive()->create();
    
    $message = WhatsAppMessage::create([
        'recipient_contact_id' => $contact->id,
        'content' => 'Test message',
    ]);
    
    $this->assertNull($message);
    $this->assertDatabaseMissing('whatsapp_messages', [
        'recipient_contact_id' => $contact->id,
    ]);
}

public function test_cannot_send_to_contact_with_disabled_notifications()
{
    $contact = Contact::factory()->noNotifications()->create();
    
    $message = WhatsAppMessage::create([
        'recipient_contact_id' => $contact->id,
        'content' => 'Test message',
    ]);
    
    $this->assertNull($message);
}
```

## API Endpoints

### Validation Middleware
Create middleware to validate contact status before processing:
```php
class ValidateContactStatus
{
    public function handle($request, Closure $next)
    {
        $contactId = $request->input('contact_id');
        $contact = Contact::find($contactId);
        
        if (!$contact || !$contact->canReceiveMessages()) {
            return response()->json([
                'error' => 'Cannot send message to inactive contact'
            ], 422);
        }
        
        return $next($request);
    }
}
```

## UI/UX Updates

### Contact Management Interface
- Add toggle for `is_active` status
- Show visual indicators for inactive contacts
- Disable message buttons for inactive contacts
- Display warning when attempting to message inactive contacts

### Message Sending Interface
- Filter contact dropdowns to only show active contacts
- Show contact status in selection lists
- Disable send button with tooltip for inactive contacts

## Migration Strategy

### Phase 1: Database Migration
1. Add `is_active` column to contacts
2. Set all existing contacts to active
3. Update factories and seeders

### Phase 2: Service Layer Updates
1. Update WhatsAppService with validation
2. Update EmailService with validation
3. Add logging for blocked messages

### Phase 3: UI Updates
1. Update contact forms with active status toggle
2. Update message interfaces with filtering
3. Add visual indicators for contact status

### Phase 4: Testing
1. Update existing tests
2. Add new test cases for inactive contacts
3. Run full test suite

## Validation Checklist

### Database
- [ ] `is_active` column added to contacts table
- [ ] Indexes created for performance
- [ ] Default values set correctly

### Models
- [ ] Contact model updated with new scopes
- [ ] WhatsAppMessage model updated with validation
- [ ] Relationships properly defined

### Services
- [ ] WhatsAppService validates contact status
- [ ] EmailService validates contact status
- [ ] Proper logging implemented

### Factories
- [ ] ContactFactory updated with new fields
- [ ] Test states created for inactive contacts
- [ ] All tests updated to use correct fields

### UI
- [ ] Contact management interface updated
- [ ] Message interfaces filtered
- [ ] Visual indicators added

### Tests
- [ ] All existing tests pass
- [ ] New tests for inactive contact prevention
- [ ] Edge cases covered

## Rollback Plan
If issues arise:
1. Revert database migration
2. Restore previous factory/seed states
3. Disable new validation temporarily
4. Monitor logs for any issues

## Success Metrics
- Zero messages sent to inactive contacts
- Improved delivery rates
- Reduced API waste
- Better user feedback
