# Database Schema Specification

## Overview
Complete database schema for House & Contact Management system with optimized indexes, constraints, and relationships.

## Table Definitions

### 1. estates
**Purpose**: Store estate/compound information

```sql
CREATE TABLE estates (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL UNIQUE COMMENT 'Estate name, must be unique system-wide',
    address TEXT COMMENT 'Full physical address',
    gps_lat DECIMAL(10, 8) COMMENT 'GPS latitude coordinate',
    gps_lng DECIMAL(11, 8) COMMENT 'GPS longitude coordinate',
    contact_email VARCHAR(255) COMMENT 'Primary estate office email',
    contact_phone VARCHAR(20) COMMENT 'Primary estate office phone',
    settings JSON COMMENT 'JSON configuration for estate-specific settings',
    logo_path VARCHAR(500) COMMENT 'Path to estate logo image',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether estate is active or archived',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_name (name),
    INDEX idx_active (is_active),
    INDEX idx_coordinates (gps_lat, gps_lng),
    INDEX idx_contact_email (contact_email),
    INDEX idx_contact_phone (contact_phone)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. houses
**Purpose**: Store individual house/unit information

```sql
CREATE TABLE houses (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    estate_id BIGINT UNSIGNED NOT NULL COMMENT 'Foreign key to estates table',
    house_number VARCHAR(20) NOT NULL COMMENT 'House/unit number within estate',
    block VARCHAR(50) COMMENT 'Block identifier for large estates',
    floor VARCHAR(20) COMMENT 'Floor number for multi-story buildings',
    type ENUM('residential', 'commercial') DEFAULT 'residential' COMMENT 'Type of house',
    meter_number VARCHAR(50) NOT NULL COMMENT 'Unique water meter identifier',
    initial_reading DECIMAL(10, 2) DEFAULT 0 COMMENT 'Initial meter reading value',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT 'Current house status',
    photos JSON COMMENT 'Array of photo file paths',
    notes TEXT COMMENT 'Additional notes and remarks',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (estate_id) REFERENCES estates(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY unique_house_per_estate (estate_id, house_number),
    UNIQUE KEY unique_meter_number (meter_number),
    
    -- Indexes
    INDEX idx_estate_id (estate_id),
    INDEX idx_house_number (house_number),
    INDEX idx_block (block),
    INDEX idx_floor (floor),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_meter_number (meter_number),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3. contacts
**Purpose**: Store contact information for all people associated with houses

```sql
CREATE TABLE contacts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'Full contact name',
    email VARCHAR(255) COMMENT 'Email address',
    phone VARCHAR(20) COMMENT 'Primary phone number',
    whatsapp_number VARCHAR(20) COMMENT 'WhatsApp number (can be same as phone)',
    communication_preference ENUM('email', 'whatsapp', 'both') DEFAULT 'both' COMMENT 'Preferred communication method',
    language_preference ENUM('english', 'swahili') DEFAULT 'english' COMMENT 'Preferred language',
    opt_out_marketing BOOLEAN DEFAULT FALSE COMMENT 'Opt-out flag for marketing messages',
    notes TEXT COMMENT 'Additional contact notes',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_name (name),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_whatsapp (whatsapp_number),
    INDEX idx_communication_pref (communication_preference),
    INDEX idx_language_pref (language_preference),
    INDEX idx_opt_out (opt_out_marketing),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 4. house_contacts (Pivot Table)
**Purpose**: Many-to-many relationship between houses and contacts with additional attributes

```sql
CREATE TABLE house_contacts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    house_id BIGINT UNSIGNED NOT NULL COMMENT 'Foreign key to houses table',
    contact_id BIGINT UNSIGNED NOT NULL COMMENT 'Foreign key to contacts table',
    contact_type ENUM('owner', 'tenant', 'caretaker', 'emergency') NOT NULL COMMENT 'Type of contact relationship',
    is_primary BOOLEAN DEFAULT FALSE COMMENT 'Whether this is the primary contact for the house',
    move_in_date DATE COMMENT 'Date when contact moved in/started relationship',
    move_out_date DATE COMMENT 'Date when contact moved out/ended relationship',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (house_id) REFERENCES houses(id) ON DELETE CASCADE,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY unique_house_contact_type (house_id, contact_id, contact_type),
    
    -- Indexes
    INDEX idx_house_id (house_id),
    INDEX idx_contact_id (contact_id),
    INDEX idx_contact_type (contact_type),
    INDEX idx_is_primary (is_primary),
    INDEX idx_move_in_date (move_in_date),
    INDEX idx_move_out_date (move_out_date),
    INDEX idx_house_type (house_id, contact_type),
    INDEX idx_contact_type_rel (contact_id, contact_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 5. audit_logs
**Purpose**: Track all changes to houses and contacts for audit trail

```sql
CREATE TABLE audit_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL COMMENT 'Name of the table being audited',
    record_id BIGINT UNSIGNED NOT NULL COMMENT 'ID of the record being changed',
    action ENUM('create', 'update', 'delete') NOT NULL COMMENT 'Type of action performed',
    old_values JSON COMMENT 'Previous values before change',
    new_values JSON COMMENT 'New values after change',
    user_id BIGINT UNSIGNED COMMENT 'User who made the change',
    ip_address VARCHAR(45) COMMENT 'IP address of the user',
    user_agent TEXT COMMENT 'User agent string',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_action (action),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Indexes Optimization

### Composite Indexes for Common Queries
```sql
-- For house searches by estate and status
CREATE INDEX idx_estate_status ON houses(estate_id, status);

-- For house searches by type and status
CREATE INDEX idx_type_status ON houses(type, status);

-- For contact searches by house and type
CREATE INDEX idx_house_contact_type ON house_contacts(house_id, contact_type, is_primary);

-- For finding active primary contacts
CREATE INDEX idx_active_primary ON house_contacts(house_id, contact_type, is_primary) 
WHERE move_out_date IS NULL;
```

### Full-Text Search Indexes
```sql
-- For house number and meter number searches
ALTER TABLE houses ADD FULLTEXT ft_house_search (house_number, meter_number, block, floor);

-- For contact name and email searches
ALTER TABLE contacts ADD FULLTEXT ft_contact_search (name, email, phone, whatsapp_number);
```

## Data Integrity Rules

### Check Constraints
```sql
-- Ensure GPS coordinates are within valid ranges
ALTER TABLE estates 
ADD CONSTRAINT chk_gps_lat CHECK (gps_lat BETWEEN -90 AND 90),
ADD CONSTRAINT chk_gps_lng CHECK (gps_lng BETWEEN -180 AND 180);

-- Ensure phone numbers contain only digits
ALTER TABLE contacts 
ADD CONSTRAINT chk_phone_format CHECK (phone REGEXP '^[0-9]{10,15}$'),
ADD CONSTRAINT chk_whatsapp_format CHECK (whatsapp_number REGEXP '^[0-9]{10,15}$');

-- Ensure initial reading is non-negative
ALTER TABLE houses 
ADD CONSTRAINT chk_initial_reading CHECK (initial_reading >= 0);
```

### Triggers for Audit Logging
```sql
DELIMITER //

-- Trigger for houses table
CREATE TRIGGER tr_houses_insert AFTER INSERT ON houses
FOR EACH ROW
BEGIN
    INSERT INTO audit_logs (table_name, record_id, action, new_values, created_at)
    VALUES ('houses', NEW.id, 'create', JSON_OBJECT(
        'estate_id', NEW.estate_id,
        'house_number', NEW.house_number,
        'meter_number', NEW.meter_number,
        'status', NEW.status
    ), NOW());
END//

CREATE TRIGGER tr_houses_update AFTER UPDATE ON houses
FOR EACH ROW
BEGIN
    INSERT INTO audit_logs (table_name, record_id, action, old_values, new_values, created_at)
    VALUES ('houses', NEW.id, 'update', JSON_OBJECT(
        'house_number', OLD.house_number,
        'meter_number', OLD.meter_number,
        'status', OLD.status
    ), JSON_OBJECT(
        'house_number', NEW.house_number,
        'meter_number', NEW.meter_number,
        'status', NEW.status
    ), NOW());
END//

-- Trigger for contacts table
CREATE TRIGGER tr_contacts_insert AFTER INSERT ON contacts
FOR EACH ROW
BEGIN
    INSERT INTO audit_logs (table_name, record_id, action, new_values, created_at)
    VALUES ('contacts', NEW.id, 'create', JSON_OBJECT(
        'name', NEW.name,
        'email', NEW.email,
        'phone', NEW.phone
    ), NOW());
END//

DELIMITER ;
```

## Migration Files Structure

### Migration Order
1. `2024_01_01_000003_create_estates_table.php`
2. `2024_01_01_000004_create_houses_table.php`
3. `2024_01_01_000005_create_contacts_table.php`
4. `2024_01_01_000005_create_house_contacts_table.php`
5. `2024_01_01_000006_create_audit_logs_table.php`

### Sample Migration File
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('houses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estate_id')->constrained()->cascadeOnDelete();
            $table->string('house_number', 20);
            $table->string('block', 50)->nullable();
            $table->string('floor', 20)->nullable();
            $table->enum('type', ['residential', 'commercial'])->default('residential');
            $table->string('meter_number', 50)->unique();
            $table->decimal('initial_reading', 10, 2)->default(0);
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->json('photos')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->unique(['estate_id', 'house_number']);
            $table->index(['estate_id', 'status']);
            $table->index('meter_number');
            $table->fullText(['house_number', 'block', 'meter_number']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('houses');
    }
};
```

## Performance Considerations

### Partitioning Strategy
For large datasets, consider partitioning:
- **houses table**: Partition by estate_id for large estates
- **audit_logs table**: Partition by created_at (monthly)

### Caching Strategy
- **Estate lists**: Cache for 1 hour
- **House counts**: Cache per estate for 30 minutes
- **Contact searches**: Cache popular queries for 15 minutes

### Query Optimization
- Use **eager loading** to prevent N+1 queries
- Implement **cursor pagination** for large datasets
- Use **database views** for complex reporting queries

## Backup & Recovery

### Backup Strategy
- **Daily backups**: Full database backup
- **Hourly backups**: Audit logs table only
- **Real-time replication**: For critical production systems

### Recovery Procedures
- **Point-in-time recovery**: Using binary logs
- **Selective restore**: Individual estate or house data
- **Audit trail recovery**: Restore from audit_logs table