# Technical Implementation Details

## Database Schema Design

### Core Tables Structure

#### estates table
```sql
CREATE TABLE estates (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    address TEXT,
    gps_lat DECIMAL(10, 8),
    gps_lng DECIMAL(11, 8),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    settings JSON,
    logo_path VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_active (is_active),
    INDEX idx_coordinates (gps_lat, gps_lng)
);
```

#### houses table
```sql
CREATE TABLE houses (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    estate_id BIGINT UNSIGNED NOT NULL,
    house_number VARCHAR(20) NOT NULL,
    block VARCHAR(50),
    floor VARCHAR(20),
    type ENUM('residential', 'commercial') DEFAULT 'residential',
    meter_number VARCHAR(50) NOT NULL UNIQUE,
    initial_reading DECIMAL(10, 2) DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    photos JSON,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (estate_id) REFERENCES estates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_house_per_estate (estate_id, house_number),
    INDEX idx_house_number (house_number),
    INDEX idx_meter_number (meter_number),
    INDEX idx_status (status),
    INDEX idx_type (type)
);
```

#### contacts table
```sql
CREATE TABLE contacts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    whatsapp_number VARCHAR(20),
    communication_preference ENUM('email', 'whatsapp', 'both') DEFAULT 'both',
    language_preference ENUM('english', 'swahili') DEFAULT 'english',
    opt_out_marketing BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_whatsapp (whatsapp_number)
);
```

#### house_contacts pivot table
```sql
CREATE TABLE house_contacts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    house_id BIGINT UNSIGNED NOT NULL,
    contact_id BIGINT UNSIGNED NOT NULL,
    contact_type ENUM('owner', 'tenant', 'caretaker', 'emergency') NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    move_in_date DATE,
    move_out_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (house_id) REFERENCES houses(id) ON DELETE CASCADE,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_house_contact_type (house_id, contact_id, contact_type),
    INDEX idx_house_type (house_id, contact_type),
    INDEX idx_contact_type (contact_id, contact_type),
    INDEX idx_primary (house_id, is_primary)
);
```

### Model Relationships

#### Estate Model
```php
class Estate extends Model
{
    protected $fillable = [
        'name', 'address', 'gps_lat', 'gps_lng', 
        'contact_email', 'contact_phone', 'settings', 'logo_path', 'is_active'
    ];
    
    protected $casts = [
        'settings' => 'array',
        'gps_lat' => 'decimal:8',
        'gps_lng' => 'decimal:8',
        'is_active' => 'boolean'
    ];
    
    public function houses(): HasMany
    {
        return $this->hasMany(House::class);
    }
    
    public function activeHouses(): HasMany
    {
        return $this->hasMany(House::class)->where('status', 'active');
    }
}
```

#### House Model
```php
class House extends Model
{
    protected $fillable = [
        'estate_id', 'house_number', 'block', 'floor', 
        'type', 'meter_number', 'initial_reading', 'status', 'photos', 'notes'
    ];
    
    protected $casts = [
        'initial_reading' => 'decimal:2',
        'photos' => 'array',
        'status' => 'string'
    ];
    
    public function estate(): BelongsTo
    {
        return $this->belongsTo(Estate::class);
    }
    
    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class, 'house_contacts')
                   ->withPivot('contact_type', 'is_primary', 'move_in_date', 'move_out_date')
                   ->withTimestamps();
    }
    
    public function primaryContact(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class, 'house_contacts')
                   ->wherePivot('is_primary', true)
                   ->wherePivot('contact_type', 'owner');
    }
}
```

#### Contact Model
```php
class Contact extends Model
{
    protected $fillable = [
        'name', 'email', 'phone', 'whatsapp_number',
        'communication_preference', 'language_preference',
        'opt_out_marketing', 'notes'
    ];
    
    protected $casts = [
        'opt_out_marketing' => 'boolean'
    ];
    
    public function houses(): BelongsToMany
    {
        return $this->belongsToMany(House::class, 'house_contacts')
                   ->withPivot('contact_type', 'is_primary', 'move_in_date', 'move_out_date')
                   ->withTimestamps();
    }
}
```

## API Endpoints

### Estate Endpoints
```php
// GET /api/estates
Route::get('/estates', [EstateController::class, 'index']);

// POST /api/estates
Route::post('/estates', [EstateController::class, 'store']);

// GET /api/estates/{id}
Route::get('/estates/{estate}', [EstateController::class, 'show']);

// PUT /api/estates/{id}
Route::put('/estates/{estate}', [EstateController::class, 'update']);

// DELETE /api/estates/{id}
Route::delete('/estates/{estate}', [EstateController::class, 'destroy']);
```

### House Endpoints
```php
// GET /api/houses?estate_id=1&search=A101
Route::get('/houses', [HouseController::class, 'index']);

// POST /api/houses
Route::post('/houses', [HouseController::class, 'store']);

// GET /api/houses/{id}
Route::get('/houses/{house}', [HouseController::class, 'show']);

// PUT /api/houses/{id}
Route::put('/houses/{house}', [HouseController::class, 'update']);

// POST /api/houses/import
Route::post('/houses/import', [HouseController::class, 'import']);

// GET /api/houses/export
Route::get('/houses/export', [HouseController::class, 'export']);
```

### Contact Endpoints
```php
// GET /api/contacts?house_id=1&type=owner
Route::get('/contacts', [ContactController::class, 'index']);

// POST /api/contacts
Route::post('/contacts', [ContactController::class, 'store']);

// PUT /api/contacts/{id}
Route::put('/contacts/{contact}', [ContactController::class, 'update']);

// POST /api/contacts/import
Route::post('/contacts/import', [ContactController::class, 'import']);
```

## Livewire Components Architecture

### Estate Management Components
```php
// EstateManager.php - Main estate CRUD
class EstateManager extends Component
{
    public $estates;
    public $showModal = false;
    public $editingEstate = null;
    
    public function render()
    {
        return view('livewire.estate-manager');
    }
}

// EstateForm.php - Create/edit estate
class EstateForm extends Component
{
    public Estate $estate;
    public $name, $address, $contact_email, $contact_phone;
    
    protected function rules()
    {
        return [
            'name' => 'required|unique:estates,name,' . $this->estate->id,
            'address' => 'required',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|regex:/^[0-9]{10,15}$/'
        ];
    }
}
```

### House Management Components
```php
// HouseRegistry.php - Main house management
class HouseRegistry extends Component
{
    use WithPagination;
    
    public $estateId;
    public $search = '';
    public $filters = [];
    public $sortField = 'house_number';
    public $sortDirection = 'asc';
    
    public function render()
    {
        $houses = House::query()
            ->when($this->estateId, fn($q) => $q->where('estate_id', $this->estateId))
            ->when($this->search, fn($q) => $q->where('house_number', 'like', "%{$this->search}%"))
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(50);
            
        return view('livewire.house-registry', compact('houses'));
    }
}

// HouseForm.php - Create/edit house
class HouseForm extends Component
{
    public House $house;
    public $estate_id, $house_number, $block, $floor, $type, $meter_number, $initial_reading;
    
    protected function rules()
    {
        return [
            'estate_id' => 'required|exists:estates,id',
            'house_number' => 'required|unique:houses,house_number,' . $this->house->id . ',id,estate_id,' . $this->estate_id,
            'meter_number' => 'required|unique:houses,meter_number,' . $this->house->id,
            'type' => 'required|in:residential,commercial',
            'initial_reading' => 'required|numeric|min:0'
        ];
    }
}

// HouseImport.php - CSV import component
class HouseImport extends Component
{
    public $file;
    public $preview = [];
    public $importResults = null;
    
    public function processFile()
    {
        $this->validate(['file' => 'required|mimes:csv,txt']);
        
        $data = array_map('str_getcsv', file($this->file->getRealPath()));
        $headers = array_shift($data);
        
        // Process and validate data
        $this->preview = $this->validateImportData($data, $headers);
    }
}
```

### Contact Management Components
```php
// ContactManager.php - Main contact interface
class ContactManager extends Component
{
    public $houseId;
    public $contacts;
    public $showModal = false;
    
    public function render()
    {
        $this->contacts = Contact::whereHas('houses', function($q) {
            $q->where('house_id', $this->houseId);
        })->get();
        
        return view('livewire.contact-manager');
    }
}

// ContactForm.php - Add/edit contact
class ContactForm extends Component
{
    public Contact $contact;
    public $houseId;
    public $contactType = 'owner';
    public $isPrimary = false;
    
    protected function rules()
    {
        return [
            'contact.name' => 'required',
            'contact.email' => 'nullable|email',
            'contact.phone' => 'required|regex:/^[0-9]{10,15}$/',
            'contact.whatsapp_number' => 'nullable|regex:/^[0-9]{10,15}$/',
            'contactType' => 'required|in:owner,tenant,caretaker,emergency',
            'isPrimary' => 'boolean'
        ];
    }
}
```

## Search Implementation

### Full-Text Search Setup
```sql
-- Add full-text indexes
ALTER TABLE houses ADD FULLTEXT idx_house_search (house_number, block, meter_number);
ALTER TABLE contacts ADD FULLTEXT idx_contact_search (name, email, phone);
```

### Search Service
```php
class HouseSearchService
{
    public function search(array $filters): Builder
    {
        $query = House::query()->with(['estate', 'contacts']);
        
        if (!empty($filters['search'])) {
            $query->where(function($q) use ($filters) {
                $q->where('house_number', 'like', "%{$filters['search']}%")
                  ->orWhere('meter_number', 'like', "%{$filters['search']}%")
                  ->orWhereHas('contacts', function($q) use ($filters) {
                      $q->where('name', 'like', "%{$filters['search']}%")
                        ->orWhere('phone', 'like', "%{$filters['search']}%");
                  });
            });
        }
        
        if (!empty($filters['estate_id'])) {
            $query->where('estate_id', $filters['estate_id']);
        }
        
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }
        
        return $query;
    }
}
```

## Validation Rules

### House Validation
```php
class HouseRequest extends FormRequest
{
    public function rules()
    {
        return [
            'estate_id' => 'required|exists:estates,id',
            'house_number' => [
                'required',
                'string',
                'max:20',
                Rule::unique('houses')->where(function ($query) {
                    return $query->where('estate_id', $this->estate_id);
                })->ignore($this->house)
            ],
            'meter_number' => 'required|string|max:50|unique:houses,meter_number,' . $this->house?->id,
            'block' => 'nullable|string|max:50',
            'floor' => 'nullable|string|max:20',
            'type' => 'required|in:residential,commercial',
            'initial_reading' => 'required|numeric|min:0',
            'status' => 'required|in:active,inactive'
        ];
    }
}
```

### Contact Validation
```php
class ContactRequest extends FormRequest
{
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'required|string|regex:/^[0-9]{10,15}$/',
            'whatsapp_number' => 'nullable|string|regex:/^[0-9]{10,15}$/',
            'communication_preference' => 'required|in:email,whatsapp,both',
            'language_preference' => 'required|in:english,swahili',
            'opt_out_marketing' => 'boolean'
        ];
    }
}
```