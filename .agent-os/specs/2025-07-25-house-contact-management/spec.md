# House & Contact Management - Technical Specification

## Project Overview
Complete technical implementation guide for managing estates, houses, and resident contact information in the water management system.

## Technical Architecture

### Database Schema
- **Primary Tables**: estates, houses, contacts, contact_types, house_contacts
- **Relationships**: One-to-many (estate→houses), many-to-many (houses→contacts)
- **Indexes**: Optimized for search by house_number, meter_number, contact details

### Core Components

#### 1. Estate Management Module
**Models & Relationships**
- `Estate` model with properties: name, address, gps_coordinates, contact_info, settings
- HasMany relationship with `House` model
- Settings stored as JSON for flexibility

**API Endpoints**
- `GET /api/estates` - List all estates
- `POST /api/estates` - Create new estate
- `PUT /api/estates/{id}` - Update estate
- `DELETE /api/estates/{id}` - Archive estate

**Livewire Components**
- `EstateManager` - Main estate CRUD interface
- `EstateSelector` - Dropdown for estate selection
- `EstateSettings` - Configuration management

#### 2. House Registry Module
**Models & Relationships**
- `House` model with: house_number, block, floor, type, meter_number, status
- BelongsTo relationship with `Estate`
- HasMany relationship with `Contact` through pivot table

**Validation Rules**
- house_number: required, unique per estate, max 20 chars
- meter_number: required, globally unique, max 50 chars
- type: enum ['residential', 'commercial']
- status: enum ['active', 'inactive']

**Livewire Components**
- `HouseRegistry` - Main house management interface
- `HouseForm` - Create/edit house form
- `HouseImport` - CSV import functionality
- `HouseSearch` - Advanced search interface

#### 3. Contact Management Module
**Models & Relationships**
- `Contact` model with: name, email, phone, whatsapp_number, preferences
- Many-to-many with `House` through `house_contacts` pivot
- Contact type stored in pivot: owner, tenant, caretaker, emergency

**Contact Types**
- owner: Primary property owner
- tenant: Current occupant
- caretaker: Estate-assigned caretaker
- emergency: Emergency contact

**Livewire Components**
- `ContactManager` - Main contact interface
- `ContactForm` - Add/edit contact form
- `ContactImport` - CSV contact import
- `ContactMerge` - Duplicate resolution tool

### Search & Filtering Implementation

#### Database Indexes
```sql
-- For house searches
CREATE INDEX idx_house_number ON houses(house_number);
CREATE INDEX idx_meter_number ON houses(meter_number);
CREATE INDEX idx_house_status ON houses(status);
CREATE INDEX idx_house_estate ON houses(estate_id);

-- For contact searches
CREATE INDEX idx_contact_name ON contacts(name);
CREATE INDEX idx_contact_phone ON contacts(phone);
CREATE INDEX idx_contact_email ON contacts(email);
```

#### Search Strategy
- **Full-text search** on house numbers, contact names
- **Exact match** for meter numbers, phone numbers
- **Fuzzy search** for name variations
- **Date range** filtering for last updated

### Import/Export System

#### CSV Import Process
1. **Validation Phase**: Check headers, data types, duplicates
2. **Preview Phase**: Show import preview with errors
3. **Import Phase**: Batch insert with transaction rollback
4. **Report Phase**: Success/failure summary

#### Export Formats
- **CSV**: Standard comma-separated values
- **Excel**: Formatted with headers and styling
- **JSON**: API-friendly format
- **PDF**: Printable reports

### Security Implementation

#### Role-Based Access Control
```php
// Policy definitions
EstatePolicy::viewAny(User $user) - management, reviewer
EstatePolicy::update(User $user, Estate $estate) - management only
HousePolicy::create(User $user) - management, caretaker (assigned estates)
ContactPolicy::update(User $user, Contact $contact) - management, reviewer
```

#### Data Validation
- **Server-side**: Laravel validation rules
- **Client-side**: Livewire real-time validation
- **Database**: Foreign key constraints, unique constraints
- **API**: Rate limiting, input sanitization

### Performance Optimization

#### Caching Strategy
- **Estate lists**: Cache for 1 hour
- **House counts**: Cache per estate for 30 minutes
- **Contact searches**: Cache popular queries for 15 minutes
- **Import results**: Cache for 24 hours

#### Pagination
- **House lists**: 50 per page
- **Contact lists**: 100 per page
- **Search results**: 25 per page with infinite scroll

#### Database Optimization
- **Eager loading**: Load contacts with houses
- **Chunking**: Process large imports in batches
- **Indexing**: Strategic indexes for common queries
- **Query optimization**: Avoid N+1 problems

### Error Handling & Recovery

#### Validation Errors
- **Duplicate detection**: Show existing records for merge
- **Format errors**: Highlight specific field issues
- **Missing data**: Allow partial import with warnings
- **Rollback capability**: Full transaction rollback on failure

#### User Experience
- **Progress indicators**: Show import/export progress
- **Error messages**: Clear, actionable error descriptions
- **Recovery options**: Undo recent changes, bulk corrections
- **Audit trail**: Track all modifications with user attribution

### Testing Strategy

#### Feature Tests
- **CRUD operations**: All estate/house/contact operations
- **Import/export**: CSV handling, validation, error cases
- **Search functionality**: Various search combinations
- **Permissions**: Role-based access control

#### Unit Tests
- **Model relationships**: Ensure proper associations
- **Validation rules**: Test all validation scenarios
- **Service classes**: Business logic testing
- **Import/export**: Data transformation testing

### Integration Points

#### Meter Reading Integration
- Houses automatically available for reading entry
- Contact info pre-populated in reading forms
- Historical readings linked to house records

#### Invoice Integration
- Primary contacts auto-selected for invoice delivery
- Contact changes reflected in billing system
- Delivery preference settings applied

#### WhatsApp Integration
- Contact phone numbers validated for WhatsApp
- Opt-in/opt-out preferences respected
- Delivery status tracking

### Mobile Responsiveness

#### Responsive Design
- **Mobile**: Single column layout, touch-friendly buttons
- **Tablet**: Two-column layout for house/contact lists
- **Desktop**: Full three-column layout with sidebar

#### Touch Optimizations
- **Swipe gestures**: For navigation between records
- **Touch targets**: Minimum 44x44px for buttons
- **Zoom prevention**: Prevent zoom on form inputs
- **Camera access**: Direct photo capture for house images

### Deployment Considerations

#### Migration Strategy
- **Zero-downtime**: Use Laravel migrations with rollback
- **Data seeding**: Sample estates and houses for testing
- **Rollback plan**: Database backup before deployment

#### Monitoring
- **Performance metrics**: Query times, page load speeds
- **Error tracking**: Laravel logs, exception monitoring
- **Usage analytics**: Popular searches, import success rates