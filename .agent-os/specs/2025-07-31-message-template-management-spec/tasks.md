# Spec Tasks

> Created: 2025-07-31
> Status: Ready for Implementation

## Tasks

- [ ] 1. Implement Message Template Listing View
  - [ ] 1.1 Write feature tests for `MessageTemplateList` component (data display, pagination).
  - [ ] 1.2 Create `app/Livewire/Settings/MessageTemplateList.php` Livewire component.
  - [ ] 1.3 Create `resources/views/livewire/settings/message-template-list.blade.php` view.
  - [ ] 1.4 Add navigation link to Message Template Management from dashboard/sidebar (e.g., under Settings).
  - [ ] 1.5 Implement pagination for message templates.
  - [ ] 1.6 Ensure proper data display for each template entry (name, type, preview).
  - [ ] 1.7 Verify all tests pass.

- [ ] 2. Implement Message Template Creation
  - [ ] 2.1 Write feature tests for `MessageTemplateForm` component (submission, validation, placeholder support).
  - [ ] 2.2 Create `app/Livewire/Settings/MessageTemplateForm.php` Livewire component (can be reused for edit).
  - [ ] 2.3 Integrate `MessageTemplateForm` into the `MessageTemplateList` view (e.g., as a modal or dedicated page).
  - [ ] 2.4 Implement form validation for name, type, and content.
  - [ ] 2.5 Implement a simple mechanism for adding/displaying placeholders in the content editor.
  - [ ] 2.6 Implement logic to create new message templates.
  - [ ] 2.7 Verify all tests pass.

- [ ] 3. Implement Message Template Editing
  - [ ] 3.1 Write feature tests for `MessageTemplateForm` component (editing existing templates).
  - [ ] 3.2 Reuse `app/Livewire/Settings/MessageTemplateForm.php` for editing functionality.
  - [ ] 3.3 Implement logic to populate the form with existing message template data.
  - [ ] 3.4 Implement logic to update existing message templates.
  - [ ] 3.5 Verify all tests pass.

- [ ] 4. Implement Message Template Deletion
  - [ ] 4.1 Write feature tests for message template deletion.
  - [ ] 4.2 Implement deletion logic within `MessageTemplateList` or a dedicated action.
  - [ ] 4.3 Add confirmation step before deletion.
  - [ ] 4.4 Verify all tests pass.

- [ ] 5. Implement Placeholder Rendering
  - [ ] 5.1 Write unit tests for placeholder rendering logic.
  - [ ] 5.2 Develop a helper function or service to replace placeholders with actual data when sending messages.
  - [ ] 5.3 Integrate placeholder rendering into `WhatsAppService` and any email sending logic.
  - [ ] 5.4 Verify all tests pass.
