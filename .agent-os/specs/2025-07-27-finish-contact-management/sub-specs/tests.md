# Tests Specification

This is the tests coverage details for the spec detailed in @.agent-os/specs/2025-07-27-finish-contact-management/spec.md

> Created: 2025-07-27
> Version: 1.0.0

## Test Coverage

### Unit Tests

**House Model**
- Test that the `contacts()` relationship is a `belongsToMany` relationship.
- Test that a house can have multiple contacts.
- Test that a contact can be attached to a house.
- Test that a contact can be detached from a house.

**Contact Model**
- Test that the `houses()` relationship is a `belongsToMany` relationship.
- Test that a contact can be associated with multiple houses.

**Estate Model**
- Test the `houses` relationship.
- Test the `activeHouses`, `occupiedHouses`, and `vacantHouses` scopes.

**HouseSearchService**
- Test that the search service returns the correct results for various search criteria.

**ImportExportService**
- Test that a CSV of houses can be imported correctly.
- Test that houses can be exported to a CSV correctly.

**ValidationService**
- Test that the validation service correctly identifies duplicate houses and contacts.

### Feature Tests

**EstateManager**
- Test that the component renders correctly.
- Test that estates can be created, updated, and deleted.
- Test pagination.

**HouseRegistry**
- Test that the component renders correctly.
- Test that houses are listed and paginated.
- Test that the search functionality works.

**HouseForm**
- Test that the form can be used to create and update a house.
- Test validation.

**HouseImport**
- Test that a CSV can be uploaded and that the preview is displayed correctly.
- Test that the import process works.

**ContactManager**
- Test that contacts are listed for a house.
- Test that contacts can be added, edited, and removed.

**ContactForm**
- Test that the form can be used to create and update a contact.
- Test validation.

### Mocking Requirements

- **Storage:** Mock the file system for CSV import/export tests.
