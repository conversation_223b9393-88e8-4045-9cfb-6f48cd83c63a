# Spec Tasks

These are the tasks to be completed for the spec detailed in @.agent-os/specs/2025-07-27-finish-contact-management/spec.md

> Created: 2025-07-27
> Status: Ready for Implementation

## Tasks

- [ ] 1. **Fix House-Contact Relationship**
  - [ ] 1.1 Write a failing unit test for the `House` model's `contacts()` relationship.
  - [ ] 1.2 Modify the `contacts()` method in `app/Models/House.php` to use `belongsToMany`.
  - [ ] 1.3 Verify the test passes.

- [ ] 2. **Write Model Unit Tests**
  - [ ] 2.1 Write unit tests for the `Estate` model.
  - [ ] 2.2 Write unit tests for the `House` model.
  - [ ] 2.3 Write unit tests for the `Contact` model.

- [ ] 3. **Write Service Unit Tests**
  - [ ] 3.1 Write unit tests for the `HouseSearchService`.
  - [ ] 3.2 Write unit tests for the `ImportExportService`.
  - [ ] 3.3 Write unit tests for the `ValidationService`.

- [ ] 4. **Write Livewire Feature Tests**
  - [ ] 4.1 Write feature tests for the `EstateManager` component.
  - [ ] 4.2 Write feature tests for the `HouseRegistry` component.
  - [ ] 4.3 Write feature tests for the `HouseForm` component.
  - [ ] 4.4 Write feature tests for the `HouseImport` component.
  - [ ] 4.5 Write feature tests for the `ContactManager` component.
  - [ ] 4.6 Write feature tests for the `ContactForm` component.

- [ ] 5. **Review and Complete Frontend Redesign**
  - [ ] 5.1 Review the current implementation of the home and contact pages.
  - [ ] 5.2 Identify any missing elements from `REDESIGN_SPEC.md`.
  - [ ] 5.3 Implement the missing elements.
  - [ ] 5.4 Verify the pages are fully responsive and match the design spec.
