# Meter Reading System Specification

## Overview
Comprehensive meter reading management system for capturing, validating, and managing water consumption data across residential estates. Supports multiple reading collection methods, automated validation, anomaly detection, and integration with billing systems.

## Core Features

### 1. Reading Collection Methods
- **Manual Entry**: Web-based form for caretakers/managers
- **Mobile App**: Dedicated mobile interface for field readings
- **Bulk Upload**: CSV/Excel file import for batch processing
- **API Integration**: Third-party meter reading systems
- **IoT Integration**: Direct meter connectivity (future)
- **Photo Capture**: Evidence-based reading with image verification

### 2. Reading Validation System
- **Progression Validation**: Ensure new reading > previous reading
- **Anomaly Detection**: Flag unusual consumption patterns
- **Range Validation**: Check against historical min/max values
- **Duplicate Prevention**: Block duplicate entries for same period
- **Meter Reset Handling**: Detect and handle meter rollovers
- **Estimation Support**: Allow estimated readings with clear marking

### 3. Data Management
- **Reading History**: Complete audit trail of all readings
- **Correction Workflow**: System for fixing erroneous readings
- **Approval Process**: Multi-level approval for critical changes
- **Data Export**: CSV/Excel export for external analysis
- **Backup & Recovery**: Automated data backup and restoration
- **Archive Management**: Historical data retention policies

### 4. Quality Assurance
- **Photo Verification**: Require photos for high-value readings
- **Double Entry**: Independent verification for accuracy
- **Supervisor Review**: Manager approval for disputed readings
- **Customer Confirmation**: Resident acknowledgment system
- **Audit Logging**: Track all reading modifications

## Technical Architecture

### Core Service Classes

#### 1. Reading Collection Service
```php
// app/Services/ReadingCollectionService.php
class ReadingCollectionService
{
    public function collectReading(House $house, $reading, Carbon $date, array $metadata = []): MeterReading
    public function bulkCollectReadings(Estate $estate, array $readings): Collection
    public function importFromFile($filePath, Estate $estate): ImportResult
    public function validateReadingFormat($reading): ValidationResult
    public function detectAnomalies(MeterReading $reading): array
}
```

#### 2. Reading Validation Service ✅ **PARTIALLY COMPLETED**
```php
// app/Services/ReadingValidationService.php
class ReadingValidationService
{
    public function validateReadingProgression(House $house, $newReading): ValidationResult ✅
    public function checkConsumptionRange(House $house, $consumption): bool
    public function detectMeterReset(House $house, $newReading): bool
    public function validateReadingDate(Carbon $date, House $house): bool
    public function checkForDuplicate(House $house, Carbon $date): bool ✅
    public function estimateReading(House $house, Carbon $date): float
}
```

#### 3. Anomaly Detection Service
```php
// app/Services/AnomalyDetectionService.php
class AnomalyDetectionService
{
    public function detectUnusualConsumption(House $house, $consumption): AnomalyResult
    public function calculateExpectedRange(House $house, Carbon $date): array
    public function flagSuspiciousReading(MeterReading $reading): void
    public function getHistoricalAverage(House $house, $months = 6): float
    public function identifyLeaks(House $house): array
}
```

## Data Models

### Meter Reading Model Extensions
```php
// app/Models/MeterReading.php
class MeterReading extends Model
{
    // Status constants
    const STATUS_DRAFT = 'draft';
    const STATUS_VALIDATED = 'validated';
    const STATUS_DISPUTED = 'disputed';
    const STATUS_CORRECTED = 'corrected';
    const STATUS_ESTIMATED = 'estimated';
    const STATUS_REJECTED = 'rejected';
    
    // Collection method constants
    const METHOD_MANUAL = 'manual';
    const METHOD_MOBILE = 'mobile';
    const METHOD_BULK_UPLOAD = 'bulk_upload';
    const METHOD_API = 'api';
    const METHOD_IOT = 'iot';
    const METHOD_ESTIMATED = 'estimated';
    
    // Relationships
    public function house()
    public function estate()
    public function user() // who entered the reading
    public function photos()
    public function corrections()
    public function approvals()
    
    // Scopes
    public function scopeForPeriod($query, $startDate, $endDate)
    public function scopeValidated($query)
    public function scopeDisputed($query)
    public function scopeByEstate($query, $estateId)
    public function scopeByHouse($query, $houseId)
    
    // Calculation methods
    public function getConsumption()
    public function getPreviousReading()
    public function getNextReading()
    public function isAnomalous()
    public function requiresApproval()
}
```

### Reading Photo Model
```php
// app/Models/ReadingPhoto.php
class ReadingPhoto extends Model
{
    public function meterReading()
    public function getUrl()
    public function getThumbnailUrl()
    public function verifyPhoto()
}
```

### Reading Correction Model
```php
// app/Models/ReadingCorrection.php
class ReadingCorrection extends Model
{
    const REASON_DATA_ENTRY_ERROR = 'data_entry_error';
    const REASON_METER_ERROR = 'meter_error';
    const REASON_DISPUTED = 'disputed';
    const REASON_VERIFICATION = 'verification';
    
    public function originalReading()
    public function correctedReading()
    public function requestedBy()
    public function approvedBy()
}
```

## Validation Rules

### Reading Progression Validation
```php
// Ensure new reading is greater than previous
public function validateReadingProgression(House $house, $newReading)
{
    $lastReading = $house->getLastReading();
    
    if (!$lastReading) {
        return ValidationResult::valid();
    }
    
    if ($newReading < $lastReading->reading) {
        // Check for meter reset (rollover)
        if ($this->detectMeterReset($house, $newReading)) {
            return ValidationResult::validWithWarning('Meter reset detected');
        }
        
        return ValidationResult::invalid('New reading must be greater than previous');
    }
    
    return ValidationResult::valid();
}
```

### Consumption Range Validation
```php
// Check if consumption falls within expected range
public function validateConsumptionRange(House $house, $consumption)
{
    $historical = $house->getHistoricalConsumption(6); // 6 months
    $average = $historical->avg();
    $stdDev = $historical->stdDev();
    
    $minExpected = max(0, $average - (2 * $stdDev));
    $maxExpected = $average + (2 * $stdDev);
    
    if ($consumption < $minExpected || $consumption > $maxExpected) {
        return ValidationResult::flagAnomaly(
            "Consumption {$consumption} outside expected range ({$minExpected}-{$maxExpected})"
        );
    }
    
    return ValidationResult::valid();
}
```

## Collection Workflows

### Manual Entry Workflow
```php
// app/Livewire/MeterReadingEntry.php
class MeterReadingEntry extends Component
{
    public $houseId;
    public $reading;
    public $readingDate;
    public $photo;
    public $notes;
    
    public function submitReading()
    {
        $this->validate([
            'reading' => 'required|numeric|min:0',
            'readingDate' => 'required|date',
            'photo' => 'nullable|image|max:2048',
        ]);
        
        $reading = $this->readingService->collectReading(
            House::find($this->houseId),
            $this->reading,
            Carbon::parse($this->readingDate),
            [
                'method' => MeterReading::METHOD_MANUAL,
                'photo' => $this->photo,
                'notes' => $this->notes,
                'user_id' => auth()->id(),
            ]
        );
        
        if ($reading->requiresApproval()) {
            $this->notifySupervisor($reading);
        }
        
        session()->flash('message', 'Reading submitted successfully');
    }
}
```

### Bulk Upload Workflow
```php
// app/Console/Commands/ImportMeterReadings.php
class ImportMeterReadings extends Command
{
    protected $signature = 'readings:import {file} {--estate=} {--dry-run}';
    
    public function handle()
    {
        $result = $this->importService->importFromFile(
            $this->argument('file'),
            Estate::find($this->option('estate'))
        );
        
        $this->table(
            ['House', 'Reading', 'Status', 'Message'],
            $result->getRows()
        );
        
        if (!$this->option('dry-run') && $result->isValid()) {
            $result->processImport();
            $this->info("Imported {$result->getSuccessCount()} readings");
        }
    }
}
```

## Anomaly Detection System

### Consumption Pattern Analysis
```php
// Detect potential leaks or unusual usage
public function detectAnomalies(House $house, $consumption)
{
    $anomalies = [];
    
    // Check for sudden spikes
    $historical = $house->getHistoricalConsumption(3);
    $avg = $historical->avg();
    
    if ($consumption > ($avg * 2.5)) {
        $anomalies[] = new Anomaly(
            'consumption_spike',
            "Consumption {$consumption} is 2.5x higher than average {$avg}"
        );
    }
    
    // Check for zero consumption
    if ($consumption == 0) {
        $anomalies[] = new Anomaly(
            'zero_consumption',
            'Zero consumption detected - possible meter issue'
        );
    }
    
    // Check for gradual increases (potential leaks)
    $trend = $this->calculateTrend($historical);
    if ($trend > 0.2) { // 20% monthly increase
        $anomalies[] = new Anomaly(
            'gradual_increase',
            'Gradual consumption increase detected - possible leak'
        );
    }
    
    return $anomalies;
}
```

### Photo Verification System
```php
// Verify reading photos for accuracy
public function verifyReadingPhoto($photoPath, $expectedReading)
{
    // OCR to extract reading from photo
    $extractedReading = $this->ocrService->extractReading($photoPath);
    
    // Compare with submitted reading
    if (abs($extractedReading - $expectedReading) > 0.1) {
        return VerificationResult::mismatch(
            $expectedReading,
            $extractedReading
        );
    }
    
    // Check photo quality
    $quality = $this->imageService->assessQuality($photoPath);
    if ($quality < 0.7) {
        return VerificationResult::poorQuality($quality);
    }
    
    return VerificationResult::verified();
}
```

## Mobile Interface

### Mobile Reading Collection
```php
// API endpoint for mobile app
// POST /api/mobile/readings
public function storeMobileReading(Request $request)
{
    $validated = $request->validate([
        'house_id' => 'required|exists:houses,id',
        'reading' => 'required|numeric|min:0',
        'photo' => 'required|image',
        'location' => 'required|array',
        'timestamp' => 'required|date',
    ]);
    
    $reading = $this->readingService->collectReading(
        House::find($validated['house_id']),
        $validated['reading'],
        Carbon::parse($validated['timestamp']),
        [
            'method' => MeterReading::METHOD_MOBILE,
            'photo' => $validated['photo'],
            'location' => $validated['location'],
            'user_id' => auth()->id(),
        ]
    );
    
    return response()->json([
        'reading' => $reading,
        'anomalies' => $reading->getAnomalies(),
        'requires_approval' => $reading->requiresApproval(),
    ]);
}
```

### GPS-based House Identification
```php
// Find nearest house based on GPS coordinates
public function findHouseByLocation($latitude, $longitude, $accuracy = 50)
{
    return House::query()
        ->selectRaw("
            houses.*,
            ST_Distance_Sphere(
                point(longitude, latitude),
                point(?, ?)
            ) as distance
        ", [$longitude, $latitude])
        ->having('distance', '<=', $accuracy)
        ->orderBy('distance')
        ->first();
}
```

## Correction & Approval Workflow

### Reading Correction Process
```php
// Handle reading correction requests
public function requestCorrection(MeterReading $reading, $newReading, $reason, $evidence = null)
{
    $correction = ReadingCorrection::create([
        'original_reading_id' => $reading->id,
        'new_reading' => $newReading,
        'reason' => $reason,
        'evidence' => $evidence,
        'requested_by' => auth()->id(),
        'status' => 'pending',
    ]);
    
    // Notify supervisors
    $this->notificationService->notifySupervisors(
        $reading->estate,
        "Reading correction requested for house {$reading->house->house_number}"
    );
    
    return $correction;
}
```

### Approval System
```php
// Multi-level approval for critical changes
public function approveReading(MeterReading $reading, User $approver)
{
    if (!$reading->requiresApproval()) {
        return ApprovalResult::notRequired();
    }
    
    $approval = ReadingApproval::create([
        'reading_id' => $reading->id,
        'approver_id' => $approver->id,
        'status' => 'approved',
        'comments' => request('comments'),
    ]);
    
    $reading->update(['status' => MeterReading::STATUS_VALIDATED]);
    
    // Trigger invoice recalculation if needed
    if ($reading->invoice) {
        $this->invoiceService->recalculate($reading->invoice);
    }
    
    return ApprovalResult::approved($approval);
}
```

## Reporting & Analytics

### Usage Analytics
```php
// Generate consumption reports
public function generateUsageReport(Estate $estate, $startDate, $endDate)
{
    return [
        'total_consumption' => $estate->getTotalConsumption($startDate, $endDate),
        'average_per_house' => $estate->getAverageConsumption($startDate, $endDate),
        'anomalies_count' => $estate->getAnomalyCount($startDate, $endDate),
        'collection_rate' => $estate->getCollectionRate($startDate, $endDate),
        'top_consumers' => $estate->getTopConsumers(10, $startDate, $endDate),
        'trend_analysis' => $estate->getConsumptionTrend($startDate, $endDate),
    ];
}
```

### Collection Performance Metrics
```php
// Track reading collection efficiency
public function getCollectionMetrics(Estate $estate, $period)
{
    $totalHouses = $estate->houses()->count();
    $collectedReadings = $estate->meterReadings()
        ->whereBetween('reading_date', $period)
        ->count();
    
    return [
        'collection_rate' => ($collectedReadings / $totalHouses) * 100,
        'missing_readings' => $totalHouses - $collectedReadings,
        'on_time_collection' => $estate->getOnTimeCollectionRate($period),
        'method_distribution' => $estate->getMethodDistribution($period),
        'error_rate' => $estate->getErrorRate($period),
    ];
}
```

## Error Handling & Recovery

### Validation Error Handling
```php
// Graceful handling of validation failures
public function handleValidationError(ValidationException $e, MeterReading $reading)
{
    switch ($e->getCode()) {
        case 'DUPLICATE_READING':
            return $this->handleDuplicateReading($reading);
            
        case 'INVALID_PROGRESSION':
            return $this->handleInvalidProgression($reading);
            
        case 'OUT_OF_RANGE':
            return $this->handleOutOfRangeReading($reading);
            
        default:
            return $this->handleGenericError($reading, $e);
    }
}
```

### Data Recovery Mechanisms
```php
// Recover from failed imports
public function recoverFromFailedImport(ImportResult $result)
{
    $failedRows = $result->getFailedRows();
    
    foreach ($failedRows as $row) {
        $this->errorLogService->logImportError(
            $row['data'],
            $row['error'],
            $row['line_number']
        );
    }
    
    // Generate recovery report
    return $this->generateRecoveryReport($failedRows);
}
```

## Testing Strategy

### Unit Tests
- Test reading validation rules
- Test anomaly detection algorithms
- Test calculation accuracy
- Test photo verification system
- Test correction workflows

### Integration Tests
- Test full reading collection workflow
- Test mobile app integration
- Test bulk import processes
- Test approval workflows
- Test notification systems

### Performance Tests
- Test bulk import with large datasets
- Test mobile app response times
- Test photo processing speed
- Test concurrent reading submissions
- Test data export performance

### Security Tests
- Test unauthorized access prevention
- Test data integrity validation
- Test photo upload security
- Test API rate limiting
- Test data encryption

## Monitoring & Analytics

### Key Metrics
- **Collection Rate**: Percentage of houses with readings
- **Validation Success Rate**: Percentage of readings passing validation
- **Anomaly Detection Rate**: Accuracy of anomaly detection
- **Correction Request Rate**: Frequency of reading corrections
- **Mobile Usage Rate**: Adoption of mobile collection methods

### Alert System
- **Missing Readings**: Alert for houses without current readings
- **High Anomaly Rate**: Alert when anomaly rate exceeds threshold
- **Collection Delays**: Alert when readings are overdue
- **System Errors**: Immediate alerts for system failures
- **Data Quality Issues**: Alerts for data integrity problems

## Security & Compliance

### Data Protection
- **Reading Encryption**: Encrypt sensitive reading data
- **Access Control**: Role-based access to reading data
- **Audit Trail**: Track all reading modifications
- **Photo Privacy**: Secure storage of reading photos
- **Data Retention**: Compliant data retention policies

### Privacy Compliance
- **Photo Consent**: Manage consent for photo collection
- **Location Privacy**: Secure handling of GPS coordinates
- **Data Minimization**: Collect only necessary data
- **Right to Correction**: Allow residents to dispute readings
- **Transparency**: Clear data usage policies

## Future Enhancements
- **IoT Integration**: Direct meter connectivity
- **AI-powered Estimation**: ML-based reading estimation
- **Smart Meter Support**: Real-time consumption monitoring
- **Voice Collection**: Voice-based reading entry
- **Blockchain Verification**: Immutable reading records
- **Predictive Analytics**: Consumption forecasting
- **Integration with Smart Home Systems**: Automated data collection