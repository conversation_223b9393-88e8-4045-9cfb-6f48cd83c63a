# Message Management CRUD

## Component Structure
```
app/Livewire/MessageManager.php
app/Livewire/MessageShow.php
app/Livewire/MessageComposer.php
resources/views/livewire/message-manager.blade.php
resources/views/livewire/message-show.blade.php
resources/views/livewire/message-composer.blade.php
```

## Features

### Message List (`/messages`)
- Conversation-style message threads
- Columns: contact, last message, status, timestamp, direction
- Quick actions: view thread, reply, mark read, delete
- Bulk operations: mark read, delete, export conversations
- Filters: status, direction, date range, message type, estate
- Search: message content, contact name, phone number

### Message Thread View (`/messages/{contact_id}`)
- WhatsApp-style conversation interface
- Message bubbles with timestamps
- Delivery status indicators
- Media attachments support
- Quick reply templates
- Message search within thread
- Export conversation history

### Message Composer (`/messages/compose`)
- New message creation
- Contact selection with search
- Template selection
- Media attachment support
- Bulk messaging to multiple contacts
- Scheduled message sending
- Message preview before sending

### Message Templates
- Pre-defined message templates
- Category organization (billing, maintenance, general)
- Variable substitution (name, amount, due date)
- Template management (create, edit, delete)
- Usage analytics for templates

## Database Schema (WhatsApp Messages)
```sql
-- Enhance existing whatsapp_messages table
ALTER TABLE whatsapp_messages ADD COLUMN thread_id VARCHAR(100) NULL;
ALTER TABLE whatsapp_messages ADD COLUMN reply_to_id BIGINT NULL;
ALTER TABLE whatsapp_messages ADD COLUMN media_url VARCHAR(500) NULL;
ALTER TABLE whatsapp_messages ADD COLUMN media_type VARCHAR(50) NULL;
ALTER TABLE whatsapp_messages ADD COLUMN template_id BIGINT NULL;
ALTER TABLE whatsapp_messages ADD COLUMN scheduled_at TIMESTAMP NULL;
ALTER TABLE whatsapp_messages ADD COLUMN read_at TIMESTAMP NULL;

-- Message templates table
CREATE TABLE message_templates (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    category VARCHAR(100),
    content TEXT,
    variables JSON,
    usage_count INT DEFAULT 0,
    created_by BIGINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Bulk message jobs table
CREATE TABLE bulk_message_jobs (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    template_id BIGINT,
    recipient_count INT,
    sent_count INT DEFAULT 0,
    failed_count INT DEFAULT 0,
    status ENUM('pending', 'processing', 'completed', 'failed'),
    scheduled_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_by BIGINT,
    created_at TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES message_templates(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

## Livewire Component Methods
```php
class MessageManager extends Component
{
    public $filters = [
        'status' => null,
        'direction' => null,
        'estate_id' => null,
        'date_from' => null,
        'date_to' => null
    ];
    
    public function mount() {}
    public function search() {}
    public function applyFilters() {}
    public function viewThread($contactId) {}
    public function markAsRead($messageId) {}
    public function deleteMessage($messageId) {}
    public function bulkMarkRead() {}
    public function exportMessages() {}
    public function refreshMessages() {}
}

class MessageShow extends Component
{
    public $contactId;
    public $messages;
    public $newMessage = '';
    
    public function mount($contactId) {}
    public function sendMessage() {}
    public function useTemplate($templateId) {}
    public function uploadMedia() {}
    public function markAsRead($messageId) {}
    public function deleteMessage($messageId) {}
    public function exportThread() {}
    public function loadMoreMessages() {}
}

class MessageComposer extends Component
{
    public $recipients = [];
    public $message = '';
    public $templateId = null;
    public $scheduledAt = null;
    
    public function mount() {}
    public function addRecipient($contactId) {}
    public function removeRecipient($contactId) {}
    public function selectTemplate($templateId) {}
    public function previewMessage() {}
    public function sendMessage() {}
    public function scheduleMessage() {}
    public function saveDraft() {}
}
```

## Special Features

### WhatsApp Integration
- Business API integration
- Message status tracking (sent, delivered, read)
- Media message support (images, documents)
- Template message compliance
- Webhook handling for incoming messages

### Bulk Messaging
- Contact list selection
- Template-based messaging
- Variable substitution
- Delivery scheduling
- Progress tracking
- Failed message retry

### Message Analytics
- Delivery rates
- Read rates
- Response rates
- Template performance
- Contact engagement metrics

### Automation Features
- Auto-responses for common queries
- Scheduled reminders
- Triggered messages (invoice due, payment received)
- Escalation workflows

## Message Categories
- **Billing**: Invoice notifications, payment reminders, receipts
- **Maintenance**: Service notifications, outage alerts
- **General**: Welcome messages, announcements, surveys
- **Emergency**: Urgent notifications, system alerts

## Compliance Features
- Message retention policies
- Opt-out management
- GDPR compliance
- Business messaging guidelines
- Spam prevention

## Integration Points
- Contact management system
- Invoice system (payment notifications)
- Meter reading system (reading reminders)
- User management (role-based messaging)

## Permissions
- Management: Full access, bulk messaging, template management
- Reviewer: Send/receive messages, view conversations
- Caretaker: Limited to assigned estate contacts