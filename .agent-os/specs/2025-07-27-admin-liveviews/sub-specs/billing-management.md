# Billing Management Component

## Component Structure
```
app/Livewire/Admin/BillingManager.php
resources/views/livewire/admin/billing-manager.blade.php
```

## Features

### Invoice Overview
- Summary cards:
  - Total outstanding
  - Overdue amount
  - This month's billing
  - Collection rate
- Invoice list with filters:
  - Status (draft, sent, paid, overdue)
  - Estate
  - Date range
  - Amount range

### Bulk Operations
- Generate invoices for estate
- Send reminders
- Mark as paid
- Apply late fees
- Export selected

### Payment Management
- Record payments
- Partial payment handling
- Payment methods tracking
- Receipt generation
- Payment history

### Overdue Management (`/invoices/overdue`)
- Overdue invoice list
- Aging analysis (30, 60, 90+ days)
- Automated reminder scheduling
- Disconnection list generation
- Payment plan creation

## Database Enhancements
```sql
-- Add to invoices table
ALTER TABLE invoices ADD COLUMN reminder_count INT DEFAULT 0;
ALTER TABLE invoices ADD COLUMN last_reminder_at TIMESTAMP NULL;
ALTER TABLE invoices ADD COLUMN disconnection_scheduled DATE NULL;

-- Payment plans table
CREATE TABLE payment_plans (
    id BIGINT PRIMARY KEY,
    invoice_id BIGINT,
    total_amount DECIMAL(10,2),
    installments INT,
    status ENUM('active', 'completed', 'defaulted'),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## Livewire Component Methods
```php
class BillingManager extends Component
{
    public $view = 'overview'; // overview, overdue, payments
    
    public function mount() {}
    public function generateInvoices($estateId) {}
    public function bulkSendReminders() {}
    public function recordPayment($invoiceId) {}
    public function createPaymentPlan($invoiceId) {}
    public function scheduleDisconnection() {}
    public function exportOverdue() {}
    public function viewInvoiceDetails($id) {}
}
```

## Business Rules
- Overdue after 30 days
- First reminder at 7 days
- Second reminder at 21 days
- Final notice at 30 days
- Disconnection notice at 45 days