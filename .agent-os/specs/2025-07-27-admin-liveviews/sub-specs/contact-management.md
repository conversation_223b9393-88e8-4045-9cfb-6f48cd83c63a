# Contact Management CRUD

## Component Structure
```
app/Livewire/ContactManager.php
app/Livewire/ContactShow.php
resources/views/livewire/contact-manager.blade.php
resources/views/livewire/contact-show.blade.php
```

## Features

### Contact List (`/contacts`)
- Paginated table with global search
- Columns: name, phone, email, house, estate, status, last contact
- Quick actions: view, edit, call, message, delete
- Bulk operations: export, send bulk messages, import contacts
- Filters: estate, house, contact type, status, communication preferences
- Advanced search: name, phone, email, address

### Contact Create/Edit (`/contacts/create`, `/contacts/{id}/edit`)
- Form fields:
  - Personal information (name, phone, email)
  - House assignment (estate + house selection)
  - Contact type (owner, tenant, emergency contact)
  - Relationship to property
  - Communication preferences
  - Emergency contact details
  - Notes and special instructions
- Phone number validation and formatting
- Email validation with duplicate checking
- House assignment with occupancy validation

### Resident Contact Profile (`/resident/profile`)
- Resident-specific contact management interface
- Pre-populated with user account data (name, email)
- Immutable property information display (estate, house, address)
- Separate account email (read-only) and contact email (editable)
- Comprehensive contact details and communication preferences
- Security: Users can only access/edit their own house contact information

### Contact Show (`/contacts/{id}`)
- Contact profile overview
- House and billing information
- Communication history
- Message thread with WhatsApp integration
- Payment history
- Document attachments
- Emergency contact information
- Activity timeline

### Contact Communication
- WhatsApp message integration
- SMS sending capability
- Email communication
- Call logging
- Message templates
- Bulk messaging tools

## Database Enhancements
```sql
-- Add to contacts table
ALTER TABLE contacts ADD COLUMN contact_type ENUM('owner', 'tenant', 'emergency', 'agent') DEFAULT 'tenant';
ALTER TABLE contacts ADD COLUMN relationship VARCHAR(100) NULL;
ALTER TABLE contacts ADD COLUMN emergency_contact_name VARCHAR(255) NULL;
ALTER TABLE contacts ADD COLUMN emergency_contact_phone VARCHAR(20) NULL;
ALTER TABLE contacts ADD COLUMN communication_preference ENUM('whatsapp', 'sms', 'email', 'call') DEFAULT 'whatsapp';
ALTER TABLE contacts ADD COLUMN status ENUM('active', 'inactive', 'blocked') DEFAULT 'active';
ALTER TABLE contacts ADD COLUMN last_contacted_at TIMESTAMP NULL;
ALTER TABLE contacts ADD COLUMN notes TEXT NULL;

-- Communication log table
CREATE TABLE communication_logs (
    id BIGINT PRIMARY KEY,
    contact_id BIGINT,
    type ENUM('whatsapp', 'sms', 'email', 'call'),
    direction ENUM('inbound', 'outbound'),
    subject VARCHAR(255) NULL,
    message TEXT,
    status ENUM('sent', 'delivered', 'read', 'failed'),
    sent_by BIGINT NULL,
    created_at TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id),
    FOREIGN KEY (sent_by) REFERENCES users(id)
);
```

## Livewire Component Methods
```php
class ContactManager extends Component
{
    public $selectedEstate = null;
    public $selectedHouse = null;
    public $filters = [];
    
    public function mount() {}
    public function search() {}
    public function filterByEstate($estateId) {}
    public function filterByHouse($houseId) {}
    public function create() {}
    public function edit($contactId) {}
    public function save() {}
    public function delete($contactId) {}
    public function sendMessage($contactId) {}
    public function bulkMessage() {}
    public function importContacts() {}
    public function exportContacts() {}
}

class ContactShow extends Component
{
    public Contact $contact;
    public $activeTab = 'profile';
    
    public function mount($id) {}
    public function switchTab($tab) {}
    public function sendWhatsApp($message) {}
    public function sendEmail($subject, $message) {}
    public function logCall($notes) {}
    public function updatePreferences() {}
    public function uploadDocument() {}
    public function addNote($note) {}
}
```

## Special Features

### House Assignment Management
- Multiple contacts per house
- Primary contact designation
- Contact type hierarchy (owner > tenant > emergency)
- Automatic notifications on assignment changes

### Communication Integration
- WhatsApp Business API integration
- Message template management
- Delivery status tracking
- Auto-responses for common queries
- Bulk messaging with personalization

### Import/Export Tools
- CSV import with validation
- Excel export with formatting
- Contact deduplication
- Data cleansing tools
- Backup and restore

### Privacy & Compliance
- Data anonymization options
- GDPR compliance tools
- Contact consent management
- Communication opt-out handling

## Permissions
- Management: Full CRUD access, bulk operations
- Reviewer: Read access, can send messages
- Caretaker: View assigned estate contacts, limited messaging