# User Management Component

## Component Structure
```
app/Livewire/Admin/UserManager.php
resources/views/livewire/admin/user-manager.blade.php
```

## Features

### User List
- Paginated table with 25 users per page
- Search by name, email, role
- Sort by name, email, created date, last login
- Bulk actions: activate/deactivate, delete

### User Form
- Create/edit modal
- Fields: name, email, password, role, estates (for caretakers)
- Email validation with uniqueness check
- Password strength requirements
- Role-based estate assignment

### User Details
- View user profile
- Activity history (last 50 actions)
- Assigned estates (for caretakers)
- Login history
- Quick actions: reset password, change role

## Database Schema
```sql
-- Already exists in users table
-- Add activity_logs table for audit trail
CREATE TABLE activity_logs (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    action VARCHAR(255),
    model_type VARCHAR(255),
    model_id BIGINT,
    changes JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP
);
```

## Livewire Component Methods
```php
class UserManager extends Component
{
    public function mount() {}
    public function search() {}
    public function create() {}
    public function edit($userId) {}
    public function save() {}
    public function delete($userId) {}
    public function bulkDelete() {}
    public function toggleStatus($userId) {}
    public function resetPassword($userId) {}
    public function exportUsers() {}
}
```

## Permissions
- Only 'management' role can access
- Cannot delete own account
- Cannot change own role
- Super admin protection