# Complete Route to Component Mapping

## All Required Routes and Components

### Admin Management Routes
```php
// User & System Administration
Route::get('/admin/users', UserManager::class)->name('admin.users');
Route::get('/admin/settings', SystemSettings::class)->name('admin.settings');
Route::get('/admin/audit', AuditLogs::class)->name('admin.audit');

// Billing & Financial Management
Route::get('/billing', BillingManager::class)->name('billing.index');
Route::get('/invoices/overdue', [BillingManager::class, 'overdue'])->name('invoices.overdue');

// Reports & Analytics
Route::get('/reports', ReportsDashboard::class)->name('reports.index');
Route::get('/reports/consumption', [ReportsDashboard::class, 'consumption'])->name('reports.consumption');

// Data Export
Route::get('/exports', ExportCenter::class)->name('exports.index');
```

### Core Entity CRUD Routes
```php
// Estate Management
Route::get('/estates', EstateManager::class)->name('estates.index');
Route::get('/estates/create', [EstateManager::class, 'create'])->name('estates.create');
Route::get('/estates/{estate}', EstateShow::class)->name('estates.show');
Route::get('/estates/{estate}/edit', [EstateManager::class, 'edit'])->name('estates.edit');

// House Management
Route::get('/houses', HouseManager::class)->name('houses.index');
Route::get('/houses/create', [HouseManager::class, 'create'])->name('houses.create');
Route::get('/houses/{house}', HouseShow::class)->name('houses.show');
Route::get('/houses/{house}/edit', [HouseManager::class, 'edit'])->name('houses.edit');

// Contact Management
Route::get('/contacts', ContactManager::class)->name('contacts.index');
Route::get('/contacts/create', [ContactManager::class, 'create'])->name('contacts.create');
Route::get('/contacts/{contact}', ContactShow::class)->name('contacts.show');
Route::get('/contacts/{contact}/edit', [ContactManager::class, 'edit'])->name('contacts.edit');

// Invoice Management
Route::get('/invoices', InvoiceManager::class)->name('invoices.index');
Route::get('/invoices/create', [InvoiceManager::class, 'create'])->name('invoices.create');
Route::get('/invoices/{invoice}', InvoiceShow::class)->name('invoices.show');
Route::get('/invoices/{invoice}/edit', [InvoiceManager::class, 'edit'])->name('invoices.edit');

// Message Management
Route::get('/messages', MessageManager::class)->name('messages.index');
Route::get('/messages/compose', MessageComposer::class)->name('messages.compose');
Route::get('/messages/{contact}', MessageShow::class)->name('messages.show');
```

## Complete Component File Structure
```
app/Livewire/
├── Admin/
│   ├── UserManager.php          # /admin/users
│   ├── SystemSettings.php       # /admin/settings
│   ├── AuditLogs.php           # /admin/audit
│   ├── BillingManager.php      # /billing, /invoices/overdue
│   ├── ReportsDashboard.php    # /reports, /reports/consumption
│   └── ExportCenter.php        # /exports
├── EstateManager.php            # /estates (list, create, edit)
├── EstateShow.php              # /estates/{id}
├── HouseManager.php            # /houses (list, create, edit)
├── HouseShow.php               # /houses/{id}
├── ContactManager.php          # /contacts (list, create, edit)
├── ContactShow.php             # /contacts/{id}
├── InvoiceManager.php          # /invoices (list, create, edit)
├── InvoiceShow.php             # /invoices/{id}
├── MessageManager.php          # /messages (list)
├── MessageShow.php             # /messages/{contact_id}
└── MessageComposer.php         # /messages/compose

resources/views/livewire/
├── admin/
│   ├── user-manager.blade.php
│   ├── system-settings.blade.php
│   ├── audit-logs.blade.php
│   ├── billing-manager.blade.php
│   ├── reports-dashboard.blade.php
│   └── export-center.blade.php
├── estate-manager.blade.php
├── estate-show.blade.php
├── house-manager.blade.php
├── house-show.blade.php
├── contact-manager.blade.php
├── contact-show.blade.php
├── invoice-manager.blade.php
├── invoice-show.blade.php
├── message-manager.blade.php
├── message-show.blade.php
└── message-composer.blade.php
```

## Navigation Structure Update
```php
// resources/views/partials/navigation.blade.php

// Main Navigation
<flux:navlist.item icon="building-office" href="{{ route('estates.index') }}">Estates</flux:navlist.item>
<flux:navlist.item icon="home" href="{{ route('houses.index') }}">Houses</flux:navlist.item>
<flux:navlist.item icon="users" href="{{ route('contacts.index') }}">Contacts</flux:navlist.item>
<flux:navlist.item icon="document-text" href="{{ route('invoices.index') }}">Invoices</flux:navlist.item>
<flux:navlist.item icon="chat-bubble-left-right" href="{{ route('messages.index') }}">Messages</flux:navlist.item>

// Financial Section
<flux:navlist.item icon="currency-dollar" href="{{ route('billing.index') }}">Billing</flux:navlist.item>
<flux:navlist.item icon="chart-bar" href="{{ route('reports.index') }}">Reports</flux:navlist.item>

// Admin Section (Management role only)
@if(auth()->user()->role === 'management')
<flux:navlist.group expandable heading="Administration">
    <flux:navlist.item icon="users" href="{{ route('admin.users') }}">User Management</flux:navlist.item>
    <flux:navlist.item icon="cog" href="{{ route('admin.settings') }}">System Settings</flux:navlist.item>
    <flux:navlist.item icon="document-text" href="{{ route('admin.audit') }}">Audit Logs</flux:navlist.item>
    <flux:navlist.item icon="download" href="{{ route('exports.index') }}">Export Center</flux:navlist.item>
</flux:navlist.group>
@endif
```

## Permission Middleware Groups
```php
// routes/web.php

// Management only routes
Route::middleware(['auth', 'role:management'])->group(function () {
    Route::get('/admin/users', UserManager::class)->name('admin.users');
    Route::get('/admin/settings', SystemSettings::class)->name('admin.settings');
    Route::get('/admin/audit', AuditLogs::class)->name('admin.audit');
    Route::get('/exports', ExportCenter::class)->name('exports.index');
});

// Management and Reviewer routes
Route::middleware(['auth', 'role:management,reviewer'])->group(function () {
    Route::get('/billing', BillingManager::class)->name('billing.index');
    Route::get('/invoices/overdue', [BillingManager::class, 'overdue'])->name('invoices.overdue');
    Route::get('/reports', ReportsDashboard::class)->name('reports.index');
    Route::get('/reports/consumption', [ReportsDashboard::class, 'consumption'])->name('reports.consumption');
    
    // Full CRUD for core entities
    Route::resource('estates', EstateManager::class);
    Route::resource('houses', HouseManager::class);
    Route::resource('contacts', ContactManager::class);
    Route::resource('invoices', InvoiceManager::class);
});

// All authenticated users (with estate-based restrictions for caretakers)
Route::middleware(['auth'])->group(function () {
    Route::get('/messages', MessageManager::class)->name('messages.index');
    Route::get('/messages/compose', MessageComposer::class)->name('messages.compose');
    Route::get('/messages/{contact}', MessageShow::class)->name('messages.show');
});
```

## Implementation Priority
1. **Core CRUD Components** (Days 1-4)
   - EstateManager/Show
   - HouseManager/Show  
   - ContactManager/Show
   - InvoiceManager/Show

2. **Communication System** (Days 5-6)
   - MessageManager
   - MessageShow
   - MessageComposer

3. **Admin Components** (Days 7-10)
   - UserManager
   - SystemSettings
   - BillingManager
   - AuditLogs

4. **Analytics & Export** (Days 11-12)
   - ReportsDashboard
   - ExportCenter

## Total Components: 17 Livewire Components
- 6 Admin components
- 8 Core entity CRUD components  
- 3 Message management components