# Route to Component Mapping

## Current Placeholder Routes
Based on `routes/web.php`, these routes need Livewire components:

### Admin Routes
```php
// Current placeholder routes that need implementation:

Route::get('/admin/users', UserManager::class)->name('admin.users');
Route::get('/admin/settings', SystemSettings::class)->name('admin.settings');
Route::get('/admin/audit', AuditLogs::class)->name('admin.audit');

Route::get('/billing', BillingManager::class)->name('billing.index');
Route::get('/invoices/overdue', [BillingManager::class, 'overdue'])->name('invoices.overdue');

Route::get('/reports', ReportsDashboard::class)->name('reports.index');
Route::get('/reports/consumption', [ReportsDashboard::class, 'consumption'])->name('reports.consumption');

Route::get('/exports', ExportCenter::class)->name('exports.index');
```

## Component File Structure
```
app/Livewire/Admin/
├── UserManager.php          # /admin/users
├── SystemSettings.php       # /admin/settings
├── AuditLogs.php           # /admin/audit
├── BillingManager.php      # /billing, /invoices/overdue
├── ReportsDashboard.php    # /reports, /reports/consumption
└── ExportCenter.php        # /exports

resources/views/livewire/admin/
├── user-manager.blade.php
├── system-settings.blade.php
├── audit-logs.blade.php
├── billing-manager.blade.php
├── reports-dashboard.blade.php
└── export-center.blade.php
```

## Navigation Updates
Update `resources/views/partials/navigation.blade.php` to include:

```php
// Add to admin section
<flux:navlist.item icon="users" href="{{ route('admin.users') }}">User Management</flux:navlist.item>
<flux:navlist.item icon="cog" href="{{ route('admin.settings') }}">System Settings</flux:navlist.item>
<flux:navlist.item icon="document-text" href="{{ route('admin.audit') }}">Audit Logs</flux:navlist.item>

// Add to main navigation
<flux:navlist.item icon="currency-dollar" href="{{ route('billing.index') }}">Billing</flux:navlist.item>
<flux:navlist.item icon="chart-bar" href="{{ route('reports.index') }}">Reports</flux:navlist.item>
<flux:navlist.item icon="download" href="{{ route('exports.index') }}">Exports</flux:navlist.item>
```

## Permission Middleware
Apply role-based access control:

```php
// In routes/web.php
Route::middleware(['auth', 'role:management'])->group(function () {
    Route::get('/admin/users', UserManager::class)->name('admin.users');
    Route::get('/admin/settings', SystemSettings::class)->name('admin.settings');
    Route::get('/admin/audit', AuditLogs::class)->name('admin.audit');
    Route::get('/exports', ExportCenter::class)->name('exports.index');
});

Route::middleware(['auth', 'role:management,reviewer'])->group(function () {
    Route::get('/billing', BillingManager::class)->name('billing.index');
    Route::get('/invoices/overdue', [BillingManager::class, 'overdue'])->name('invoices.overdue');
    Route::get('/reports', ReportsDashboard::class)->name('reports.index');
    Route::get('/reports/consumption', [ReportsDashboard::class, 'consumption'])->name('reports.consumption');
});
```

## Implementation Order
1. **UserManager** - Foundation for user management
2. **SystemSettings** - Core configuration
3. **AuditLogs** - Security and compliance
4. **BillingManager** - Financial operations
5. **ReportsDashboard** - Analytics and insights
6. **ExportCenter** - Data export capabilities