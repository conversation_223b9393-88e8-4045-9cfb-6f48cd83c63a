# Audit Logs Component

## Component Structure
```
app/Livewire/Admin/AuditLogs.php
resources/views/livewire/admin/audit-logs.blade.php
```

## Features

### Log Viewer
- Real-time activity feed
- Filterable by:
  - User
  - Action type (create, update, delete, login)
  - Model type (User, Estate, House, Invoice, etc.)
  - Date range
  - IP address
- Searchable descriptions
- Expandable change details

### Log Entry Details
- User information
- Timestamp with timezone
- IP address and location
- User agent details
- Before/after values for updates
- Related model links

### Export Options
- CSV export
- PDF report generation
- Date range selection
- Filter preservation

## Activity Tracking
Track these events:
- User login/logout
- CRUD operations on all models
- Settings changes
- Export operations
- Failed login attempts
- Password resets
- Role changes

## Livewire Component Methods
```php
class AuditLogs extends Component
{
    public $filters = [
        'user_id' => null,
        'action' => null,
        'model_type' => null,
        'date_from' => null,
        'date_to' => null,
    ];
    
    public function mount() {}
    public function applyFilters() {}
    public function clearFilters() {}
    public function viewDetails($logId) {}
    public function exportLogs() {}
    public function refreshLogs() {}
}
```

## Performance Considerations
- Index on created_at, user_id, action
- Pagination with 50 records per page
- Lazy load change details
- Archive old logs (>6 months)
- Use database views for complex queries