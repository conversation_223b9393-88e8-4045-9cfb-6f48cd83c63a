# System Settings Component

## Component Structure
```
app/Livewire/Admin/SystemSettings.php
resources/views/livewire/admin/system-settings.blade.php
```

## Settings Categories

### General Settings
- System name
- Default timezone
- Date/time formats
- Currency settings
- Language preferences

### Water Rate Configuration
- Rate tiers management
- Add/edit/delete tiers
- Set consumption ranges
- Configure pricing
- Effective date management

### Estate Defaults
- Default billing cycle
- Reading day preferences
- Late payment penalties
- Grace period settings

### WhatsApp Integration
- API credentials
- Message templates
- Notification triggers
- Test messaging
- Delivery reports

### Email Settings
- SMTP configuration
- From addresses
- Email templates
- Test email sending

## Database Schema
```sql
CREATE TABLE system_settings (
    id BIGINT PRIMARY KEY,
    category VARCHAR(50),
    key VARCHAR(100),
    value TEXT,
    type VARCHAR(20), -- string, number, boolean, json
    description TEXT,
    updated_by BIGINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE KEY category_key (category, key)
);
```

## Livewire Component Methods
```php
class SystemSettings extends Component
{
    public $activeTab = 'general';
    
    public function mount() {}
    public function switchTab($tab) {}
    public function updateSetting($key, $value) {}
    public function testWhatsApp() {}
    public function testEmail() {}
    public function addWaterRateTier() {}
    public function deleteWaterRateTier($id) {}
    public function saveWaterRates() {}
    public function resetToDefaults($category) {}
}
```

## Validation Rules
- Required fields validation
- Format validation (email, URL, numbers)
- Range validation for numeric values
- JSON structure validation
- API credential verification