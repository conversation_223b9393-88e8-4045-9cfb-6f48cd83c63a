# Invoice Management CRUD

## Component Structure
```
app/Livewire/InvoiceManager.php
app/Livewire/InvoiceShow.php
resources/views/livewire/invoice-manager.blade.php
resources/views/livewire/invoice-show.blade.php
```

## Features

### Invoice List (`/invoices`)
- Paginated table with advanced filtering
- Columns: invoice number, house, amount, status, due date, days overdue
- Quick actions: view, edit, send, mark paid, download PDF
- Bulk operations: send reminders, mark paid, export, generate reports
- Filters: status, estate, date range, amount range, overdue period
- Status indicators: draft, sent, paid, overdue, cancelled

### Invoice Create/Edit (`/invoices/create`, `/invoices/{id}/edit`)
- Form fields:
  - House selection (estate + house)
  - Billing period (from/to dates)
  - Meter readings (previous/current)
  - Water consumption calculation
  - Rate tier application
  - Additional charges/adjustments
  - Discount application
  - Due date setting
  - Notes and special instructions
- Automatic calculation based on water rates
- Previous balance inclusion
- Tax calculation if applicable

### Invoice Show (`/invoices/{id}`) ✅ **PARTIALLY COMPLETED**
- Invoice details with PDF preview ✅ **COMPLETED**
- Payment history and status ✅ **COMPLETED**
- Communication log
- Related documents
- Adjustment history ✅ **COMPLETED**
- Payment plan details (if applicable)
- Print/download options ✅ **COMPLETED**
- Send invoice actions

### Invoice Processing ✅ **PARTIALLY COMPLETED**
- Automatic generation from meter readings ✅ **COMPLETED**
- Batch invoice creation
- PDF generation with custom templates ✅ **COMPLETED**
- Email/WhatsApp delivery ✅ **COMPLETED**
- Payment recording and reconciliation ✅ **COMPLETED**
- Late fee calculation and application

## Database Enhancements
```sql
-- Add to invoices table
ALTER TABLE invoices ADD COLUMN invoice_number VARCHAR(50) UNIQUE;
ALTER TABLE invoices ADD COLUMN previous_balance DECIMAL(10,2) DEFAULT 0;
ALTER TABLE invoices ADD COLUMN current_charges DECIMAL(10,2) DEFAULT 0;
ALTER TABLE invoices ADD COLUMN adjustments DECIMAL(10,2) DEFAULT 0;
ALTER TABLE invoices ADD COLUMN late_fees DECIMAL(10,2) DEFAULT 0;
ALTER TABLE invoices ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0;
ALTER TABLE invoices ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0;
ALTER TABLE invoices ADD COLUMN notes TEXT NULL;
ALTER TABLE invoices ADD COLUMN sent_at TIMESTAMP NULL;
ALTER TABLE invoices ADD COLUMN reminder_count INT DEFAULT 0;
ALTER TABLE invoices ADD COLUMN last_reminder_at TIMESTAMP NULL;

-- Invoice line items table (already exists, may need enhancements)
-- Payment tracking table (already exists as invoice_payments)
```

## Livewire Component Methods
```php
class InvoiceManager extends Component
{
    public $filters = [
        'status' => null,
        'estate_id' => null,
        'date_from' => null,
        'date_to' => null,
        'overdue_only' => false
    ];
    
    public function mount() {}
    public function search() {}
    public function applyFilters() {}
    public function create() {}
    public function edit($invoiceId) {}
    public function save() {}
    public function delete($invoiceId) {}
    public function sendInvoice($invoiceId) {}
    public function markPaid($invoiceId) {}
    public function downloadPDF($invoiceId) {}
    public function bulkSendReminders() {}
    public function generateBatch($estateId) {}
    public function exportInvoices() {}
}

class InvoiceShow extends Component
{
    public Invoice $invoice;
    public $activeTab = 'details';
    
    public function mount($id) {}
    public function switchTab($tab) {}
    public function recordPayment($amount) {} ✅ **COMPLETED**
    public function sendReminder() {}
    public function addAdjustment($amount, $reason) {} ✅ **COMPLETED**
    public function applyLateFee() {}
    public function createPaymentPlan() {}
    public function downloadPDF() {} ✅ **COMPLETED**
    public function sendWhatsApp() {} ✅ **COMPLETED**
    public function sendEmail() {}
}
```

## Special Features

### Automatic Invoice Generation
- Scheduled batch generation
- Meter reading integration
- Rate calculation engine
- Previous balance carryover
- Adjustment application

### Payment Processing ✅ **PARTIALLY COMPLETED**
- Multiple payment methods ✅ **COMPLETED**
- Partial payment handling ✅ **COMPLETED**
- Overpayment credit application ✅ **COMPLETED**
- Payment plan management
- Receipt generation

### Communication Integration
- WhatsApp invoice delivery
- Email with PDF attachment
- SMS payment reminders
- Delivery status tracking
- Template customization

### Financial Controls
- Invoice approval workflow
- Adjustment authorization
- Late fee automation
- Discount management
- Tax calculation

## PDF Template Features ✅ **PARTIALLY COMPLETED**
- Company branding ✅ **COMPLETED**
- Invoice details table ✅ **COMPLETED**
- Payment instructions ✅ **COMPLETED**
- QR code for online payment
- Terms and conditions ✅ **COMPLETED**
- Multi-language support

## Reporting Integration
- Revenue reports
- Outstanding analysis
- Collection efficiency
- Payment trends
- Customer analysis

## Permissions
- Management: Full CRUD access, adjustments, approvals
- Reviewer: View, send, record payments
- Caretaker: View assigned estate invoices only