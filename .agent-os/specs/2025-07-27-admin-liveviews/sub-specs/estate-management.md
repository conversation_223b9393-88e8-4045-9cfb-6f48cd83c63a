# Estate Management CRUD

## Component Structure
```
app/Livewire/EstateManager.php
app/Livewire/EstateShow.php
resources/views/livewire/estate-manager.blade.php
resources/views/livewire/estate-show.blade.php
```

## Features

### Estate List (`/estates`)
- Paginated table with search
- Columns: name, location, total houses, active houses, manager
- Quick actions: view, edit, delete
- Bulk operations: export, assign manager
- Filter by: status, manager, location
- Sort by: name, created date, house count

### Estate Create/Edit (`/estates/create`, `/estates/{id}/edit`)
- Form fields:
  - Name (required, unique)
  - Description
  - Address/Location
  - Manager assignment
  - Default billing settings
  - Contact information
- Validation rules
- Image upload for estate photo
- Map integration for location

### Estate Show (`/estates/{id}`)
- Estate overview dashboard
- Key metrics:
  - Total houses
  - Occupied vs vacant
  - Current month revenue
  - Outstanding invoices
  - Average consumption
- House list with quick actions
- Recent activity feed
- Contact management
- Billing summary

### Estate Analytics
- Consumption trends
- Revenue analysis
- Occupancy rates
- Payment collection rates
- Maintenance requests

## Database Enhancements
```sql
-- Add to estates table
ALTER TABLE estates ADD COLUMN manager_id BIGINT NULL;
ALTER TABLE estates ADD COLUMN image_path VARCHAR(500) NULL;
ALTER TABLE estates ADD COLUMN latitude DECIMAL(10, 8) NULL;
ALTER TABLE estates ADD COLUMN longitude DECIMAL(11, 8) NULL;
ALTER TABLE estates ADD COLUMN billing_day INT DEFAULT 1;
ALTER TABLE estates ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active';
```

## Livewire Component Methods
```php
class EstateManager extends Component
{
    public function mount() {}
    public function search() {}
    public function create() {}
    public function edit($estateId) {}
    public function save() {}
    public function delete($estateId) {}
    public function bulkExport() {}
    public function assignManager($estateId, $managerId) {}
}

class EstateShow extends Component
{
    public Estate $estate;
    
    public function mount($id) {}
    public function refreshMetrics() {}
    public function exportHouses() {}
    public function generateBilling() {}
}
```

## Permissions
- Management: Full CRUD access
- Reviewer: Read-only access
- Caretaker: View assigned estates only