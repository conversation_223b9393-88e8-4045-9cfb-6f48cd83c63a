# Export Center Component

## Component Structure
```
app/Livewire/Admin/ExportCenter.php
resources/views/livewire/admin/export-center.blade.php
```

## Features

### Export Templates
- Pre-configured exports:
  - Monthly billing summary
  - Meter reading sheets
  - Customer database
  - Financial reports
  - Audit logs
- Custom template builder
- Field selection
- Filter configuration
- Format options (CSV, Excel, PDF)

### Scheduled Exports
- Recurring export jobs
- Frequency options (daily, weekly, monthly)
- Email delivery lists
- FTP/cloud upload options
- Retention policies

### Export History
- Past exports list
- Download links (7-day retention)
- Re-run previous exports
- Export logs
- Storage usage tracking

### Bulk Data Export
- Full database backup
- Selective table export
- Data anonymization options
- Compliance exports (GDPR)

## Database Schema
```sql
CREATE TABLE export_templates (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    description TEXT,
    model VARCHAR(100),
    fields JSON,
    filters JSON,
    format VARCHAR(20),
    created_by BIGIN<PERSON>,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE export_jobs (
    id BIGINT PRIMARY KEY,
    template_id BIGINT,
    status ENUM('pending', 'processing', 'completed', 'failed'),
    file_path VARCHAR(500),
    file_size BIGINT,
    record_count INT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT
);
```

## Livewire Component Methods
```php
class ExportCenter extends Component
{
    public $activeTab = 'templates';
    
    public function mount() {}
    public function createTemplate() {}
    public function runExport($templateId) {}
    public function scheduleExport($templateId) {}
    public function downloadExport($jobId) {}
    public function deleteExport($jobId) {}
    public function testExport($templateId) {}
}
```

## Export Formats
- CSV with UTF-8 BOM
- Excel with multiple sheets
- PDF with headers/footers
- JSON for API integration
- XML for legacy systems