# House Management CRUD

## Component Structure
```
app/Livewire/HouseManager.php
app/Livewire/HouseShow.php
resources/views/livewire/house-manager.blade.php
resources/views/livewire/house-show.blade.php
```

## Features

### House List (`/houses`)
- Paginated table with advanced search
- Columns: house number, estate, meter number, occupancy status, current reading, last bill
- Quick actions: view, edit, add reading, generate bill
- Bulk operations: import readings, export data, send notifications
- Filters: estate, occupancy status, meter status, billing status
- Sort by: house number, last reading date, outstanding amount

### House Create/Edit (`/houses/create`, `/houses/{id}/edit`)
- Form fields:
  - House number (required, unique per estate)
  - Estate selection
  - Meter number (unique)
  - House type (apartment, villa, townhouse)
  - Bedrooms/bathrooms count
  - Square footage
  - Occupancy status
  - Special notes
- Validation with UniqueHouseNumber rule
- Meter number validation
- Estate-specific house numbering

### House Show (`/houses/{id}`)
- House overview dashboard
- Current occupant information
- Meter reading history with chart
- Billing history
- Payment status
- Consumption analytics
- Contact management
- Maintenance history
- Document attachments

### House Analytics
- Consumption patterns
- Billing trends
- Payment history
- Occupancy timeline
- Comparative analysis with similar houses

## Database Enhancements
```sql
-- Add to houses table
ALTER TABLE houses ADD COLUMN house_type ENUM('apartment', 'villa', 'townhouse', 'studio') DEFAULT 'apartment';
ALTER TABLE houses ADD COLUMN bedrooms INT NULL;
ALTER TABLE houses ADD COLUMN bathrooms INT NULL;
ALTER TABLE houses ADD COLUMN square_footage DECIMAL(8,2) NULL;
ALTER TABLE houses ADD COLUMN occupancy_status ENUM('occupied', 'vacant', 'maintenance') DEFAULT 'vacant';
ALTER TABLE houses ADD COLUMN notes TEXT NULL;
ALTER TABLE houses ADD COLUMN last_reading_date DATE NULL;
ALTER TABLE houses ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active';
```

## Livewire Component Methods
```php
class HouseManager extends Component
{
    public $selectedEstate = null;
    public $filters = [];
    
    public function mount() {}
    public function search() {}
    public function filterByEstate($estateId) {}
    public function create() {}
    public function edit($houseId) {}
    public function save() {}
    public function delete($houseId) {}
    public function addReading($houseId) {}
    public function generateBill($houseId) {}
    public function bulkImportReadings() {}
    public function exportHouses() {}
}

class HouseShow extends Component
{
    public House $house;
    public $activeTab = 'overview';
    
    public function mount($id) {}
    public function switchTab($tab) {}
    public function addContact() {}
    public function recordReading() {}
    public function generateInvoice() {}
    public function recordPayment() {}
    public function uploadDocument() {}
}
```

## Special Features

### Meter Reading Integration
- Quick reading entry from house list
- Reading validation against previous readings
- Automatic consumption calculation
- Anomaly detection and alerts

### Billing Integration
- One-click invoice generation
- Payment recording
- Outstanding balance tracking
- Payment plan management

### Contact Management
- Multiple contacts per house
- Primary contact designation
- Contact history tracking
- Communication preferences

## Permissions
- Management: Full CRUD access
- Reviewer: Read-only access, can add readings
- Caretaker: View assigned estate houses, add readings