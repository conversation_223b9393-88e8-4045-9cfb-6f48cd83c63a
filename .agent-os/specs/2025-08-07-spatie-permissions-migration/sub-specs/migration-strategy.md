# Migration Strategy

> Spec: Spatie Permissions Migration
> Sub-spec: Data Migration and Rollback Strategy
> Created: 2025-08-07
> Status: Planning

## Migration Overview

This document outlines the comprehensive strategy for migrating from the custom RBAC system to Spatie Laravel Permissions. The migration will be executed in phases to minimize risk and ensure data integrity.

## Pre-Migration Preparation

### 1. System Assessment
```php
// Assessment Checklist
$assessment = [
    'current_roles' => [
        'ADMIN' => 'System Administrator',
        'MANAGER' => 'Estate Manager', 
        'REVIEWER' => 'Accountant/Reviewer',
        'CARETAKER' => 'Field Staff/Caretaker',
        'RESIDENT' => 'Tenant/House Owner'
    ],
    'current_permissions_count' => 150,
    'current_users_count' => 'TBD',
    'current_estates_count' => 'TBD',
    'complex_permission_scenarios' => [
        'estate_based_access' => true,
        'hierarchical_management' => true,
        'user_overrides' => true,
        'permission_inheritance' => true
    ]
];
```

### 2. Backup Strategy
```bash
# Database backup
mysqldump -u username -p water_management > backup_before_migration.sql

# Code backup
git checkout -b custom-rbac-backup
git add .
git commit -m "Backup custom RBAC system before Spatie migration"

# File system backup
cp -r /path/to/water-management /path/to/water-management-backup
```

### 3. Migration Environment Setup
```php
// .env.migration
APP_ENV=migration
DB_DATABASE=water_management_migration
CACHE_DRIVER=file
SESSION_DRIVER=file
```

## Phase 1: Role Migration

### 1.1 Current Role Analysis
```php
// Current UserRole enum
enum UserRole: string
{
    case ADMIN = 'ADMIN';
    case MANAGER = 'MANAGER';
    case REVIEWER = 'REVIEWER';
    case CARETAKER = 'CARETAKER';
    case RESIDENT = 'RESIDENT';
}
```

### 1.2 Role Mapping Strategy
```php
// Role mapping configuration
$roleMapping = [
    'ADMIN' => [
        'spatie_role' => 'admin',
        'description' => 'System Administrator',
        'permissions' => ['*'], // All permissions
        'guard' => 'web'
    ],
    'MANAGER' => [
        'spatie_role' => 'manager',
        'description' => 'Estate Manager',
        'permissions' => [
            'view estates', 'create estates', 'edit estates',
            'view houses', 'create houses', 'edit houses',
            'view contacts', 'create contacts', 'edit contacts',
            'view readings', 'approve readings',
            'view invoices', 'create invoices', 'approve invoices',
            'view reports', 'generate reports',
            'view team members', 'manage team assignments'
        ],
        'guard' => 'web'
    ],
    'REVIEWER' => [
        'spatie_role' => 'reviewer',
        'description' => 'Accountant/Reviewer',
        'permissions' => [
            'view estates', 'view houses', 'view contacts',
            'view readings', 'review readings', 'approve readings',
            'view invoices', 'create invoices', 'edit invoices',
            'view reports', 'generate reports'
        ],
        'guard' => 'web'
    ],
    'CARETAKER' => [
        'spatie_role' => 'caretaker',
        'description' => 'Field Staff/Caretaker',
        'permissions' => [
            'view estates', 'view houses', 'view contacts',
            'create readings', 'edit readings',
            'create contacts', 'edit contacts'
        ],
        'guard' => 'web'
    ],
    'RESIDENT' => [
        'spatie_role' => 'resident',
        'description' => 'Tenant/House Owner',
        'permissions' => [
            'view own invoices',
            'view own readings',
            'view own consumption',
            'submit inquiries',
            'manage profile'
        ],
        'guard' => 'web'
    ]
];
```

### 1.3 Role Migration Script
```php
// database/migrations/2025_08_07_migrate_roles.php
class MigrateRoles extends Migration
{
    public function up()
    {
        $roleMapping = [
            'ADMIN' => 'admin',
            'MANAGER' => 'manager',
            'REVIEWER' => 'reviewer',
            'CARETAKER' => 'caretaker',
            'RESIDENT' => 'resident'
        ];
        
        foreach ($roleMapping as $oldRole => $newRole) {
            // Check if role already exists
            if (!Role::where('name', $newRole)->exists()) {
                Role::create([
                    'name' => $newRole,
                    'guard_name' => 'web'
                ]);
            }
        }
    }
    
    public function down()
    {
        $roles = ['admin', 'manager', 'reviewer', 'caretaker', 'resident'];
        
        foreach ($roles as $role) {
            Role::where('name', $role)->delete();
        }
    }
}
```

### 1.4 User Role Assignment Migration
```php
// database/migrations/2025_08_07_migrate_user_roles.php
class MigrateUserRoles extends Migration
{
    public function up()
    {
        $roleMapping = [
            'ADMIN' => 'admin',
            'MANAGER' => 'manager',
            'REVIEWER' => 'reviewer',
            'CARETAKER' => 'caretaker',
            'RESIDENT' => 'resident'
        ];
        
        $users = User::all();
        
        foreach ($users as $user) {
            if (isset($roleMapping[$user->role])) {
                $spatieRole = Role::where('name', $roleMapping[$user->role])->first();
                if ($spatieRole) {
                    $user->assignRole($spatieRole);
                }
            }
        }
    }
    
    public function down()
    {
        $reverseMapping = [
            'admin' => 'ADMIN',
            'manager' => 'MANAGER',
            'reviewer' => 'REVIEWER',
            'caretaker' => 'CARETAKER',
            'resident' => 'RESIDENT'
        ];
        
        $users = User::all();
        
        foreach ($users as $user) {
            $user->roles()->detach();
            $user->role = null;
            $user->save();
        }
    }
}
```

## Phase 2: Permission Migration

### 2.1 Current Permission Analysis
```php
// Current permission structure analysis
$currentPermissions = [
    'system' => [
        'view_system_settings',
        'manage_system_settings',
        'view_audit_logs',
        'manage_users',
        'manage_roles',
        'manage_permissions'
    ],
    'estate' => [
        'view_estates',
        'create_estates',
        'edit_estates',
        'delete_estates',
        'manage_estate_assignments'
    ],
    'house' => [
        'view_houses',
        'create_houses',
        'edit_houses',
        'delete_houses',
        'manage_house_accounts'
    ],
    'contact' => [
        'view_contacts',
        'create_contacts',
        'edit_contacts',
        'delete_contacts',
        'manage_contact_assignments'
    ],
    'reading' => [
        'view_readings',
        'create_readings',
        'edit_readings',
        'delete_readings',
        'approve_readings',
        'review_readings'
    ],
    'billing' => [
        'view_invoices',
        'create_invoices',
        'edit_invoices',
        'delete_invoices',
        'approve_invoices',
        'manage_billing_settings'
    ],
    'reports' => [
        'view_reports',
        'generate_reports',
        'export_reports',
        'view_analytics',
        'view_aging_reports',
        'view_revenue_reports'
    ],
    'team' => [
        'view_team_members',
        'manage_team_assignments',
        'view_team_performance'
    ],
    'resident' => [
        'view_own_invoices',
        'view_own_readings',
        'view_own_consumption',
        'submit_inquiries',
        'manage_profile'
    ]
];
```

### 2.2 Permission Creation Script
```php
// database/migrations/2025_08_07_create_permissions.php
class CreatePermissions extends Migration
{
    public function up()
    {
        $permissions = [
            // System permissions
            'view system settings',
            'manage system settings',
            'view audit logs',
            'manage users',
            'manage roles',
            'manage permissions',
            
            // Estate permissions
            'view estates',
            'create estates',
            'edit estates',
            'delete estates',
            'manage estate assignments',
            
            // House permissions
            'view houses',
            'create houses',
            'edit houses',
            'delete houses',
            'manage house accounts',
            
            // Contact permissions
            'view contacts',
            'create contacts',
            'edit contacts',
            'delete contacts',
            'manage contact assignments',
            
            // Reading permissions
            'view readings',
            'create readings',
            'edit readings',
            'delete readings',
            'approve readings',
            'review readings',
            
            // Billing permissions
            'view invoices',
            'create invoices',
            'edit invoices',
            'delete invoices',
            'approve invoices',
            'manage billing settings',
            
            // Report permissions
            'view reports',
            'generate reports',
            'export reports',
            'view analytics',
            'view aging reports',
            'view revenue reports',
            
            // Team permissions
            'view team members',
            'manage team assignments',
            'view team performance',
            
            // Resident permissions
            'view own invoices',
            'view own readings',
            'view own consumption',
            'submit inquiries',
            'manage profile'
        ];
        
        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web'
            ]);
        }
    }
    
    public function down()
    {
        Permission::query()->delete();
    }
}
```

### 2.3 Role-Permission Assignment
```php
// database/migrations/2025_08_07_assign_role_permissions.php
class AssignRolePermissions extends Migration
{
    public function up()
    {
        // Admin role - all permissions
        $adminRole = Role::where('name', 'admin')->first();
        $adminRole->givePermissionTo(Permission::all());
        
        // Manager role permissions
        $managerRole = Role::where('name', 'manager')->first();
        $managerPermissions = [
            'view estates', 'create estates', 'edit estates',
            'view houses', 'create houses', 'edit houses',
            'view contacts', 'create contacts', 'edit contacts',
            'view readings', 'approve readings',
            'view invoices', 'create invoices', 'approve invoices',
            'view reports', 'generate reports',
            'view team members', 'manage team assignments'
        ];
        $managerRole->givePermissionTo($managerPermissions);
        
        // Reviewer role permissions
        $reviewerRole = Role::where('name', 'reviewer')->first();
        $reviewerPermissions = [
            'view estates', 'view houses', 'view contacts',
            'view readings', 'review readings', 'approve readings',
            'view invoices', 'create invoices', 'edit invoices',
            'view reports', 'generate reports'
        ];
        $reviewerRole->givePermissionTo($reviewerPermissions);
        
        // Caretaker role permissions
        $caretakerRole = Role::where('name', 'caretaker')->first();
        $caretakerPermissions = [
            'view estates', 'view houses', 'view contacts',
            'create readings', 'edit readings',
            'create contacts', 'edit contacts'
        ];
        $caretakerRole->givePermissionTo($caretakerPermissions);
        
        // Resident role permissions
        $residentRole = Role::where('name', 'resident')->first();
        $residentPermissions = [
            'view own invoices',
            'view own readings',
            'view own consumption',
            'submit inquiries',
            'manage profile'
        ];
        $residentRole->givePermissionTo($residentPermissions);
    }
    
    public function down()
    {
        $roles = Role::all();
        foreach ($roles as $role) {
            $role->permissions()->detach();
        }
    }
}
```

## Phase 3: User Permission Migration

### 3.1 User Permission Override Migration
```php
// database/migrations/2025_08_07_migrate_user_permissions.php
class MigrateUserPermissions extends Migration
{
    public function up()
    {
        // Migrate user permission overrides from custom system
        $userOverrides = DB::table('user_permission_overrides')->get();
        
        foreach ($userOverrides as $override) {
            $user = User::find($override->user_id);
            $permission = Permission::where('name', $override->permission_name)->first();
            
            if ($user && $permission) {
                if ($override->granted) {
                    $user->givePermissionTo($permission);
                } else {
                    $user->revokePermissionTo($permission);
                }
            }
        }
    }
    
    public function down()
    {
        // Remove all direct user permissions
        $users = User::all();
        foreach ($users as $user) {
            $user->permissions()->detach();
        }
    }
}
```

### 3.2 Estate Assignment Migration
```php
// database/migrations/2025_08_07_migrate_estate_assignments.php
class MigrateEstateAssignments extends Migration
{
    public function up()
    {
        // Estate assignments remain the same, but we need to ensure
        // they work with the new permission system
        $assignments = DB::table('user_estate_assignments')->get();
        
        foreach ($assignments as $assignment) {
            $user = User::find($assignment->user_id);
            $estate = Estate::find($assignment->estate_id);
            
            if ($user && $estate) {
                // The existing relationship should work with Spatie
                // We just need to ensure the user has the base permissions
                // Estate-specific filtering will be handled in the services
            }
        }
    }
    
    public function down()
    {
        // No changes needed for rollback
    }
}
```

## Phase 4: Data Validation

### 4.1 Migration Validation Script
```php
// app/Console/Commands/ValidateMigration.php
class ValidateMigration extends Command
{
    protected $signature = 'migration:validate';
    protected $description = 'Validate Spatie permissions migration';
    
    public function handle()
    {
        $this->info('Validating migration...');
        
        // Check roles
        $expectedRoles = ['admin', 'manager', 'reviewer', 'caretaker', 'resident'];
        $actualRoles = Role::pluck('name')->toArray();
        
        if (array_diff($expectedRoles, $actualRoles)) {
            $this->error('Missing roles: ' . implode(', ', array_diff($expectedRoles, $actualRoles)));
            return 1;
        }
        
        // Check permissions
        $expectedPermissionCount = 42; // Total permissions
        $actualPermissionCount = Permission::count();
        
        if ($actualPermissionCount !== $expectedPermissionCount) {
            $this->error("Expected {$expectedPermissionCount} permissions, found {$actualPermissionCount}");
            return 1;
        }
        
        // Check user role assignments
        $usersWithoutRoles = User::doesntHave('roles')->count();
        if ($usersWithoutRoles > 0) {
            $this->error("{$usersWithoutRoles} users without roles");
            return 1;
        }
        
        $this->info('Migration validation completed successfully');
        return 0;
    }
}
```

### 4.2 Data Integrity Checks
```php
// tests/Feature/MigrationValidationTest.php
class MigrationValidationTest extends TestCase
{
    public function test_all_roles_migrated()
    {
        $expectedRoles = ['admin', 'manager', 'reviewer', 'caretaker', 'resident'];
        $actualRoles = Role::pluck('name')->toArray();
        
        $this->assertEqualsCanonicalizing($expectedRoles, $actualRoles);
    }
    
    public function test_admin_has_all_permissions()
    {
        $admin = User::role('admin')->first();
        $allPermissions = Permission::all();
        
        $this->assertTrue($admin->hasAllPermissions($allPermissions));
    }
    
    public function test_user_role_assignments_preserved()
    {
        $users = User::all();
        
        foreach ($users as $user) {
            $this->assertGreaterThan(0, $user->roles()->count());
        }
    }
    
    public function test_estate_assignments_preserved()
    {
        $assignments = DB::table('user_estate_assignments')->count();
        $this->assertGreaterThan(0, $assignments);
    }
}
```

## Rollback Strategy

### 5.1 Rollback Scripts
```php
// database/migrations/2025_08_07_rollback_spatie_migration.php
class RollbackSpatieMigration extends Migration
{
    public function up()
    {
        // Restore original user role column
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['ADMIN', 'MANAGER', 'REVIEWER', 'CARETAKER', 'RESIDENT'])->after('id');
        });
        
        // Restore user roles from Spatie
        $users = User::all();
        foreach ($users as $user) {
            $role = $user->roles()->first();
            if ($role) {
                $reverseMapping = [
                    'admin' => 'ADMIN',
                    'manager' => 'MANAGER',
                    'reviewer' => 'REVIEWER',
                    'caretaker' => 'CARETAKER',
                    'resident' => 'RESIDENT'
                ];
                
                $user->role = $reverseMapping[$role->name] ?? null;
                $user->save();
            }
        }
        
        // Drop Spatie tables
        Schema::dropIfExists('model_has_permissions');
        Schema::dropIfExists('model_has_roles');
        Schema::dropIfExists('role_has_permissions');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('roles');
    }
    
    public function down()
    {
        // Re-run migration if needed
    }
}
```

### 5.2 Rollback Validation
```php
// app/Console/Commands/ValidateRollback.php
class ValidateRollback extends Command
{
    protected $signature = 'migration:validate-rollback';
    protected $description = 'Validate rollback from Spatie permissions';
    
    public function handle()
    {
        $this->info('Validating rollback...');
        
        // Check user role column
        if (!Schema::hasColumn('users', 'role')) {
            $this->error('User role column not restored');
            return 1;
        }
        
        // Check all users have roles
        $usersWithoutRoles = User::whereNull('role')->count();
        if ($usersWithoutRoles > 0) {
            $this->error("{$usersWithoutRoles} users without roles");
            return 1;
        }
        
        // Check Spatie tables are gone
        $spatieTables = ['roles', 'permissions', 'model_has_roles', 'model_has_permissions', 'role_has_permissions'];
        foreach ($spatieTables as $table) {
            if (Schema::hasTable($table)) {
                $this->error("Spatie table {$table} still exists");
                return 1;
            }
        }
        
        $this->info('Rollback validation completed successfully');
        return 0;
    }
}
```

## Migration Execution Plan

### Day 1: Preparation
- [ ] Create database backup
- [ ] Set up migration environment
- [ ] Install Spatie package
- [ ] Run initial analysis

### Day 2: Role Migration
- [ ] Run role migration
- [ ] Validate role creation
- [ ] Migrate user roles
- [ ] Test role assignments

### Day 3: Permission Migration
- [ ] Create permissions
- [ ] Assign role permissions
- [ ] Migrate user permissions
- [ ] Validate permission structure

### Day 4: Validation and Testing
- [ ] Run migration validation
- [ ] Execute test suite
- [ ] Performance testing
- [ ] Bug fixes

### Day 5: Finalization
- [ ] Documentation updates
- [ ] Team training
- [ ] Production deployment plan
- [ ] Go/no-go decision

## Risk Mitigation

### High-Risk Areas
1. **Data Loss**: Comprehensive backup and validation
2. **Downtime**: Execute during maintenance window
3. **Performance**: Monitor and optimize queries
4. **Compatibility**: Test all components thoroughly

### Contingency Plans
1. **Immediate Rollback**: If critical issues arise
2. **Parallel Systems**: Run both systems temporarily
3. **Gradual Rollout**: Migrate users in batches
4. **Feature Flags**: Enable/disable new system selectively

## Success Criteria

### Migration Success
- All data migrated without loss
- All functionality preserved
- Performance improved
- Tests passing

### Rollback Success
- System restored to original state
- No data corruption
- All functionality working
- Minimal downtime