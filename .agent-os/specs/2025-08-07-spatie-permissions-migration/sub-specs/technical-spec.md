# Technical Specification

> Spec: Spatie Permissions Migration
> Sub-spec: Technical Implementation Details
> Created: 2025-08-07
> Status: Planning

## Current System Analysis

### Existing RBAC Architecture

The current water management system uses a custom RBAC implementation with the following components:

#### 1. Role System
- **UserRole Enum**: Defines ADMIN, MANAGER, REVI<PERSON><PERSON><PERSON>, CARETAKER, RESIDENT roles
- **User Model**: Contains `role` column with enum values
- **Role-based routing**: Different route prefixes for each role (`/admin`, `/management`, `/reviewer`, `/caretaker`, `/resident`)

#### 2. Permission System
- **Custom Permissions Table**: Stores permission definitions
- **Role Permissions Table**: Maps roles to permissions
- **User Permission Overrides**: Individual user permission exceptions
- **Permission Audit Logs**: Tracks permission changes
- **Estate Assignments**: Users assigned to specific estates
- **Management Hierarchy**: Organizational structure

#### 3. Permission Validation
- **PermissionValidationService**: Centralized permission checking
- **Custom Gates**: Laravel gate definitions for authorization
- **Middleware**: Role-based middleware for route protection
- **Component-level Checks**: Permission-dependent UI rendering

#### 4. Estate-Based Access Control
- **User Estate Assignments**: Many-to-many relationship between users and estates
- **Estate-Scoped Queries**: All data access filtered by assigned estates
- **Hierarchical Management**: Managers oversee reviewers and caretakers

### Current Pain Points

1. **Complex Testing**: Permission-dependent tests require extensive mocking
2. **Performance Issues**: Complex permission checking impacts performance
3. **Maintenance Overhead**: Custom code requires ongoing maintenance
4. **Limited Tooling**: No built-in admin interface for permission management
5. **Inconsistent Patterns**: Different permission checking approaches across components

## Spatie Permissions Integration Plan

### Package Configuration

#### 1. Installation
```bash
composer require spatie/laravel-permission
```

#### 2. Configuration Setup
```php
// config/permission.php
return [
    'models' => [
        'permission' => Spatie\Permission\Models\Permission::class,
        'role' => Spatie\Permission\Models\Role::class,
    ],
    'table_names' => [
        'roles' => 'roles',
        'permissions' => 'permissions',
        'model_has_permissions' => 'model_has_permissions',
        'model_has_roles' => 'model_has_roles',
        'role_has_permissions' => 'role_has_permissions',
    ],
    'column_names' => [
        'role_pivot_key' => 'role_id',
        'permission_pivot_key' => 'permission_id',
        'model_morph_key' => 'model_id',
        'model_has_roles_role_pivot_key' => 'role_id',
    ],
    'register_permission_check_method' => true,
    'cache' => [
        'expiration_time' => \DateInterval::createFromDateString('24 hours'),
        'key' => 'spatie.permission.cache',
        'model_key' => 'spatie.permission.cache.model.',
    ],
];
```

#### 3. Model Integration
```php
// app/Models/User.php
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;
    
    // Remove existing role column and relationships
    // Add Spatie role and permission methods
}
```

### Permission Structure Design

#### 1. Role Definition
```php
// Roles to create
$roles = [
    'admin' => 'System Administrator',
    'manager' => 'Estate Manager', 
    'reviewer' => 'Accountant/Reviewer',
    'caretaker' => 'Field Staff/Caretaker',
    'resident' => 'Tenant/House Owner'
];
```

#### 2. Permission Structure
```php
// Permission groups
$permissions = [
    // System Administration
    'system' => [
        'view system settings',
        'manage system settings',
        'view audit logs',
        'manage users',
        'manage roles',
        'manage permissions',
    ],
    
    // Estate Management
    'estate' => [
        'view estates',
        'create estates',
        'edit estates',
        'delete estates',
        'manage estate assignments',
    ],
    
    // House Management
    'house' => [
        'view houses',
        'create houses',
        'edit houses',
        'delete houses',
        'manage house accounts',
    ],
    
    // Contact Management
    'contact' => [
        'view contacts',
        'create contacts',
        'edit contacts',
        'delete contacts',
        'manage contact assignments',
    ],
    
    // Meter Reading Management
    'reading' => [
        'view readings',
        'create readings',
        'edit readings',
        'delete readings',
        'approve readings',
        'review readings',
    ],
    
    // Billing and Invoicing
    'billing' => [
        'view invoices',
        'create invoices',
        'edit invoices',
        'delete invoices',
        'approve invoices',
        'manage billing settings',
    ],
    
    // Reports and Analytics
    'reports' => [
        'view reports',
        'generate reports',
        'export reports',
        'view analytics',
        'view aging reports',
        'view revenue reports',
    ],
    
    // Team Management
    'team' => [
        'view team members',
        'manage team assignments',
        'view team performance',
    ],
    
    // Resident Portal
    'resident' => [
        'view own invoices',
        'view own readings',
        'view own consumption',
        'submit inquiries',
        'manage profile',
    ],
];
```

#### 3. Estate-Based Permissions
```php
// Custom permission handling for estate-based access
class EstatePermissionService
{
    public function hasEstatePermission(User $user, string $permission, Estate $estate): bool
    {
        if ($user->hasRole('admin')) {
            return true;
        }
        
        if (!$user->assignedEstates()->where('id', $estate->id)->exists()) {
            return false;
        }
        
        return $user->hasPermissionTo($permission);
    }
    
    public function getEstateScopedPermissions(User $user): array
    {
        $basePermissions = $user->getAllPermissions();
        
        return $basePermissions->filter(function ($permission) use ($user) {
            return $this->isEstateScopedPermission($permission);
        })->toArray();
    }
}
```

### Migration Strategy

#### 1. Data Migration Scripts
```php
// database/migrations/2025_08_07_migrate_to_spatie_permissions.php
class MigrateToSpatiePermissions extends Migration
{
    public function up()
    {
        // Create Spatie tables
        $this->call('vendor:publish', ['--provider' => 'Spatie\Permission\PermissionServiceProvider']);
        $this->call('migrate');
        
        // Migrate roles
        $this->migrateRoles();
        
        // Migrate permissions
        $this->migratePermissions();
        
        // Migrate user roles
        $this->migrateUserRoles();
        
        // Migrate user permissions
        $this->migrateUserPermissions();
        
        // Clean up old tables
        $this->cleanupOldTables();
    }
    
    protected function migrateRoles()
    {
        $roleMapping = [
            'ADMIN' => 'admin',
            'MANAGER' => 'manager',
            'REVIEWER' => 'reviewer',
            'CARETAKER' => 'caretaker',
            'RESIDENT' => 'resident'
        ];
        
        foreach ($roleMapping as $oldRole => $newRole) {
            Role::create([
                'name' => $newRole,
                'guard_name' => 'web'
            ]);
        }
    }
}
```

#### 2. Service Layer Refactoring
```php
// app/Services/PermissionValidationService.php
class PermissionValidationService
{
    public function canAccessEstate(User $user, Estate $estate): bool
    {
        if ($user->hasRole('admin')) {
            return true;
        }
        
        return $user->assignedEstates()->where('id', $estate->id)->exists();
    }
    
    public function canManageUsers(User $user, ?Estate $estate = null): bool
    {
        if ($user->hasRole('admin')) {
            return true;
        }
        
        if ($user->hasRole('manager')) {
            return $estate ? $this->canAccessEstate($user, $estate) : true;
        }
        
        return false;
    }
    
    public function canViewReports(User $user, ?Estate $estate = null): bool
    {
        return $user->hasAnyPermission(['view reports', 'view analytics']);
    }
}
```

#### 3. Controller Updates
```php
// app/Http/Controllers/Admin/EstateController.php
class EstateController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view estates');
        $this->middleware('permission:create estates')->only(['create', 'store']);
        $this->middleware('permission:edit estates')->only(['edit', 'update']);
        $this->middleware('permission:delete estates')->only(['destroy']);
    }
    
    public function index()
    {
        $estates = Estate::query();
        
        if (!auth()->user()->hasRole('admin')) {
            $estates->whereHas('assignedUsers', function ($query) {
                $query->where('user_id', auth()->id());
            });
        }
        
        return view('admin.estates.index', compact('estates'));
    }
}
```

#### 4. Livewire Component Updates
```php
// app/Livewire/Admin/Sidebar.php
class AdminSidebar extends Component
{
    public function render()
    {
        $user = auth()->user();
        
        return view('livewire.admin.sidebar', [
            'canViewSystemSettings' => $user->can('view system settings'),
            'canManageUsers' => $user->can('manage users'),
            'canViewReports' => $user->can('view reports'),
            'canManageEstates' => $user->can('view estates'),
        ]);
    }
}
```

### Testing Strategy

#### 1. Test Helpers
```php
// tests/Helpers/PermissionTestHelpers.php
trait PermissionTestHelpers
{
    protected function createUserWithPermissions(array $permissions, array $roles = [])
    {
        $user = User::factory()->create();
        
        foreach ($roles as $role) {
            $user->assignRole($role);
        }
        
        foreach ($permissions as $permission) {
            $user->givePermissionTo($permission);
        }
        
        return $user;
    }
    
    protected function assertUserCanAccessRoute(User $user, string $route)
    {
        $response = $this->actingAs($user)->get($route);
        $response->assertStatus(200);
    }
    
    protected function assertUserCannotAccessRoute(User $user, string $route)
    {
        $response = $this->actingAs($user)->get($route);
        $response->assertStatus(403);
    }
}
```

#### 2. Updated Test Examples
```php
// tests/Feature/PermissionTest.php
class PermissionTest extends TestCase
{
    use PermissionTestHelpers;
    
    public function test_admin_can_access_all_estates()
    {
        $admin = $this->createUserWithPermissions([], ['admin']);
        $estate = Estate::factory()->create();
        
        $this->assertUserCanAccessRoute($admin, "/admin/estates/{$estate->id}/edit");
    }
    
    public function test_manager_can_only_access_assigned_estates()
    {
        $manager = $this->createUserWithPermissions(['view estates'], ['manager']);
        $assignedEstate = Estate::factory()->create();
        $unassignedEstate = Estate::factory()->create();
        
        $manager->assignedEstates()->attach($assignedEstate);
        
        $this->assertUserCanAccessRoute($manager, "/management/estates/{$assignedEstate->id}/edit");
        $this->assertUserCannotAccessRoute($manager, "/management/estates/{$unassignedEstate->id}/edit");
    }
}
```

### Performance Optimization

#### 1. Caching Configuration
```php
// config/permission.php
'cache' => [
    'expiration_time' => \DateInterval::createFromDateString('6 hours'),
    'key' => 'spatie.permission.cache',
    'model_key' => 'spatie.permission.cache.model.',
],
```

#### 2. Query Optimization
```php
// app/Providers/AppServiceProvider.php
public function boot()
{
    // Optimize permission queries
    Permission::retrieved(function ($permission) {
        $permission->loadMissing('roles');
    });
    
    // Cache user permissions
    User::retrieved(function ($user) {
        $user->loadMissing(['roles', 'permissions']);
    });
}
```

### Rollback Strategy

#### 1. Rollback Migration
```php
// database/migrations/2025_08_07_rollback_spatie_permissions.php
class RollbackSpatiePermissions extends Migration
{
    public function up()
    {
        // Restore original tables
        Schema::create('permissions', function (Blueprint $table) {
            // Original schema
        });
        
        // Restore user role column
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['ADMIN', 'MANAGER', 'REVIEWER', 'CARETAKER', 'RESIDENT'])->after('id');
        });
        
        // Restore user role assignments
        $this->restoreUserRoles();
        
        // Drop Spatie tables
        Schema::dropIfExists('model_has_permissions');
        Schema::dropIfExists('model_has_roles');
        Schema::dropIfExists('role_has_permissions');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('roles');
    }
}
```

#### 2. Code Rollback
- Maintain feature branch with custom RBAC implementation
- Use Git for easy rollback if needed
- Document all changes for easy reversion

## Implementation Timeline

### Week 1: Analysis and Setup
- Days 1-2: Current system analysis
- Days 3-4: Package installation and configuration
- Day 5: Initial testing and validation

### Week 2: Data Migration
- Days 1-2: Role migration
- Days 3-4: Permission structure migration
- Day 5: User permission migration

### Week 3: Code Refactoring
- Days 1-2: Service layer updates
- Days 3-4: Controller updates
- Day 5: Component updates

### Week 4: Testing and Optimization
- Days 1-2: Test suite updates
- Days 3-4: Performance optimization
- Day 5: Documentation and final validation

## Success Criteria

### Technical Metrics
- All existing functionality preserved
- Permission checking performance improved by 20%
- Test execution time reduced by 15%
- Zero data loss during migration

### Quality Metrics
- Code complexity reduced
- Test coverage maintained or improved
- Developer satisfaction increased
- Maintenance overhead reduced

### Risk Mitigation
- Comprehensive rollback strategy
- Incremental migration approach
- Extensive testing at each phase
- Documentation for all changes