# Tests Specification

This is the tests specification for @.agent-os/specs/2025-07-31-resident-portal-spec/spec.md

> Created: 2025-07-31
> Version: 1.0.0

## Unit Tests
- **Models:**
    - `User` (if extended for resident roles): Test scope methods for residents, relationships to `House`, `Invoice`, `MeterReading`, `WhatsAppMessage`.
    - `Inquiry` (if new model created): Test relationships and attribute casting.
- **Services:**
    - `ResidentAuthService` (if created): Test resident login/logout logic.
    - `ResidentDataService` (if created): Test data retrieval methods for dashboard, invoices, messages, meter readings, ensuring data isolation.
    - `InquiryService` (if created): Test inquiry submission and routing logic.

## Integration Tests
- **Authentication:**
    - Test resident login and logout.
    - Test access control to resident portal routes (authenticated vs. unauthenticated).
- **Livewire Components:**
    - `ResidentDashboard`: Test data display.
    - `ResidentInvoiceHistory`: Test data display, pagination, and PDF download.
    - `ResidentMessageHistory`: Test data display and message content viewing.
    - `ResidentMeterReadingHistory`: Test data display and chronological order.
    - `ResidentInquiryForm`: Test form submission and validation.
- **API Endpoints (if implemented):**
    - Test all new API endpoints for resident authentication and data retrieval, ensuring proper authorization and data filtering.

## Feature Tests
- **End-to-End Scenarios:**
    - As a Resident, I can register (if enabled) and log in to the portal.
    - As a Resident, I can view my dashboard summary.
    - As a Resident, I can navigate to my invoice history, view details of an invoice, and download its PDF.
    - As a Resident, I can view my message history.
    - As a Resident, I can view my meter reading history.
    - As a Resident, I can submit an inquiry through the portal and verify it is received by management.
    - Test unauthorized access attempts to resident data.

## Mocking Requirements
- **External Services:** Mock any external services used (e.g., for sending inquiry notifications).
- **Date/Time:** Mock `now()` for consistent date-based testing.
