# API Specification

This is the API specification for @.agent-os/specs/2025-07-31-resident-portal-spec/spec.md

> Created: 2025-07-31
> Version: 1.0.0

## New API Endpoints Needed
While Livewire handles most interactions, dedicated API endpoints might be beneficial for:

-   **Resident Authentication:**
    -   `POST /api/resident/login`: Authenticate resident and return token.
    -   `POST /api/resident/logout`: Invalidate resident session/token.
-   **Resident Data Retrieval:**
    -   `GET /api/resident/dashboard`: Get summary data for the resident dashboard.
    -   `GET /api/resident/invoices`: Get a list of invoices for the authenticated resident.
    -   `GET /api/resident/invoices/{invoice_id}`: Get details of a specific invoice.
    -   `GET /api/resident/messages`: Get a list of messages for the authenticated resident.
    -   `GET /api/resident/meter-readings`: Get meter reading history for the authenticated resident's house.
-   **Inquiry Submission:**
    -   `POST /api/resident/inquiries`: Submit a new inquiry from the resident.

## Controllers and Actions
-   `ResidentAuthController`: Handles login/logout.
-   `ResidentDashboardController`: Provides data for the dashboard.
-   `ResidentInvoiceController`: Manages invoice retrieval.
-   `ResidentMessageController`: Manages message retrieval.
-   `ResidentMeterReadingController`: Manages meter reading history retrieval.
-   `ResidentInquiryController`: Handles inquiry submission.

## Error Handling
-   Standard Laravel API error responses (e.g., 401 Unauthorized, 403 Forbidden, 404 Not Found, 422 Unprocessable Entity for validation errors).
-   Ensure clear error messages for residents.
