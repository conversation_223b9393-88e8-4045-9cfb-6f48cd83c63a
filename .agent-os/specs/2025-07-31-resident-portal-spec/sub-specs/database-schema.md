# Database Schema

This is the database schema specification for @.agent-os/specs/2025-07-31-resident-portal-spec/spec.md

> Created: 2025-07-31
> Version: 1.0.0

## Existing Models/Tables to Utilize
- `users` table (Model: `App\Models\User`) - Will likely be extended for resident roles.
- `invoices` table (Model: `App\Models\Invoice`)
- `meter_readings` table (Model: `App\Models\MeterReading`)
- `whatsapp_messages` table (Model: `App\Models\WhatsAppMessage`)
- `contacts` table (Model: `App\Models\Contact`) - For inquiry submission.
- `houses` table (Model: `App\Models\House`) - To link residents to their houses.

## Potential Database Changes Needed
- **User Roles:** Ensure the `users` table and `UserRole` enum can differentiate between staff and residents. This might involve adding a `role` column or refining existing role management.
- **Resident-House Linkage:** Confirm a clear and robust linkage between a `User` (resident) and their `House`. The `house_contacts` table might be relevant here.
- **Inquiry System:** If a new dedicated inquiry system is needed beyond the existing `ContactManager`, a new table (e.g., `inquiries`) might be required to store resident submissions.

**Example Migration (if new inquiry table is needed):**
```php
Schema::create('inquiries', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Resident user
    $table->foreignId('house_id')->nullable()->constrained()->onDelete('set null');
    $table->string('subject');
    $table->text('message');
    $table->string('status')->default('open'); // e.g., open, in_progress, closed
    $table->timestamps();
});
