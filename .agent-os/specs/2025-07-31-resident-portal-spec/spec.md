# Spec Requirements Document

> Spec: Resident/Tenant Portal
> Created: 2025-07-31
> Status: Enhanced

## Overview
This spec outlines the development of a dedicated Resident/Tenant Portal, providing residents with direct access to view their specific invoices, messages, meter reading history, and submit inquiries.

## User Stories
### Resident Authentication and Dashboard
As a Resident, I want to securely log in to a dedicated portal, so that I can access my personal water management information.
Detailed workflow description: Residents will have a separate login interface. Upon successful login, they will be directed to a personalized dashboard showing a summary of their current invoice, recent meter readings, and unread messages.

### Invoice Viewing
As a Resident, I want to view my specific invoices, so that I can understand my billing details and payment status.
Detailed workflow description: Residents should be able to see a list of their past and current invoices, with options to view detailed line items, download PDF copies, and see payment history.

### Message Viewing
As a Resident, I want to view messages sent to me by the management, so that I can stay informed about important updates.
Detailed workflow description: A section in the portal should display a list of messages (e.g., WhatsApp messages, system notifications) sent to the resident, with the ability to view full content.

### Meter Reading History
As a Resident, I want to view my meter reading history, so that I can track my water consumption over time.
Detailed workflow description: A dedicated view should show a chronological list or graph of the resident's meter readings, including date and reading value.

### Contact Profile Management
As a Resident, I want to view and edit my personal contact information, so that I can keep my details up to date and manage communication preferences.
Detailed workflow description: A profile page should display the resident's personal information (name, email, phone, etc.) along with their property details (estate, house, address). Residents should be able to edit their contact details while property information remains read-only.

### Inquiry Submission
As a Resident, I want to submit inquiries or support requests to the management, so that I can get help with billing issues or other concerns.
Detailed workflow description: A form should be available for residents to submit text-based inquiries, which will be routed to the appropriate management staff (e.g., via the existing ContactManager or a new inquiry system).

## Spec Scope
1. **Resident Authentication:** Implement a separate login flow for residents.
2. **Resident Dashboard:** Create a personalized dashboard summarizing key information.
3. **Invoice History View:** Display a list of resident's invoices with detail view and PDF download.
4. **Message History View:** Display a list of messages sent to the resident.
5. **Meter Reading History View:** Display historical meter readings for the resident's house.
6. **Contact Profile Management:** Provide a profile page for residents to view and edit their contact information.
7. **Inquiry Submission Form:** Provide a form for residents to submit questions/issues.

## Out of Scope
- Online payment processing within the portal (M-Pesa integration is a separate Phase 4 feature).
- Complex two-way communication/chat system.
- Full CRUD contact management (residents can only edit their own profile).

## Expected Deliverable
1. A secure, dedicated web portal for residents.
2. Dashboard with key summaries.
3. Views for invoice history, message history, and meter reading history.
4. A contact profile page for residents to manage their personal information.
5. A functional form for submitting inquiries.
6. Integration with existing data models (`User`, `Invoice`, `MeterReading`, `WhatsAppMessage`, `Contact`).

## Spec Documentation
- Tasks: @.agent-os/specs/2025-07-31-resident-portal-spec/tasks.md
- Technical Specification: @.agent-os/specs/2025-07-31-resident-portal-spec/sub-specs/technical-spec.md
- Database Schema: @.agent-os/specs/2025-07-31-resident-portal-spec/sub-specs/database-schema.md
- API Spec: @.agent-os/specs/2025-07-31-resident-portal-spec/sub-specs/api-spec.md
- Tests Spec: @.agent-os/specs/2025-07-31-resident-portal-spec/sub-specs/tests.md
