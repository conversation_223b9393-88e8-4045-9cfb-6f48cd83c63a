# Technical Stack

> Last Updated: 2025-07-27
> Version: 1.0.0

- **Application Framework:** Laravel 12
- **JavaScript Framework:** Livewire, Tailadmin
- **CSS Framework:** Tailwind CSS 4
- **Build Tool:** Vite
- **Package Manager:** Bun
- **Database System:** SQLite (development), MySQL/PostgreSQL (production)
- **ORM:** Eloquent
- **UI Component Library:** Tailadmin
- **Testing:** Pest or PHPunit 12+
- **Fonts Provider:** n/a
- **Icon Library:** Flux:icon with lucide
- **Application Hosting:** n/a
- **Database Hosting:** n/a
- **Asset Hosting:** n/a
- **Deployment Solution:** n/a
- **Code Repository URL:** n/a


## File Structure
```
app/
├── Enums/           # Enumerations (UserRole, etc.)
├── Models/          # Eloquent models
├── Livewire/        # Livewire components
├── Services/        # Business logic services
└── Exports/         # Excel/PDF export classes

database/
├── migrations/      # Database migrations
├── seeders/         # Database seeders
└── factories/       # Model factories

resources/
├── views/           # Blade templates
├── css/             # Tailwind CSS
└── js/              # JavaScript/Vite entry
```