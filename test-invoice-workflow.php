<?php

require_once 'vendor/autoload.php';

use Illuminate\Contracts\Console\Kernel;

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

// Create test data
$estate = \App\Models\Estate::create(['name' => 'Test Estate', 'address' => 'Test Address']);
$house = \App\Models\House::create([
    'estate_id' => $estate->id,
    'house_number' => 'A101',
    'meter_number' => 'MTR001',
]);

// Create invoice
$invoice = \App\Models\Invoice::create([
    'invoice_number' => 'INV-TEST-001',
    'house_id' => $house->id,
    'billing_period_start' => now()->subMonth(),
    'billing_period_end' => now(),
    'due_date' => now()->addDays(7),
    'subtotal' => 2500.00,
    'total_amount' => 2500.00,
    'status' => 'pending',
]);

// Add line item
\App\Models\InvoiceLineItem::create([
    'invoice_id' => $invoice->id,
    'description' => 'Water consumption - 50 units',
    'quantity' => 50,
    'unit_price' => 50.00,
    'total_amount' => 2500.00,
]);

echo '✅ Invoice created: '.$invoice->invoice_number."\n";
echo '✅ Line item added: '.$invoice->lineItems()->count()." items\n";
echo '✅ Initial balance: KES '.number_format($invoice->balance_due, 2)."\n";

// Test payment
$payment = \App\Models\InvoicePayment::create([
    'invoice_id' => $invoice->id,
    'amount' => 1000.00,
    'payment_date' => now(),
    'payment_method' => 'cash',
    'notes' => 'Test payment',
]);

$invoice->refresh();
echo '✅ Payment recorded: KES '.number_format($payment->amount, 2)."\n";
echo '✅ New balance: KES '.number_format($invoice->balance_due, 2)."\n";

// Test adjustment
$adjustment = \App\Models\InvoiceAdjustment::create([
    'invoice_id' => $invoice->id,
    'amount' => -200.00,
    'type' => 'discount',
    'reason' => 'Early payment discount',
]);

$invoice->refresh();
echo '✅ Adjustment added: KES '.number_format($adjustment->amount, 2)."\n";
echo '✅ Final balance: KES '.number_format($invoice->balance_due + $invoice->adjustments()->sum('amount'), 2)."\n";
echo "✅ All workflow tests passed successfully!\n";
echo "✅ Invoice workflow is fully functional!\n";
