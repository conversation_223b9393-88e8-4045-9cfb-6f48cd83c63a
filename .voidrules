> Agent OS User Standards
> Last Updated: 2025-07-25

## CRITICAL EXECUTION RULES
**LOAD FILES ON-DEMAND. FOLLOW STANDARDS STRICTLY. INSTRUCTIONS OVERRIDE DEFAULTS.**

---

## EXTERNAL FILE LOADING PROTOCOL
**MANDATORY:** When you encounter a file reference (e.g., @rules/general.md), use your Read tool to load it ONLY when needed for the specific task.

### LOADING RULES:
- **DO NOT** preemptively load all references
- **USE** lazy loading based on actual need for current task
- **TREAT** loaded content as mandatory instructions that override defaults
- **FOLLOW** references recursively when needed
- **LOAD** immediately when referenced content is required for execution

### WHEN TO LOAD:
- **Standards files:** When starting development work
- **Instruction files:** When user requests specific Agent OS workflow
- **Referenced files:** When current file explicitly depends on another

---

## GLOBAL STANDARDS HIERARCHY

### DEVELOPMENT STANDARDS (Load when coding):
- **Tech Stack Defaults:** @.agent-os/standards/tech-stack.md
- **Code Style Preferences:** @.agent-os/standards/code-style.md  
- **Best Practices Philosophy:** @.agent-os/standards/best-practices.md

### AGENT OS INSTRUCTIONS (Load when workflow requested):
- **Initialize Products:** @.agent-os/instructions/plan-product.md
- **Plan Features:** @.agent-os/instructions/create-spec.md
- **Execute Tasks:** @.agent-os/instructions/execute-tasks.md
- **Analyze Existing Code:** @.agent-os/instructions/analyze-product.md

---

## EXECUTION PRIORITY ORDER
**FOLLOW THIS HIERARCHY (HIGHEST TO LOWEST):**

1. **USER DIRECT COMMANDS** - Override everything
2. **PROJECT-SPECIFIC .agent-os/product/ FILES** - Override global standards
3. **LOADED AGENT OS INSTRUCTION FILES** - Override general behavior
4. **LOADED GLOBAL STANDARDS FILES** - Override AI defaults
5. **DEFAULT AI BEHAVIOR** - Fallback only

---

## WORKFLOW INTEGRATION

### FOR DEVELOPMENT WORK:
1. **FIRST:** Load relevant standards files (@.agent-os/standards/*)
2. **THEN:** Apply standards to all code generation
3. **ALWAYS:** Maintain consistency with loaded preferences

### FOR AGENT OS WORKFLOWS:
1. **FIRST:** Load specific instruction file when requested
2. **THEN:** Follow instruction file exactly as written
3. **ALSO:** Load any standards files referenced in instructions
4. **MAINTAIN:** Hierarchy - instructions override standards

### FOR PROJECT INITIALIZATION:
1. **CHECK:** If .agent-os/product/ exists in current project
2. **IF EXISTS:** Prioritize project-specific files over global standards
3. **IF NOT:** Use global standards as defaults for new project setup

---

## CRITICAL REMINDERS

### FILE LOADING:
- **NEVER** assume file contents - always load when needed
- **ALWAYS** treat loaded content as mandatory instructions
- **REMEMBER** project files override global files

### EXECUTION:
- **FOLLOW** loaded standards and instructions exactly
- **DO NOT** deviate from specified workflows
- **MAINTAIN** consistency across all development work

### HIERARCHY:
- **USER COMMANDS** trump everything
- **PROJECT-SPECIFIC** trumps global
- **INSTRUCTIONS** trump standards  
- **STANDARDS** trump defaults

---

## USAGE EXAMPLES

### When user says "start a new project":
1. **LOAD:** @.agent-os/instructions/plan-product.md
2. **FOLLOW:** Exact workflow in that file
3. **LOAD:** Referenced standards files as needed

### When user says "code this feature":
1. **LOAD:** @.agent-os/standards/code-style.md
2. **LOAD:** @.agent-os/standards/best-practices.md  
3. **APPLY:** Standards to all generated code

### When user says "create a spec":
1. **LOAD:** @.agent-os/instructions/create-spec.md
2. **FOLLOW:** Exact process in that file
3. **LOAD:** Any standards files referenced

---

## VERIFICATION CHECKLIST
**BEFORE PROCEEDING WITH ANY DEVELOPMENT:**
- [ ] Identified what files need to be loaded for current task
- [ ] Loaded required files using Read tool
- [ ] Understood hierarchy (project > global > defaults)
- [ ] Ready to follow loaded instructions exactly

**ONLY PROCEED AFTER LOADING REQUIRED FILES**