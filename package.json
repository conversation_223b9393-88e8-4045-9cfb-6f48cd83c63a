{"private": true, "type": "module", "scripts": {"build": "bun run vite build", "dev": "bun run vite"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/vite": "^4.1.11", "autoprefixer": "^10.4.21", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "tailwindcss": "^4.1.11", "vite": "^6.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}, "devDependencies": {"chart.js": "^4.5.0", "postcss": "^8.5.6"}}