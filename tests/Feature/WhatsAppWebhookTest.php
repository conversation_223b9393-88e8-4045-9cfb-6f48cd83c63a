<?php

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\WhatsAppMessage;
use Illuminate\Support\Facades\Config;

test('whatsapp webhook verification succeeds with correct token', function () {
    $token = 'test_verify_token';
    Config::set('services.whatsapp.webhook_verify_token', $token);
    // Set required WhatsApp service config to avoid null assignment error
    Config::set('services.whatsapp.api_url', 'https://test-url.com');
    Config::set('services.whatsapp.token', 'test_token');
    Config::set('services.whatsapp.enabled', false);

    $response = $this->get('/api/webhooks/whatsapp?hub_mode=subscribe&hub_verify_token='.$token.'&hub_challenge=challenge123');

    $response->assertStatus(200);
    $response->assertContent('challenge123');
});

test('whatsapp webhook verification fails with incorrect token', function () {
    Config::set('services.whatsapp.webhook_verify_token', 'correct_token');
    // Set required WhatsApp service config to avoid null assignment error
    Config::set('services.whatsapp.api_url', 'https://test-url.com');
    Config::set('services.whatsapp.token', 'test_token');
    Config::set('services.whatsapp.enabled', false);

    $response = $this->get('/api/webhooks/whatsapp?hub_mode=subscribe&hub_verify_token=wrong_token&hub_challenge=challenge123');

    $response->assertStatus(403);
});

test('whatsapp webhook handles text message from known contact', function () {
    // Set required WhatsApp service config to avoid null assignment error
    Config::set('services.whatsapp.api_url', 'https://test-url.com');
    Config::set('services.whatsapp.token', 'test_token');
    Config::set('services.whatsapp.enabled', false); // Disable to prevent actual sending
    Config::set('services.whatsapp.webhook_secret', 'test_secret');

    // Create test data
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id, 'phone' => '+**********', 'whatsapp_number' => '+**********', 'is_active' => true, 'receive_notifications' => true]);

    $webhookData = [
        'object' => 'whatsapp_business_account',
        'entry' => [
            [
                'id' => 'test_entry_id',
                'changes' => [
                    [
                        'value' => [
                            'messaging_product' => 'whatsapp',
                            'metadata' => [
                                'display_phone_number' => '+**********',
                                'phone_number_id' => 'test_phone_id',
                            ],
                            'contacts' => [
                                [
                                    'profile' => [
                                        'name' => 'Test Contact',
                                    ],
                                    'wa_id' => '+**********',
                                ],
                            ],
                            'messages' => [
                                [
                                    'from' => '+**********',
                                    'id' => 'test_message_id',
                                    'timestamp' => '**********',
                                    'type' => 'text',
                                    'text' => [
                                        'body' => 'Hello',
                                    ],
                                ],
                            ],
                        ],
                        'field' => 'messages',
                    ],
                ],
            ],
        ],
    ];

    // Create a valid signature
    $payload = json_encode($webhookData);
    $signature = 'sha256='.hash_hmac('sha256', $payload, 'test_secret');

    $response = $this->withHeaders(['X-Hub-Signature-256' => $signature])
        ->postJson('/api/webhooks/whatsapp', $webhookData);

    if ($response->status() !== 200) {
        $this->fail("Response status: {$response->status()}. Body: {$response->content()}");
    }

    $response->assertStatus(200);

    // Check that incoming message was recorded
    $this->assertDatabaseHas('whatsapp_messages', [
        'recipient' => '+**********',
        'message_type' => 'incoming_text',
        'direction' => 'incoming',
        'status' => 'received',
        'recipient_contact_id' => $contact->id,
    ]);
});

test('whatsapp webhook handles interactive button message', function () {
    // Set required WhatsApp service config to avoid null assignment error
    Config::set('services.whatsapp.api_url', 'https://test-url.com');
    Config::set('services.whatsapp.token', 'test_token');
    Config::set('services.whatsapp.enabled', false); // Disable to prevent actual sending
    Config::set('services.whatsapp.webhook_secret', 'test_secret');

    // Create test data
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id, 'phone' => '+**********', 'whatsapp_number' => '+**********', 'is_active' => true, 'receive_notifications' => true]);

    $webhookData = [
        'object' => 'whatsapp_business_account',
        'entry' => [
            [
                'id' => 'test_entry_id',
                'changes' => [
                    [
                        'value' => [
                            'messaging_product' => 'whatsapp',
                            'metadata' => [
                                'display_phone_number' => '+**********',
                                'phone_number_id' => 'test_phone_id',
                            ],
                            'contacts' => [
                                [
                                    'profile' => [
                                        'name' => 'Test Contact',
                                    ],
                                    'wa_id' => '+**********',
                                ],
                            ],
                            'messages' => [
                                [
                                    'from' => '+**********',
                                    'id' => 'test_message_id',
                                    'timestamp' => '**********',
                                    'type' => 'interactive',
                                    'interactive' => [
                                        'type' => 'button_reply',
                                        'button_reply' => [
                                            'id' => 'pay_invoice',
                                            'title' => 'Pay Invoice',
                                        ],
                                    ],
                                ],
                            ],
                        ],
                        'field' => 'messages',
                    ],
                ],
            ],
        ],
    ];

    // Create a valid signature
    $payload = json_encode($webhookData);
    $signature = 'sha256='.hash_hmac('sha256', $payload, 'test_secret');

    $response = $this->withHeaders(['X-Hub-Signature-256' => $signature])
        ->postJson('/api/webhooks/whatsapp', $webhookData);

    if ($response->status() !== 200) {
        $this->fail("Response status: {$response->status()}. Body: {$response->content()}");
    }

    $response->assertStatus(200);

    // Check that interactive message was recorded
    $this->assertDatabaseHas('whatsapp_messages', [
        'recipient' => '+**********',
        'message_type' => 'incoming_interactive',
        'direction' => 'incoming',
        'status' => 'received',
        'recipient_contact_id' => $contact->id,
    ]);
});

test('whatsapp webhook handles image message with meter reading', function () {
    // Set required WhatsApp service config to avoid null assignment error
    Config::set('services.whatsapp.api_url', 'https://test-url.com');
    Config::set('services.whatsapp.token', 'test_token');
    Config::set('services.whatsapp.enabled', false); // Disable to prevent actual sending
    Config::set('services.whatsapp.webhook_secret', 'test_secret');

    // Create test data
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id, 'phone' => '+**********', 'whatsapp_number' => '+**********', 'is_active' => true, 'receive_notifications' => true]);

    $webhookData = [
        'object' => 'whatsapp_business_account',
        'entry' => [
            [
                'id' => 'test_entry_id',
                'changes' => [
                    [
                        'value' => [
                            'messaging_product' => 'whatsapp',
                            'metadata' => [
                                'display_phone_number' => '+**********',
                                'phone_number_id' => 'test_phone_id',
                            ],
                            'contacts' => [
                                [
                                    'profile' => [
                                        'name' => 'Test Contact',
                                    ],
                                    'wa_id' => '+**********',
                                ],
                            ],
                            'messages' => [
                                [
                                    'from' => '+**********',
                                    'id' => 'test_message_id',
                                    'timestamp' => '**********',
                                    'type' => 'image',
                                    'image' => [
                                        'mime_type' => 'image/jpeg',
                                        'sha256' => 'test_hash',
                                        'id' => 'test_media_id',
                                        'caption' => 'meter reading for house 123',
                                    ],
                                ],
                            ],
                        ],
                        'field' => 'messages',
                    ],
                ],
            ],
        ],
    ];

    // Create a valid signature
    $payload = json_encode($webhookData);
    $signature = 'sha256='.hash_hmac('sha256', $payload, 'test_secret');

    $response = $this->withHeaders(['X-Hub-Signature-256' => $signature])
        ->postJson('/api/webhooks/whatsapp', $webhookData);

    if ($response->status() !== 200) {
        $this->fail("Response status: {$response->status()}. Body: {$response->content()}");
    }

    $response->assertStatus(200);

    // Check that image message was recorded
    $this->assertDatabaseHas('whatsapp_messages', [
        'recipient' => '+**********',
        'message_type' => 'incoming_image',
        'direction' => 'incoming',
        'status' => 'received',
        'recipient_contact_id' => $contact->id,
        'media_url' => 'test_media_id',
        'media_type' => 'image',
    ]);

    // Check that meter reading was created
    $this->assertDatabaseHas('meter_readings', [
        'house_id' => $house->id,
        'status' => 'submitted',
        'notes' => 'Submitted via WhatsApp with image',
    ]);
});

test('whatsapp webhook handles message from unknown contact', function () {
    // Set required WhatsApp service config to avoid null assignment error
    Config::set('services.whatsapp.api_url', 'https://test-url.com');
    Config::set('services.whatsapp.token', 'test_token');
    Config::set('services.whatsapp.enabled', false); // Disable to prevent actual sending
    Config::set('services.whatsapp.webhook_secret', 'test_secret');

    $webhookData = [
        'object' => 'whatsapp_business_account',
        'entry' => [
            [
                'id' => 'test_entry_id',
                'changes' => [
                    [
                        'value' => [
                            'messaging_product' => 'whatsapp',
                            'metadata' => [
                                'display_phone_number' => '+**********',
                                'phone_number_id' => 'test_phone_id',
                            ],
                            'contacts' => [
                                [
                                    'profile' => [
                                        'name' => 'Unknown Contact',
                                    ],
                                    'wa_id' => '+**********',
                                ],
                            ],
                            'messages' => [
                                [
                                    'from' => '+**********',
                                    'id' => 'test_message_id',
                                    'timestamp' => '**********',
                                    'type' => 'text',
                                    'text' => [
                                        'body' => 'Hello',
                                    ],
                                ],
                            ],
                        ],
                        'field' => 'messages',
                    ],
                ],
            ],
        ],
    ];

    // Create a valid signature
    $payload = json_encode($webhookData);
    $signature = 'sha256='.hash_hmac('sha256', $payload, 'test_secret');

    $response = $this->withHeaders(['X-Hub-Signature-256' => $signature])
        ->postJson('/api/webhooks/whatsapp', $webhookData);

    if ($response->status() !== 200) {
        $this->fail("Response status: {$response->status()}. Body: {$response->content()}");
    }

    $response->assertStatus(200);

    // Should not create a message record for unknown contacts
    $this->assertDatabaseMissing('whatsapp_messages', [
        'recipient' => '+**********',
    ]);
});

test('whatsapp webhook handles status update', function () {
    // Set required WhatsApp service config to avoid null assignment error
    Config::set('services.whatsapp.api_url', 'https://test-url.com');
    Config::set('services.whatsapp.token', 'test_token');
    Config::set('services.whatsapp.enabled', false); // Disable to prevent actual sending
    Config::set('services.whatsapp.webhook_secret', 'test_secret');

    // Create an outgoing message first
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $user = \App\Models\User::factory()->create();

    $message = WhatsAppMessage::create([
        'message_id' => 'test_message_id',
        'sender_id' => $user->id, // Required field
        'recipient' => '+**********',
        'message_type' => 'text',
        'content' => 'Test message',
        'status' => 'sent',
        'recipient_contact_id' => $contact->id,
        'house_id' => $house->id,
        'estate_id' => $estate->id,
    ]);

    $webhookData = [
        'object' => 'whatsapp_business_account',
        'entry' => [
            [
                'id' => 'test_entry_id',
                'changes' => [
                    [
                        'value' => [
                            'messaging_product' => 'whatsapp',
                            'metadata' => [
                                'display_phone_number' => '+**********',
                                'phone_number_id' => 'test_phone_id',
                            ],
                            'statuses' => [
                                [
                                    'id' => 'test_message_id',
                                    'status' => 'delivered',
                                    'timestamp' => '**********',
                                    'recipient_id' => '+**********',
                                ],
                            ],
                        ],
                        'field' => 'messages',
                    ],
                ],
            ],
        ],
    ];

    // Create a valid signature
    $payload = json_encode($webhookData);
    $signature = 'sha256='.hash_hmac('sha256', $payload, 'test_secret');

    $response = $this->withHeaders(['X-Hub-Signature-256' => $signature])
        ->postJson('/api/webhooks/whatsapp', $webhookData);

    if ($response->status() !== 200) {
        $this->fail("Response status: {$response->status()}. Body: {$response->content()}");
    }

    $response->assertStatus(200);

    // Check that message status was updated
    $this->assertDatabaseHas('whatsapp_messages', [
        'message_id' => 'test_message_id',
        'status' => 'delivered',
    ]);
});
