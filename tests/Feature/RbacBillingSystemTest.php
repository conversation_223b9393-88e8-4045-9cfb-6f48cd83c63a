<?php

use App\Enums\UserRole;
use App\Models\Estate;
use App\Models\User;
use Illuminate\Support\Facades\Gate;

test('residents have access to their accounts and invoices', function () {
    $resident = User::factory()->create(['role' => UserRole::RESIDENT]);

    // Check resident permissions
    expect($resident->hasPermission('accounts.view_own'))->toBeTrue();
    expect($resident->hasPermission('accounts.view_balance_own'))->toBeTrue();
    expect($resident->hasPermission('accounts.view_transactions_own'))->toBeTrue();
    expect($resident->hasPermission('accounts.view_statement_own'))->toBeTrue();
    expect($resident->hasPermission('accounts.export_statement_own'))->toBeTrue();
    expect($resident->hasPermission('invoices.view_own'))->toBeTrue();
    expect($resident->hasPermission('invoices.download_own'))->toBeTrue();
    expect($resident->hasPermission('invoices.view_payments_own'))->toBeTrue();
    expect($resident->hasPermission('invoices.view_adjustments_own'))->toBeTrue();
    expect($resident->hasPermission('payments.view_own'))->toBeTrue();
    expect($resident->hasPermission('payments.view_history_own'))->toBeTrue();
    expect($resident->hasPermission('resident.payments.create'))->toBeTrue();
    expect($resident->hasPermission('resident.invoices.download'))->toBeTrue();

    // Check resident doesn't have access to other permissions
    expect($resident->hasPermission('accounts.view_all'))->toBeFalse();
    expect($resident->hasPermission('invoices.view_all'))->toBeFalse();
    expect($resident->hasPermission('export.data_all'))->toBeFalse();
});

test('managers have access to reports for assigned estates', function () {
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $estate = Estate::factory()->create();
    $admin = User::factory()->create(['role' => UserRole::ADMIN]);
    $manager->assignedEstates()->attach($estate->id, ['assigned_by' => $admin->id]);

    // Check manager permissions for assigned estates
    expect($manager->hasPermission('reports.view_assigned'))->toBeTrue();
    expect($manager->hasPermission('reports.generate_assigned'))->toBeTrue();
    expect($manager->hasPermission('reports.aging_assigned'))->toBeTrue();
    expect($manager->hasPermission('reports.revenue_assigned'))->toBeTrue();
    expect($manager->hasPermission('reports.billing_assigned'))->toBeTrue();
    expect($manager->hasPermission('reports.customer_statements_assigned'))->toBeTrue();
    expect($manager->hasPermission('analytics.view_assigned'))->toBeTrue();
    expect($manager->hasPermission('export.data_assigned'))->toBeTrue();

    // Check manager has invoice management permissions
    expect($manager->hasPermission('invoices.view_assigned'))->toBeTrue();
    expect($manager->hasPermission('invoices.adjust_assigned'))->toBeTrue();
    expect($manager->hasPermission('invoices.export_assigned'))->toBeTrue();
    expect($manager->hasPermission('invoices.approve_assigned'))->toBeTrue();
    expect($manager->hasPermission('invoices.send_assigned'))->toBeTrue();
    expect($manager->hasPermission('invoices.generate_assigned'))->toBeTrue();

    // Check manager has account access permissions
    expect($manager->hasPermission('accounts.view_assigned'))->toBeTrue();
    expect($manager->hasPermission('accounts.view_balance_assigned'))->toBeTrue();
    expect($manager->hasPermission('accounts.view_transactions_assigned'))->toBeTrue();
    expect($manager->hasPermission('accounts.view_statement_assigned'))->toBeTrue();
    expect($manager->hasPermission('accounts.export_statement_assigned'))->toBeTrue();

    // Check manager doesn't have system-level permissions
    expect($manager->hasPermission('export.data_all'))->toBeFalse();
    expect($manager->hasPermission('invoices.view_all'))->toBeFalse();
});

test('reviewers have full access to accounts invoices and statements for assigned estates', function () {
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
    $estate = Estate::factory()->create();
    $admin = User::factory()->create(['role' => UserRole::ADMIN]);
    $reviewer->assignedEstates()->attach($estate->id, ['assigned_by' => $admin->id]);

    // Check reviewer has full account access
    expect($reviewer->hasPermission('accounts.view_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('accounts.manage_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('accounts.view_balance_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('accounts.view_transactions_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('accounts.create_transaction_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('accounts.edit_transaction_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('accounts.view_statement_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('accounts.export_statement_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('accounts.adjust_balance_assigned'))->toBeTrue();

    // Check reviewer has full invoice access
    expect($reviewer->hasPermission('invoices.view_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('invoices.generate_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('invoices.edit_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('invoices.send_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('invoices.export_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('invoices.approve_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('invoices.adjust_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('invoices.create_manual'))->toBeTrue();
    expect($reviewer->hasPermission('invoices.delete_assigned'))->toBeTrue();

    // Check reviewer has full payment access
    expect($reviewer->hasPermission('payments.view_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('payments.approve_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('payments.create_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('payments.edit_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('payments.export_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('payments.reconcile_assigned'))->toBeTrue();

    // Check reviewer has full report access
    expect($reviewer->hasPermission('reports.view_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('reports.generate_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('reports.aging_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('reports.revenue_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('reports.billing_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('reports.customer_statements_assigned'))->toBeTrue();
    expect($reviewer->hasPermission('reports.financial_assigned'))->toBeTrue();

    // Check reviewer doesn't have system-level permissions
    expect($reviewer->hasPermission('export.data_all'))->toBeFalse();
    expect($reviewer->hasPermission('invoices.view_all'))->toBeFalse();
});

test('caretakers have access to balance list for assigned estates', function () {
    $caretaker = User::factory()->create(['role' => UserRole::CARETAKER]);
    $estate = Estate::factory()->create();
    $admin = User::factory()->create(['role' => UserRole::ADMIN]);
    $caretaker->assignedEstates()->attach($estate->id, ['assigned_by' => $admin->id]);

    // Check caretaker has balance viewing permissions
    expect($caretaker->hasPermission('accounts.view_balance_assigned'))->toBeTrue();
    expect($caretaker->hasPermission('accounts.view_balance_list_assigned'))->toBeTrue();

    // Check caretaker has basic invoice viewing permissions
    expect($caretaker->hasPermission('invoices.view_assigned'))->toBeTrue();
    expect($caretaker->hasPermission('invoices.view_status_assigned'))->toBeTrue();

    // Check caretaker has basic report permissions
    expect($caretaker->hasPermission('reports.view_assigned'))->toBeTrue();
    expect($caretaker->hasPermission('reports.balance_list_assigned'))->toBeTrue();

    // Check caretaker has contact management permissions
    expect($caretaker->hasPermission('contacts.view_assigned'))->toBeTrue();
    expect($caretaker->hasPermission('contacts.manage_assigned'))->toBeTrue();
    expect($caretaker->hasPermission('contacts.create_assigned'))->toBeTrue();
    expect($caretaker->hasPermission('contacts.edit_assigned'))->toBeTrue();

    // Check caretaker doesn't have financial management permissions
    expect($caretaker->hasPermission('accounts.manage_assigned'))->toBeFalse();
    expect($caretaker->hasPermission('invoices.edit_assigned'))->toBeFalse();
    expect($caretaker->hasPermission('payments.create_assigned'))->toBeFalse();
    expect($caretaker->hasPermission('export.data_assigned'))->toBeFalse();
});

test('admin has full access to all billing system features', function () {
    $admin = User::factory()->create(['role' => UserRole::ADMIN]);

    // Check admin has all system-level permissions
    expect($admin->hasPermission('export.data_all'))->toBeTrue();
    expect($admin->hasPermission('invoices.view_all'))->toBeTrue();
    expect($admin->hasPermission('invoices.generate_all'))->toBeTrue();
    expect($admin->hasPermission('invoices.edit_all'))->toBeTrue();
    expect($admin->hasPermission('invoices.delete'))->toBeTrue();
    expect($admin->hasPermission('invoices.send_all'))->toBeTrue();
    expect($admin->hasPermission('invoices.adjust_all'))->toBeTrue();
    expect($admin->hasPermission('invoices.export_all'))->toBeTrue();

    // Check admin has full account access
    expect($admin->hasPermission('accounts.view_all'))->toBeTrue();
    expect($admin->hasPermission('accounts.manage_all'))->toBeTrue();
    expect($admin->hasPermission('accounts.view_balance_all'))->toBeTrue();
    expect($admin->hasPermission('accounts.view_transactions_all'))->toBeTrue();
    expect($admin->hasPermission('accounts.view_statement_all'))->toBeTrue();
    expect($admin->hasPermission('accounts.export_statement_all'))->toBeTrue();

    // Check admin has full report access
    expect($admin->hasPermission('reports.view_all'))->toBeTrue();
    expect($admin->hasPermission('reports.generate_all'))->toBeTrue();
    expect($admin->hasPermission('reports.aging_all'))->toBeTrue();
    expect($admin->hasPermission('reports.revenue_all'))->toBeTrue();
    expect($admin->hasPermission('reports.billing_all'))->toBeTrue();
    expect($admin->hasPermission('reports.customer_statements_all'))->toBeTrue();
    expect($admin->hasPermission('reports.financial_all'))->toBeTrue();
    expect($admin->hasPermission('analytics.view_all'))->toBeTrue();

    // Check admin has full payment access
    expect($admin->hasPermission('payments.view_all'))->toBeTrue();
    expect($admin->hasPermission('payments.approve_all'))->toBeTrue();
    expect($admin->hasPermission('payments.export_all'))->toBeTrue();
});

test('gates are properly registered for new permissions', function () {
    // Test that gates are registered for new permissions
    expect(Gate::has('accounts.view_own'))->toBeTrue();
    expect(Gate::has('accounts.view_assigned'))->toBeTrue();
    expect(Gate::has('accounts.view_all'))->toBeTrue();
    expect(Gate::has('invoices.view_own'))->toBeTrue();
    expect(Gate::has('invoices.view_assigned'))->toBeTrue();
    expect(Gate::has('invoices.view_all'))->toBeTrue();
    expect(Gate::has('payments.view_own'))->toBeTrue();
    expect(Gate::has('payments.view_assigned'))->toBeTrue();
    expect(Gate::has('payments.view_all'))->toBeTrue();
    expect(Gate::has('reports.aging_assigned'))->toBeTrue();
    expect(Gate::has('reports.revenue_assigned'))->toBeTrue();
    expect(Gate::has('reports.billing_assigned'))->toBeTrue();
    expect(Gate::has('reports.customer_statements_assigned'))->toBeTrue();
    expect(Gate::has('reports.balance_list_assigned'))->toBeTrue();
});