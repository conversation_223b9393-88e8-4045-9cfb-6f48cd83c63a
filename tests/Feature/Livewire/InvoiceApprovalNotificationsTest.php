<?php

declare(strict_types=1);

use App\Enums\UserRole;
use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use App\Models\WhatsAppMessage;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Enable WhatsApp for testing
    config(['services.whatsapp.enabled' => true]);

    // Mock the WhatsApp service to prevent actual API calls
    $this->mockWhatsAppService = mock(\App\Services\WhatsAppService::class);
    app()->instance(\App\Services\WhatsAppService::class, $this->mockWhatsAppService);

    // Mock the WhatsApp templates
    $this->mockWhatsAppTemplates = mock(\App\Services\WhatsAppTemplates::class);
    app()->instance(\App\Services\WhatsAppTemplates::class, $this->mockWhatsAppTemplates);
});

test('invoice submission sends notifications to reviewers', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
    $reviewerContact = Contact::factory()->create([
        'user_id' => $reviewer->id,
        'whatsapp_number' => '+1234567890',
        'receive_notifications' => true,
    ]);

    // Assign estate to users
    $manager->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);
    $reviewer->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    // Mock template rendering
    $this->mockWhatsAppTemplates
        ->shouldReceive('renderTemplate')
        ->with('invoice_submitted_for_approval', \Mockery::type('array'))
        ->andReturn('Test notification message');

    // Mock WhatsApp sending - expect at least one call
    $this->mockWhatsAppService
        ->shouldReceive('sendText')
        ->atLeast()
        ->once()
        ->andReturn(new WhatsAppMessage);

    // Submit invoice for approval
    $invoice->submitForApproval($manager);

    // Verify invoice status changed
    expect($invoice->fresh()->status)->toBe('submitted');
    expect($invoice->fresh()->submitted_by)->toBe($manager->id);
});

test('invoice approval sends notification to manager', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
    $managerContact = Contact::factory()->create([
        'user_id' => $manager->id,
        'whatsapp_number' => '+1234567890',
        'receive_notifications' => true,
    ]);

    // Assign estate to users
    $manager->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);
    $reviewer->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'submitted',
        'submitted_by' => $manager->id,
    ]);

    // Mock template rendering
    $this->mockWhatsAppTemplates
        ->shouldReceive('renderTemplate')
        ->with('invoice_approved', \Mockery::type('array'))
        ->andReturn('Test approval notification');

    // Mock WhatsApp sending - expect at least one call
    $this->mockWhatsAppService
        ->shouldReceive('sendText')
        ->atLeast()
        ->once()
        ->andReturn(new WhatsAppMessage);

    // Approve invoice
    $invoice->approve($reviewer);

    // Verify invoice status changed
    expect($invoice->fresh()->status)->toBe('approved');
    expect($invoice->fresh()->approved_by)->toBe($reviewer->id);
});

test('invoice rejection sends notification to manager with reason', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
    $managerContact = Contact::factory()->create([
        'user_id' => $manager->id,
        'whatsapp_number' => '+1234567890',
        'receive_notifications' => true,
    ]);

    // Assign estate to users
    $manager->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);
    $reviewer->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'submitted',
        'submitted_by' => $manager->id,
    ]);

    $rejectionReason = 'Incorrect meter reading';

    // Mock template rendering
    $this->mockWhatsAppTemplates
        ->shouldReceive('renderTemplate')
        ->with('invoice_rejected', \Mockery::type('array'))
        ->andReturn('Test rejection notification');

    // Mock WhatsApp sending - expect at least one call
    $this->mockWhatsAppService
        ->shouldReceive('sendText')
        ->atLeast()
        ->once()
        ->andReturn(new WhatsAppMessage);

    // Reject invoice
    $invoice->reject($reviewer, $rejectionReason);

    // Verify invoice status changed back to draft
    expect($invoice->fresh()->status)->toBe('draft');
    expect($invoice->fresh()->approved_by)->toBe($reviewer->id);
});

test('notifications are not sent when WhatsApp is disabled', function () {
    // Disable WhatsApp
    config(['services.whatsapp.enabled' => false]);

    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);

    // Assign estate to users
    $manager->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);
    $reviewer->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    // Mock services to ensure they're not called
    $this->mockWhatsAppTemplates->shouldNotReceive('renderTemplate');
    $this->mockWhatsAppService->shouldNotReceive('sendText');

    // Submit invoice for approval - should not throw error but also not send notifications
    $invoice->submitForApproval($manager);

    // Verify invoice status changed
    expect($invoice->fresh()->status)->toBe('submitted');
});

test('notifications are not sent when contact has no WhatsApp', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);

    // Create contact without WhatsApp
    Contact::factory()->create([
        'user_id' => $reviewer->id,
        'whatsapp_number' => null,
        'receive_notifications' => true,
    ]);

    // Assign estate to users
    $manager->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);
    $reviewer->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    // Mock services to ensure they're not called
    $this->mockWhatsAppTemplates->shouldNotReceive('renderTemplate');
    $this->mockWhatsAppService->shouldNotReceive('sendText');

    // Submit invoice for approval - should not send notifications
    $invoice->submitForApproval($manager);

    // Verify invoice status changed
    expect($invoice->fresh()->status)->toBe('submitted');
});
