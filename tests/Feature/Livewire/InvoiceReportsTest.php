<?php

namespace Tests\Feature\Livewire;

use App\Livewire\Invoice\InvoiceReports;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class InvoiceReportsTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $estate;
    protected $house;
    protected $waterRate;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->estate = Estate::factory()->create();
        $this->house = House::factory()->create(['estate_id' => $this->estate->id]);
        $this->waterRate = WaterRate::factory()->create();
    }

    public function test_invoice_reports_component_renders()
    {
        $this->actingAs($this->user);

        Livewire::test(InvoiceReports::class)
            ->assertStatus(200)
            ->assertSee('Invoice Reports')
            ->assertSee('Generate Report');
    }

    public function test_component_has_default_values()
    {
        $this->actingAs($this->user);

        $component = Livewire::test(InvoiceReports::class);
        
        $this->assertEquals('summary', $component->get('report_type'));
        $this->assertEquals('all', $component->get('status'));
        $this->assertNotNull($component->get('date_from'));
        $this->assertNotNull($component->get('date_to'));
    }

    public function test_generate_report_validates_input()
    {
        $this->actingAs($this->user);

        Livewire::test(InvoiceReports::class)
            ->set('date_from', '')
            ->set('date_to', '')
            ->call('generateReport')
            ->assertHasErrors(['date_from', 'date_to']);
    }

    public function test_generate_report_validates_date_range()
    {
        $this->actingAs($this->user);

        Livewire::test(InvoiceReports::class)
            ->set('date_from', '2024-01-15')
            ->set('date_to', '2024-01-10')
            ->call('generateReport')
            ->assertHasErrors(['date_to']);
    }

    public function test_generate_report_with_valid_data()
    {
        $this->actingAs($this->user);

        // Create test invoice
        Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'paid',
            'created_at' => now()->subDays(5),
        ]);

        Livewire::test(InvoiceReports::class)
            ->set('date_from', now()->startOfMonth()->format('Y-m-d'))
            ->set('date_to', now()->endOfMonth()->format('Y-m-d'))
            ->set('report_type', 'summary')
            ->set('status', 'all')
            ->call('generateReport')
            ->assertFileDownloaded();
    }

    public function test_generate_report_filters_by_status()
    {
        $this->actingAs($this->user);

        // Create invoices with different statuses
        Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'paid',
            'created_at' => now()->subDays(5),
        ]);

        Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'sent',
            'created_at' => now()->subDays(3),
        ]);

        Livewire::test(InvoiceReports::class)
            ->set('status', 'paid')
            ->call('generateReport')
            ->assertFileDownloaded();
    }

    public function test_generate_report_filters_by_estate()
    {
        $this->actingAs($this->user);

        $anotherEstate = Estate::factory()->create();
        $anotherHouse = House::factory()->create(['estate_id' => $anotherEstate->id]);

        // Create invoices in different estates
        Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'created_at' => now()->subDays(5),
        ]);

        Invoice::factory()->create([
            'house_id' => $anotherHouse->id,
            'water_rate_id' => $this->waterRate->id,
            'created_at' => now()->subDays(3),
        ]);

        Livewire::test(InvoiceReports::class)
            ->set('estate_id', $this->estate->id)
            ->call('generateReport')
            ->assertFileDownloaded();
    }
}
