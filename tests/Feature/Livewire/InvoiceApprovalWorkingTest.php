<?php

declare(strict_types=1);

use App\Enums\UserRole;
use App\Livewire\Admin\InvoiceApproval;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);

test('renders invoice approval dashboard with pending invoices', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);

    $draftInvoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    $submittedInvoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'submitted',
        'submitted_by' => $manager->id,
        'submitted_at' => now(),
    ]);

    $approvedInvoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'approved',
        'approved_by' => $reviewer->id,
        'approved_at' => now(),
    ]);

    Livewire::actingAs($reviewer)
        ->test(InvoiceApproval::class)
        ->assertSet('filter', 'pending')
        ->assertSee('Invoice Approval')
        ->assertSee($draftInvoice->invoice_number)
        ->assertSee($submittedInvoice->invoice_number)
        ->assertDontSee($approvedInvoice->invoice_number);
});

test('can submit invoice for approval', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);

    // Assign estate to manager
    $manager->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    Livewire::actingAs($manager)
        ->test(InvoiceApproval::class)
        ->call('submitForApproval', $invoice->id)
        ->assertDispatched('invoice-submitted');

    $this->assertDatabaseHas('invoices', [
        'id' => $invoice->id,
        'status' => 'submitted',
        'submitted_by' => $manager->id,
    ]);

    expect($invoice->fresh()->submitted_at)->not()->toBeNull();
});
