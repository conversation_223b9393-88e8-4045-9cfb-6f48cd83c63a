<?php

declare(strict_types=1);

use App\Enums\UserRole;
use App\Livewire\Invoice\InvoiceDetail;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);

test('invoice detail component shows approval workflow buttons', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);

    // Assign estate to users
    $manager->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);
    $reviewer->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    // Test as manager - should see submit button
    Livewire::actingAs($manager)
        ->test(InvoiceDetail::class, ['invoice' => $invoice])
        ->assertSee('Submit for Approval')
        ->assertDontSee('Approve');

    // Test as reviewer - should not see submit button for draft
    Livewire::actingAs($reviewer)
        ->test(InvoiceDetail::class, ['invoice' => $invoice])
        ->assertDontSee('Submit for Approval')
        ->assertDontSee('Approve');
});

test('invoice detail component shows approve/reject buttons for submitted invoices', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);

    // Assign estate to users
    $manager->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);
    $reviewer->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'submitted',
        'submitted_by' => $manager->id,
        'submitted_at' => now(),
    ]);

    // Test as reviewer - should see approve/reject buttons
    Livewire::actingAs($reviewer)
        ->test(InvoiceDetail::class, ['invoice' => $invoice])
        ->assertDontSee('Submit for Approval')
        ->assertSee('Approve')
        ->assertSee('Reject');

    // Test as manager - should not see approve/reject buttons
    Livewire::actingAs($manager)
        ->test(InvoiceDetail::class, ['invoice' => $invoice])
        ->assertDontSee('Submit for Approval')
        ->assertDontSee('Approve')
        ->assertDontSeeHtml('<button wire:click="approve">')
        ->assertDontSeeHtml('<button wire:click="$set(\'modalState\', \'rejection\')">');
});

test('can submit invoice for approval from detail page', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);

    // Assign estate to manager
    $manager->assignedEstates()->attach($estate->id, [
        'assigned_by' => $manager->id,
        'assigned_at' => now(),
    ]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    Livewire::actingAs($manager)
        ->test(InvoiceDetail::class, ['invoice' => $invoice])
        ->call('submitForApproval')
        ->assertDispatched('success', 'Invoice submitted for approval successfully');

    $this->assertDatabaseHas('invoices', [
        'id' => $invoice->id,
        'status' => 'submitted',
        'submitted_by' => $manager->id,
    ]);
});
