<?php

use App\Livewire\Estate\WaterRateList;
use App\Models\Estate;
use App\Models\User;
use App\Models\WaterRate;
use Livewire\Livewire;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function () {
    $this->actingAs(User::factory()->create(['role' => 'manager']));
});

it('displays water rates for a specific estate', function () {
    $estate = Estate::factory()->create();
    $otherEstate = Estate::factory()->create();

    WaterRate::factory()->count(5)->create(['estate_id' => $estate->id]);
    WaterRate::factory()->count(3)->create(['estate_id' => $otherEstate->id]);

    $component = Livewire::test(WaterRateList::class, ['estate' => $estate])
        ->assertSeeHtml('Water Rates for '.$estate->name);

    // Assert that water rates for the specific estate are displayed
    foreach (WaterRate::where('estate_id', $estate->id)->get() as $waterRate) {
        $component->assertSee($waterRate->name);
    }

    // Assert that water rates for other estates are NOT displayed
    foreach (WaterRate::where('estate_id', $otherEstate->id)->get() as $waterRate) {
        $component->assertDontSee($waterRate->name);
    }
});

it('paginates water rates', function () {
    $estate = Estate::factory()->create();
    $waterRates = WaterRate::factory()->count(15)->create(['estate_id' => $estate->id]);

    Livewire::test(WaterRateList::class, ['estate' => $estate])
        ->assertSeeHtml('Water Rates for '.$estate->name)
        ->assertSeeHtml('<nav') // Check for the presence of pagination HTML
        ->assertSee($waterRates->sortByDesc('effective_from')->take(10)->first()->name) // First item on first page
        ->assertDontSee($waterRates->sortByDesc('effective_from')->skip(10)->first()->name);
    // First item on second page should not be visible
});

it('shows water rate details', function () {
    $estate = Estate::factory()->create();
    $waterRate = WaterRate::factory()->create([
        'estate_id' => $estate->id,
        'name' => 'Residential Rate',
        'rate_per_unit' => 15.50,
        'effective_from' => '2024-01-01',
    ]);

    Livewire::test(WaterRateList::class, ['estate' => $estate])
        ->assertSee($waterRate->name)
        ->assertSee('15.50')
        ->assertSee('Jan 01, 2024');
});

it('can delete a water rate', function () {
    $estate = Estate::factory()->create();
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);

    $this->assertDatabaseHas('water_rates', ['id' => $waterRate->id]);

    Livewire::test(WaterRateList::class, ['estate' => $estate])
        ->call('deleteWaterRate', $waterRate);

    $this->assertDatabaseMissing('water_rates', ['id' => $waterRate->id]);
});

it('prevents deletion of water rate in use by invoices', function () {
    $estate = Estate::factory()->create();
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);

    // Create an invoice that uses this water rate
    $house = \App\Models\House::factory()->create(['estate_id' => $estate->id]);
    $invoice = \App\Models\Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
    ]);

    $this->assertDatabaseHas('water_rates', ['id' => $waterRate->id]);
    $this->assertDatabaseHas('invoices', ['id' => $invoice->id]);

    Livewire::test(WaterRateList::class, ['estate' => $estate])
        ->call('deleteWaterRate', $waterRate)
        ->assertHasErrors(['deletion' => 'Cannot delete water rate that is in use by invoices']);

    // Water rate should still exist
    $this->assertDatabaseHas('water_rates', ['id' => $waterRate->id]);
});

it('can delete water rate not in use by invoices', function () {
    $estate = Estate::factory()->create();
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);

    // Create an invoice that uses a different water rate
    $otherWaterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $house = \App\Models\House::factory()->create(['estate_id' => $estate->id]);
    $invoice = \App\Models\Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $otherWaterRate->id,
    ]);

    $this->assertDatabaseHas('water_rates', ['id' => $waterRate->id]);

    Livewire::test(WaterRateList::class, ['estate' => $estate])
        ->call('deleteWaterRate', $waterRate);

    // Our water rate should be deleted, but the other one should remain
    $this->assertDatabaseMissing('water_rates', ['id' => $waterRate->id]);
    $this->assertDatabaseHas('water_rates', ['id' => $otherWaterRate->id]);
    $this->assertDatabaseHas('invoices', ['id' => $invoice->id]);
});
