<?php

use App\Livewire\EstateManager;
use App\Models\Estate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);

test('renders estate manager component', function () {
    $user = User::factory()->create(['role' => 'manager']);
    $this->actingAs($user);

    Livewire::test(EstateManager::class)
        ->assertSee('Estates')
        ->assertSee('Add Estate');
});

test('displays list of estates', function () {
    $user = User::factory()->create(['role' => 'manager']);
    $estates = Estate::factory()->count(3)->create();

    Livewire::actingAs($user)
        ->test(EstateManager::class)
        ->assertSee($estates[0]->name)
        ->assertSee($estates[1]->name)
        ->assertSee($estates[2]->name);
});

test('can create new estate', function () {
    $user = User::factory()->create(['role' => 'manager']);
    $this->actingAs($user);

    Livewire::test(EstateManager::class)
        ->call('openCreateModal')
        ->set('form.name', 'New Estate')
        ->set('form.code', 'EST-001')
        ->set('form.city', 'Nairobi')
        ->set('form.address', '123 Main Street')
        ->set('form.contact_email', '<EMAIL>')
        ->set('form.contact_phone', '+254700000000')
        ->call('save')
        ->assertHasNoErrors()
        ->assertDispatched('estate-saved')
        ->assertSee('New Estate');

    $this->assertDatabaseHas('estates', [
        'name' => 'New Estate',
        'code' => 'EST-001',
        'city' => 'Nairobi',
        'address' => '123 Main Street',
        'contact_email' => '<EMAIL>',
        'contact_phone' => '+254700000000',
    ]);
});

test('validates estate creation', function () {
    $user = User::factory()->create(['role' => 'manager']);
    $this->actingAs($user);

    Livewire::test(EstateManager::class)
        ->call('openCreateModal')
        ->set('form.name', '')
        ->call('save')
        ->assertHasErrors(['form.name' => 'required']);
});

test('can edit existing estate', function () {
    $user = User::factory()->create(['role' => 'manager']);
    $estate = Estate::factory()->create();
    $this->actingAs($user);

    Livewire::test(EstateManager::class)
        ->call('openEditModal', $estate->id)
        ->set('form.name', 'Updated Estate Name')
        ->set('form.code', 'UPD-001')
        ->call('save')
        ->assertHasNoErrors()
        ->assertDispatched('estate-saved')
        ->assertSee('Updated Estate Name');

    $this->assertDatabaseHas('estates', [
        'id' => $estate->id,
        'name' => 'Updated Estate Name',
        'code' => 'UPD-001',
    ]);
});

test('can delete estate', function () {
    $user = User::factory()->create(['role' => 'manager']);
    $estate = Estate::factory()->create();
    $this->actingAs($user);

    Livewire::test(EstateManager::class)
        ->call('confirmDelete', $estate->id)
        ->assertSet('estateIdToDelete', $estate->id)
        ->call('delete')
        ->assertHasNoErrors()
        ->assertDispatched('estate-deleted');

    $this->assertDatabaseMissing('estates', ['id' => $estate->id]);
});

test('can view estate details', function () {
    $user = User::factory()->create(['role' => 'manager']);
    $estate = Estate::factory()->create();
    $this->actingAs($user);

    Livewire::test(EstateManager::class)
        ->call('showEstate', $estate->id)
        ->assertSet('showModal', true)
        ->assertSet('estateIdToShow', $estate->id)
        ->assertSee($estate->name)
        ->assertSee($estate->code);
});

test('requires manager role to access', function () {
    $user = User::factory()->create(['role' => 'caretaker']);
    $this->actingAs($user);

    $response = $this->get('/estates');
    $response->assertForbidden();
});

test('validates estate form fields', function () {
    $user = User::factory()->create(['role' => 'manager']);
    $this->actingAs($user);

    Livewire::test(EstateManager::class)
        ->call('openCreateModal')
        ->set('form.name', '')
        ->set('form.code', '')
        ->set('form.city', '')
        ->call('save')
        ->assertHasErrors([
            'form.name' => 'required',
            'form.code' => 'required',
            'form.city' => 'required',
        ]);
});

test('validates email format for contact_email', function () {
    $user = User::factory()->create(['role' => 'manager']);
    $this->actingAs($user);

    Livewire::test(EstateManager::class)
        ->call('openCreateModal')
        ->set('form.contact_email', 'invalid-email')
        ->call('save')
        ->assertHasErrors(['form.contact_email' => 'email']);
});
