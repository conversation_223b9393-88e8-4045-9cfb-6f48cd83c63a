<?php

use App\Enums\UserRole;
use App\Models\Estate;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('reviewer can access billing reports', function () {
    // Create admin user to assign estates
    $admin = User::factory()->create(['role' => UserRole::ADMIN]);

    // Create reviewer and estate
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
    $estate = Estate::factory()->create();

    // Assign estate to reviewer
    $reviewer->assignedEstates()->attach($estate->id, ['assigned_by' => $admin->id]);
    
    // Debug output
    dump('User ID: ' . $reviewer->id);
    dump('User Role: ' . $reviewer->role->value);
    dump('Has Permission: ' . ($reviewer->hasPermission('invoices.generate_assigned') ? 'true' : 'false'));
    dump('Has Estates: ' . ($reviewer->assignedEstates()->exists() ? 'true' : 'false'));
    dump('Gate Check: ' . (\Illuminate\Support\Facades\Gate::forUser($reviewer)->check('invoices.generate_assigned') ? 'true' : 'false'));
    
    // Test other reviewer routes first
    $dashboardResponse = $this->actingAs($reviewer)->get('/reviewer/dashboard');
    dump('Dashboard Response Status: ' . $dashboardResponse->status());
    
    $billingResponse = $this->actingAs($reviewer)->get('/reviewer/billing');
    dump('Billing Response Status: ' . $billingResponse->status());
    
    $reportsResponse = $this->actingAs($reviewer)->get('/reviewer/billing/reports');
    dump('Reports Response Status: ' . $reportsResponse->status());
    $reportsResponse->assertStatus(200);
});

test('unauthenticated user cannot access billing reports', function () {
    $response = $this->get('/reviewer/billing/reports');

    $response->assertRedirect('/login');
});

test('user without permission cannot access billing reports', function () {
    // Create a manager user who doesn't have invoices.generate_assigned permission
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);

    $response = $this->actingAs($manager)->get('/reviewer/billing/reports');

    $response->assertStatus(403);
});
