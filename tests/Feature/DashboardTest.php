<?php

use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('guests are redirected to the login page', function () {
    $this->get('/dashboard')->assertRedirect('/login');
});

test('authenticated users can visit the dashboard', function () {
    $this->actingAs($user = User::factory()->create(['role' => \App\Enums\UserRole::MANAGER]));

    $this->get('/dashboard')->assertRedirect('/management/dashboard');
    $this->get('/management/dashboard')->assertStatus(200);
});
