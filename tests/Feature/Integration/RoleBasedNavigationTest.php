<?php

use App\Enums\UserRole;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('admin user gets admin sidebar on all pages', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $response = $this->get(route('admin.dashboard'));
    $response->assertSee('System Overview');
    $response->assertSee('User Management');

    $response = $this->get(route('admin.users'));
    $response->assertSee('System Overview');
    $response->assertSee('User Management');

    $response = $this->get(route('admin.settings'));
    $response->assertSee('System Overview');
    $response->assertSee('User Management');
});

test('manager user gets manager sidebar on all pages', function () {
    $estate = \App\Models\Estate::factory()->create();
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $user->assignedEstates()->attach($estate, ['assigned_by' => $user->id]);
    $this->actingAs($user);

    $response = $this->get(route('management.dashboard'));
    $response->assertSee('Management Dashboard');
    $response->assertSee('Estates');

    $response = $this->get(route('management.estates'));
    $response->assertSee('Management Dashboard');
    $response->assertSee('Estates');

    $response = $this->get(route('management.houses'));
    $response->assertSee('Management Dashboard');
    $response->assertSee('Estates');
});

test('reviewer user gets reviewer sidebar on all pages', function () {
    $user = User::factory()->create(['role' => UserRole::REVIEWER]);
    $this->actingAs($user);

    $response = $this->get(route('reviewer.dashboard'));
    $response->assertSee('Reviewer Dashboard');
    $response->assertSee('Billing');

    $response = $this->get(route('reviewer.billing'));
    $response->assertSee('Reviewer Dashboard');
    $response->assertSee('Billing');

    $response = $this->get(route('reviewer.readings'));
    $response->assertSee('Reviewer Dashboard');
    $response->assertSee('Billing');
});

test('caretaker user gets caretaker sidebar on all pages', function () {
    $estate = \App\Models\Estate::factory()->create();
    $user = User::factory()->create(['role' => UserRole::CARETAKER]);
    $user->assignedEstates()->attach($estate, ['assigned_by' => $user->id]);
    $this->actingAs($user);

    $response = $this->get(route('caretaker.dashboard'));
    $response->assertSee('Caretaker Dashboard');
    $response->assertSee('Estates');

    $response = $this->get(route('caretaker.estates'));
    $response->assertSee('Caretaker Dashboard');
    $response->assertSee('Estates');

    $response = $this->get(route('caretaker.houses', ['estateId' => $estate->id]));
    $response->assertSee('Caretaker Dashboard');
    $response->assertSee('Estates');
});

test('resident user gets resident navigation on all pages', function () {
    $user = User::factory()->create(['role' => UserRole::RESIDENT]);
    $this->actingAs($user);

    $response = $this->get(route('resident.dashboard'));
    $response->assertStatus(200);
    // Should work now
    $response->assertSee('Resident Portal');
    $response->assertSee('Dashboard');
    $response->assertSee('Invoices');
    $response->assertSee('Readings');
    $response->assertSee('Messages');
    $response->assertSee('Contact Us');

    $response = $this->get(route('resident.invoices'));
    $response->assertSee('Resident Portal');
    $response->assertSee('Invoices');
});

test('navigation links are role appropriate', function () {
    // Admin should see admin links
    $admin = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($admin);

    $response = $this->get(route('admin.dashboard'));
    $response->assertSee('User Management');
    $response->assertSee('System Settings');
    $response->assertSee('Audit Logs');

    // Manager should NOT see admin links
    $estate = \App\Models\Estate::factory()->create();
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $manager->assignedEstates()->attach($estate, ['assigned_by' => $manager->id]);
    $this->actingAs($manager);

    $response = $this->get(route('management.dashboard'));
    $response->assertDontSee('User Management');
    $response->assertDontSee('System Settings');
    $response->assertDontSee('Audit Logs');
    $response->assertSee('Team Management');
    $response->assertSee('Reports');
});

test('navigation links respect permissions', function () {
    // Reviewer should see billing links
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
    $this->actingAs($reviewer);

    $response = $this->get(route('reviewer.dashboard'));
    $response->assertSee('Billing');
    $response->assertSee('Readings');

    // Caretaker should NOT see billing links
    $estate = \App\Models\Estate::factory()->create();
    $caretaker = User::factory()->create(['role' => UserRole::CARETAKER]);
    $caretaker->assignedEstates()->attach($estate, ['assigned_by' => $caretaker->id]);
    $this->actingAs($caretaker);

    $response = $this->get(route('caretaker.dashboard'));
    $response->assertDontSee('Billing');
    $response->assertSee('Readings');
});

test('sidebar components are accessible', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $response = $this->get(route('admin.dashboard'));

    $response->assertSee('role="navigation"');
    $response->assertSee('aria-label');
    $response->assertSee('tabindex');
});

test('sidebar components have proper seo structure', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $response = $this->get(route('admin.dashboard'));

    $response->assertSee('nav');
    $response->assertSee('ul');
    $response->assertSee('li');
    $response->assertSee('a');
});

test('sidebar components work with javascript disabled', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $response = $this->get(route('admin.dashboard'), [], ['HTTP_X-Requested-With' => '']);

    $response->assertStatus(200);
    $response->assertSee('System Overview');
    $response->assertSee('User Management');
});

test('navigation state persists across page requests', function () {
    $estate = \App\Models\Estate::factory()->create();
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $user->assignedEstates()->attach($estate, ['assigned_by' => $user->id]);
    $this->actingAs($user);

    // Start at dashboard
    $response = $this->get(route('management.dashboard'));
    $response->assertSee('Management Dashboard');

    // Navigate to estates
    $response = $this->get(route('management.estates'));
    $response->assertSee('Management Dashboard');
    $response->assertSee('Estates');

    // Navigate to houses
    $response = $this->get(route('management.houses'));
    $response->assertSee('Management Dashboard');
    $response->assertSee('Estates');
    $response->assertSee('Houses');
});

test('user profile displayed consistently across roles', function () {
    $users = [
        User::factory()->create(['role' => UserRole::ADMIN, 'name' => 'Admin User', 'email' => '<EMAIL>']),
        User::factory()->create(['role' => UserRole::MANAGER, 'name' => 'Manager User', 'email' => '<EMAIL>']),
        User::factory()->create(['role' => UserRole::REVIEWER, 'name' => 'Reviewer User', 'email' => '<EMAIL>']),
        User::factory()->create(['role' => UserRole::CARETAKER, 'name' => 'Caretaker User', 'email' => '<EMAIL>']),
    ];

    foreach ($users as $user) {
        $this->actingAs($user);

        $route = match ($user->role) {
            UserRole::ADMIN => route('admin.dashboard'),
            UserRole::MANAGER => route('management.dashboard'),
            UserRole::REVIEWER => route('reviewer.dashboard'),
            UserRole::CARETAKER => route('caretaker.dashboard'),
        };

        $response = $this->get($route);
        $response->assertSee($user->name);
        $response->assertSee($user->email);
        $response->assertSee(substr($user->name, 0, 2)); // Initials
    }
});
