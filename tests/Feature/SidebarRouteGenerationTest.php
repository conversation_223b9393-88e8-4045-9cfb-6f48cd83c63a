<?php

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Sidebar Route Generation', function () {
    beforeEach(function () {
        // Create test users for each role
        $this->admin = User::factory()->create(['role' => UserRole::ADMIN]);
        $this->manager = User::factory()->create(['role' => UserRole::MANAGER]);
        $this->reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
        $this->caretaker = User::factory()->create(['role' => UserRole::CARETAKER]);
        $this->resident = User::factory()->create(['role' => UserRole::RESIDENT]);
    });

    describe('Estate Routes', function () {
        it('generates correct estate route for admin', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="' . route('estates') . '"');
            $response->assertDontSee(route('management.estates'));
            $response->assertDontSee(route('reviewer.estates'));
            $response->assertDontSee(route('caretaker.estates'));
        });

        it('generates correct estate route for manager', function () {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('href="' . route('management.estates') . '"');
            $response->assertDontSee(route('estates'));
            $response->assertDontSee(route('reviewer.estates'));
            $response->assertDontSee(route('caretaker.estates'));
        });

        it('generates correct estate route for reviewer', function () {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('href="' . route('reviewer.estates') . '"');
            $response->assertDontSee(route('estates'));
            $response->assertDontSee(route('management.estates'));
            $response->assertDontSee(route('caretaker.estates'));
        });

        it('generates correct estate route for caretaker', function () {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('href="' . route('caretaker.estates') . '"');
            $response->assertDontSee(route('estates'));
            $response->assertDontSee(route('management.estates'));
            $response->assertDontSee(route('reviewer.estates'));
        });

        it('does not show estate route for resident', function () {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertDontSee(route('estates'));
            $response->assertDontSee(route('management.estates'));
            $response->assertDontSee(route('reviewer.estates'));
            $response->assertDontSee(route('caretaker.estates'));
        });
    });

    describe('Estate Analytics Routes', function () {
        it('generates correct estate analytics route for admin', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="' . route('estates.analytics') . '"');
            $response->assertDontSee(route('management.estates.analytics'));
        });

        it('generates correct estate analytics route for manager', function () {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('href="' . route('management.estates.analytics') . '"');
            $response->assertDontSee(route('estates.analytics'));
        });

        it('does not show estate analytics route for non-admin/manager users', function () {
            $users = [$this->reviewer, $this->caretaker, $this->resident];
            
            foreach ($users as $user) {
                $route = match($user->role) {
                    UserRole::REVIEWER => '/reviewer/dashboard',
                    UserRole::CARETAKER => '/caretaker/dashboard',
                    UserRole::RESIDENT => '/resident/dashboard',
                };

                $response = $this->actingAs($user)->get($route);
                $response->assertDontSee(route('estates.analytics'));
                $response->assertDontSee(route('management.estates.analytics'));
            }
        });
    });

    describe('House Routes', function () {
        it('generates correct house route for admin', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="' . route('houses') . '"');
            $response->assertDontSee(route('management.houses'));
            $response->assertDontSee(route('reviewer.houses'));
            $response->assertDontSee(route('caretaker.houses'));
        });

        it('generates correct house route for manager', function () {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('href="' . route('management.houses') . '"');
            $response->assertDontSee(route('houses'));
            $response->assertDontSee(route('reviewer.houses'));
            $response->assertDontSee(route('caretaker.houses'));
        });

        it('generates correct house route for reviewer', function () {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('href="' . route('reviewer.houses') . '"');
            $response->assertDontSee(route('houses'));
            $response->assertDontSee(route('management.houses'));
            $response->assertDontSee(route('caretaker.houses'));
        });

        it('generates correct house route for caretaker with estate parameter', function () {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            // Should contain the base route
            $response->assertSee(route('caretaker.houses'));
            
            // Should contain the route generation logic with estateId parameter
            $response->assertSee("auth()->user()->isAdmin() ? route('houses') : (auth()->user()->isManager() ? route('management.houses') : (auth()->user()->isReviewer() ? route('reviewer.houses') : route('caretaker.houses', ['estateId' => auth()->user()->assignedEstates()->first()->id ?? ''])))");
        });

        it('generates correct house route for resident', function () {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            // Resident should see houses route (their own houses)
            $response->assertSee('href="' . route('houses') . '"');
        });
    });

    describe('Contact Routes', function () {
        it('generates correct contact route for admin', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="' . route('contacts') . '"');
            $response->assertDontSee(route('management.contacts'));
            $response->assertDontSee(route('reviewer.contacts'));
            $response->assertDontSee(route('caretaker.contacts'));
        });

        it('generates correct contact route for manager', function () {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('href="' . route('management.contacts') . '"');
            $response->assertDontSee(route('contacts'));
            $response->assertDontSee(route('reviewer.contacts'));
            $response->assertDontSee(route('caretaker.contacts'));
        });

        it('generates correct contact route for reviewer', function () {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('href="' . route('reviewer.contacts') . '"');
            $response->assertDontSee(route('contacts'));
            $response->assertDontSee(route('management.contacts'));
            $response->assertDontSee(route('caretaker.contacts'));
        });

        it('generates correct contact route for caretaker', function () {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('href="' . route('caretaker.contacts') . '"');
            $response->assertDontSee(route('contacts'));
            $response->assertDontSee(route('management.contacts'));
            $response->assertDontSee(route('reviewer.contacts'));
        });

        it('does not show contact route for resident', function () {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertDontSee(route('contacts'));
            $response->assertDontSee(route('management.contacts'));
            $response->assertDontSee(route('reviewer.contacts'));
            $response->assertDontSee(route('caretaker.contacts'));
        });
    });

    describe('Reading Routes', function () {
        it('generates correct record readings route for admin', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="' . route('readings.create') . '"');
            $response->assertDontSee(route('caretaker.readings.create'));
        });

        it('generates correct record readings route for caretaker', function () {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('href="' . route('caretaker.readings.create') . '"');
            $response->assertDontSee(route('readings.create'));
        });

        it('generates correct review readings route for admin', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="' . route('readings.review') . '"');
            $response->assertDontSee(route('reviewer.readings.pending'));
        });

        it('generates correct review readings route for reviewer', function () {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('href="' . route('reviewer.readings.pending') . '"');
            $response->assertDontSee(route('readings.review'));
        });

        it('generates correct all readings route for each role', function () {
            // Admin
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');
            $response->assertSee('href="' . route('readings.index') . '"');

            // Caretaker
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');
            $response->assertSee('href="' . route('caretaker.readings') . '"');

            // Reviewer
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');
            $response->assertSee('href="' . route('reviewer.readings') . '"');

            // Resident
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');
            $response->assertSee('href="' . route('resident.readings') . '"');
        });
    });

    describe('Billing Routes', function () {
        it('generates correct billing route for admin', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="' . route('billing.index') . '"');
            $response->assertDontSee(route('reviewer.billing'));
            $response->assertDontSee(route('resident.invoices'));
        });

        it('generates correct billing route for reviewer', function () {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');

            $response->assertSee('href="' . route('reviewer.billing') . '"');
            $response->assertDontSee(route('billing.index'));
            $response->assertDontSee(route('resident.invoices'));
        });

        it('generates correct billing route for resident', function () {
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');

            $response->assertSee('href="' . route('resident.invoices') . '"');
            $response->assertDontSee(route('billing.index'));
            $response->assertDontSee(route('reviewer.billing'));
        });

        it('does not show billing route for manager and caretaker', function () {
            // Manager
            $response = $this->actingAs($this->manager)
                ->get('/management.dashboard');
            $response->assertDontSee(route('billing.index'));
            $response->assertDontSee(route('reviewer.billing'));
            $response->assertDontSee(route('resident.invoices'));

            // Caretaker
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');
            $response->assertDontSee(route('billing.index'));
            $response->assertDontSee(route('reviewer.billing'));
            $response->assertDontSee(route('resident.invoices'));
        });
    });

    describe('Report Routes', function () {
        it('generates correct reports route for admin with dropdown', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');

            $response->assertSee(route('admin.reports.aging'));
            $response->assertSee(route('admin.reports.revenue'));
            $response->assertSee(route('admin.reports.customer-statement'));
        });

        it('generates correct reports route for manager', function () {
            $response = $this->actingAs($this->manager)
                ->get('/management.dashboard');

            $response->assertSee('href="' . route('management.reports') . '"');
            $response->assertDontSee(route('admin.reports.aging'));
        });

        it('generates correct reports route for reviewer', function () {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');

            $response->assertSee('href="' . route('reviewer.billing.reports') . '"');
            $response->assertDontSee(route('admin.reports.aging'));
        });

        it('generates correct reports route for resident', function () {
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');

            $response->assertSee('href="' . route('resident.reports') . '"');
            $response->assertDontSee(route('admin.reports.aging'));
        });

        it('does not show reports route for caretaker', function () {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');

            $response->assertDontSee(route('management.reports'));
            $response->assertDontSee(route('reviewer.billing.reports'));
            $response->assertDontSee(route('resident.reports'));
        });
    });

    describe('Admin Routes', function () {
        it('shows admin routes only to admin user', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');

            $response->assertSee('href="' . route('admin.users') . '"');
            $response->assertSee('href="' . route('admin.settings') . '"');
            $response->assertSee('href="' . route('admin.audit') . '"');
        });

        it('does not show admin routes to non-admin users', function () {
            $nonAdminUsers = [$this->manager, $this->reviewer, $this->caretaker, $this->resident];
            
            foreach ($nonAdminUsers as $user) {
                $route = match($user->role) {
                    UserRole::MANAGER => '/management.dashboard',
                    UserRole::REVIEWER => '/reviewer.dashboard',
                    UserRole::CARETAKER => '/caretaker.dashboard',
                    UserRole::RESIDENT => '/resident.dashboard',
                };

                $response = $this->actingAs($user)->get($route);
                $response->assertDontSee(route('admin.users'));
                $response->assertDontSee(route('admin.settings'));
                $response->assertDontSee(route('admin.audit'));
            }
        });
    });

    describe('Export Routes', function () {
        it('shows export route to users with export permissions', function () {
            $usersWithExport = [$this->admin, $this->manager, $this->reviewer, $this->resident];
            
            foreach ($usersWithExport as $user) {
                $route = match($user->role) {
                    UserRole::ADMIN => '/admin.dashboard',
                    UserRole::MANAGER => '/management.dashboard',
                    UserRole::REVIEWER => '/reviewer.dashboard',
                    UserRole::RESIDENT => '/resident.dashboard',
                };

                $response = $this->actingAs($user)->get($route);
                $response->assertSee('href="' . route('exports.index') . '"');
            }
        });

        it('does not show export route to caretaker', function () {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');

            $response->assertDontSee(route('exports.index'));
        });
    });
});