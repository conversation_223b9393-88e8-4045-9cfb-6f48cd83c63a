<?php

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Traits\SetsUpSpatiePermissions;
use Livewire\Livewire;

uses(RefreshDatabase::class, SetsUpSpatiePermissions::class);

describe('Sidebar Permissions', function () {
    beforeEach(function () {
        // Set up Spatie permissions for tests
        $this->setUpSpatiePermissions();

        // Create test users for each role
        $this->admin = User::factory()->create(['role' => UserRole::ADMIN]);
        $this->manager = User::factory()->create(['role' => UserRole::MANAGER]);
        $this->reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
        $this->caretaker = User::factory()->create(['role' => UserRole::CARETAKER]);
        $this->resident = User::factory()->create(['role' => UserRole::RESIDENT]);
    });

    describe('Admin User Sidebar', function () {
        it('shows all admin-specific navigation items', function () {
            $response = $this->actingAs($this->admin)
                ->get('/dashboard')
                ->assertRedirect('/admin/dashboard');

            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('Admin Dashboard');
            $response->assertSee('Users');
            $response->assertSee('Estate Assignments');
            $response->assertSee('Team Management');
            $response->assertSee('System Settings');
            $response->assertSee('Audit Logs');
        });

        it('shows admin-level analytics links', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('Analytics');
        });

        it('shows admin reports dropdown', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('Reports');
            $response->assertSee('Aging Report');
            $response->assertSee('Revenue Report');
        });

        it('hides role-specific items that admin does not need', function () {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            // Admin should not see caretaker-specific record readings link
            $response->assertDontSee('Record Readings');
        });
    });

    describe('Manager User Sidebar', function () {
        it('shows manager-specific navigation items', function () {
            $response = $this->actingAs($this->manager)
                ->get('/dashboard')
                ->assertRedirect('/management/dashboard');

            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('Management Dashboard');
            $response->assertSee('Team');
        });

        it('shows manager-level analytics but not admin analytics', function () {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('Estate Analytics');
            // Should not see general analytics link
            $response->assertDontSee('Analytics');
        });

        it('shows manager reports but not admin reports dropdown', function () {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('Reports');
            $response->assertDontSee('Aging Report'); // This is in admin dropdown
        });

        it('hides admin-specific items', function () {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertDontSee('User Management');
            $response->assertDontSee('System Settings');
            $response->assertDontSee('Audit Logs');
        });
    });

    describe('Reviewer User Sidebar', function () {
it('shows reviewer-specific navigation items', function () {
            $response = $this->actingAs($this->reviewer)
                ->get('/dashboard')
                ->assertRedirect('/reviewer.dashboard');

            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');

            $response->assertSee('Reviewer Dashboard');
            $response->assertSee('Reading Review');
        });

        it('shows reviewer billing and reports', function () {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('Billing');
            $response->assertSee('Reports');
        });

        it('hides management and admin items', function () {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertDontSee('Estate Analytics');
            $response->assertDontSee('Team');
            $response->assertDontSee('User Management');
            $response->assertDontSee('System Settings');
        });
    });

    describe('Caretaker User Sidebar', function () {
        it('shows caretaker-specific navigation items', function () {
            $response = $this->actingAs($this->caretaker)
                ->get('/dashboard')
                ->assertRedirect('/caretaker/dashboard');

            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('Caretaker Dashboard');
            $response->assertSee('Record Readings');
        });

        it('shows limited analytics and reports', function () {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('Reports');
            $response->assertDontSee('Estate Analytics');
            $response->assertDontSee('Analytics');
        });

        it('hides billing and admin features', function () {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertDontSee('Billing');
            $response->assertDontSee('User Management');
            $response->assertDontSee('System Settings');
            $response->assertDontSee('Audit Logs');
        });
    });

    describe('Resident User Sidebar', function () {
        it('shows resident-specific navigation items', function () {
            $response = $this->actingAs($this->resident)
                ->get('/dashboard')
                ->assertRedirect('/resident/dashboard');

            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertSee('Dashboard');
            $response->assertSee('Billing');
        });

        it('shows resident-specific analytics and reports', function () {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertSee('Analytics');
            $response->assertSee('Reports');
        });

        it('hides all management and operational features', function () {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertDontSee('Estates');
            $response->assertDontSee('Houses');
            $response->assertDontSee('Contacts');
            $response->assertDontSee('Water Operations');
            $response->assertDontSee('Administration');
        });
    });

    describe('Common Navigation Items', function () {
        it('shows estates link to users with appropriate permissions', function () {
            // Admin should see estates link
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');
            $response->assertSee('Estates');

            // Manager should see estates link
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');
            $response->assertSee('Estates');

            // Reviewer should see estates link
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');
            $response->assertSee('Estates');

            // Caretaker should see estates link
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');
            $response->assertSee('Estates');

            // Resident should not see estates link
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');
            $response->assertDontSee('Estates');
        });

        it('shows houses link to users with appropriate permissions', function () {
            // Admin should see houses link
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');
            $response->assertSee('Houses');

            // Manager should see houses link
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');
            $response->assertSee('Houses');

            // Reviewer should see houses link
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');
            $response->assertSee('Houses');

            // Caretaker should see houses link
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');
            $response->assertSee('Houses');

            // Resident should see houses link (own houses)
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');
            $response->assertSee('Houses');
        });

        it('shows contacts link to users with appropriate permissions', function () {
            // Admin should see contacts link
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');
            $response->assertSee('Contacts');

            // Manager should see contacts link
            $response = $this->actingAs($this->manager)
                ->get('/management.dashboard');
            $response->assertSee('Contacts');

            // Reviewer should see contacts link
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');
            $response->assertSee('Contacts');

            // Caretaker should see contacts link
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');
            $response->assertSee('Contacts');

            // Resident should see contacts link (own contacts)
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');
            $response->assertSee('Contacts');
        });
    });

    describe('Route Generation Logic', function () {
        it('generates correct estate routes for each role', function () {
            // Test admin estate route
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');
            $response->assertSee('href="' . route('estates') . '"');

            // Test manager estate route
            $response = $this->actingAs($this->manager)
                ->get('/management.dashboard');
            $response->assertSee('href="' . route('management.estates') . '"');

            // Test reviewer estate route
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');
            $response->assertSee('href="' . route('reviewer.estates') . '"');

            // Test caretaker estate route
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');
            $response->assertSee('href="' . route('caretaker.estates') . '"');
        });

        it('generates correct estate analytics routes for each role', function () {
            // Test admin estate analytics route
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');
            $response->assertSee('href="' . route('estates.analytics') . '"');

            // Test manager estate analytics route
            $response = $this->actingAs($this->manager)
                ->get('/management.dashboard');
            $response->assertSee('href="' . route('management.estates.analytics') . '"');
        });

        it('generates correct billing routes for each role', function () {
            // Test admin billing route
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');
            $response->assertSee('href="' . route('billing.index') . '"');

            // Test reviewer billing route
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');
            $response->assertSee('href="' . route('reviewer.billing') . '"');

            // Test resident billing route
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');
            $response->assertSee('href="' . route('resident.invoices') . '"');
        });
    });

    describe('Permission-based Visibility', function () {
        it('respects analytics permissions', function () {
            // Admin has analytics.view_all - should see both analytics links
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');
            $response->assertSee('Estate Analytics');
            $response->assertSee('Analytics');

            // Manager has analytics.view_assigned - should see estate analytics only
            $response = $this->actingAs($this->manager)
                ->get('/management.dashboard');
            $response->assertSee('Estate Analytics');
            $response->assertDontSee('Analytics');

            // Resident has analytics.view_own - should see analytics only
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');
            $response->assertDontSee('Estate Analytics');
            $response->assertSee('Analytics');
        });

        it('respects reading permissions', function () {
            // Caretaker has readings.create_assigned - should see record readings
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');
            $response->assertSee('Record Readings');

            // Reviewer has readings.review_assigned - should see review readings
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');
            $response->assertSee('Review Readings');

            // Admin has readings.create_all - should see record readings
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');
            $response->assertSee('Record Readings');

            // Manager has readings.review_assigned - should see review readings
            $response = $this->actingAs($this->manager)
                ->get('/management.dashboard');
            $response->assertSee('Review Readings');

            // Resident has readings.view_own - should see all readings
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');
            $response->assertSee('All Readings');
        });

        it('respects administration permissions', function () {
            // Only admin should see administration section
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');
            $response->assertSee('Administration');

            $nonAdminUsers = [$this->manager, $this->reviewer, $this->caretaker, $this->resident];
            foreach ($nonAdminUsers as $user) {
                $response = $this->actingAs($user)
                    ->get('/dashboard');
                $response->assertDontSee('Administration');
            }
        });
    });
});