<?php

use App\Models\AccountTransaction;
use App\Models\House;
use App\Models\HouseAccount;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

it('can create house account', function () {
    $house = House::factory()->create();

    $account = HouseAccount::create([
        'house_id' => $house->id,
        'current_balance' => 0,
        'total_credit' => 0,
        'total_debit' => 0,
    ]);

    expect($account)->not->toBeNull();
    expect($account->house_id)->toEqual($house->id);
    expect($account->current_balance)->toEqual(0);
    expect($account->total_credit)->toEqual(0);
    expect($account->total_debit)->toEqual(0);
});

it('has unique house id', function () {
    $house = House::factory()->create();

    HouseAccount::create([
        'house_id' => $house->id,
        'current_balance' => 0,
        'total_credit' => 0,
        'total_debit' => 0,
    ]);

    $this->expectException(\Illuminate\Database\QueryException::class);

    HouseAccount::create([
        'house_id' => $house->id,
        'current_balance' => 100,
        'total_credit' => 100,
        'total_debit' => 0,
    ]);
});

it('belongs to house', function () {
    $house = House::factory()->create();
    $account = HouseAccount::factory()->create(['house_id' => $house->id]);

    expect($account->house)->toBeInstanceOf(House::class);
    expect($account->house->id)->toEqual($house->id);
});

it('has many transactions', function () {
    $account = HouseAccount::factory()->create();
    $transaction = AccountTransaction::factory()->create(['house_account_id' => $account->id]);

    expect($account->transactions->contains($transaction))->toBeTrue();
    expect($account->transactions)->toHaveCount(1);
});

it('returns current balance attribute', function () {
    $account = HouseAccount::factory()->create(['current_balance' => 1500.50]);

    expect($account->getCurrentBalanceAttribute())->toEqual(1500.50);
    expect($account->current_balance)->toEqual(1500.50);
});

it('can update balance', function () {
    $account = HouseAccount::factory()->create(['current_balance' => 1000]);

    $account->update(['current_balance' => 1500]);

    expect($account->fresh()->current_balance)->toEqual(1500);
});

it('tracks last transaction date', function () {
    $account = HouseAccount::factory()->create();

    expect($account->last_transaction_date)->toBeNull();

    $transaction = AccountTransaction::factory()->create([
        'house_account_id' => $account->id,
        'created_at' => now()->subDays(1),
    ]);

    $account->refresh();
    expect($account->last_transaction_date)->toEqual($transaction->created_at);
});

it('calculates total credit correctly', function () {
    $account = HouseAccount::factory()->create(['total_credit' => 5000]);

    expect($account->total_credit)->toEqual(5000);
});

it('calculates total debit correctly', function () {
    $account = HouseAccount::factory()->create(['total_debit' => 3000]);

    expect($account->total_debit)->toEqual(3000);
});

it('has proper fillable fields', function () {
    $account = new HouseAccount;

    $expectedFillable = [
        'house_id',
        'current_balance',
        'total_credit',
        'total_debit',
        'last_transaction_date',
    ];

    expect($account->getFillable())->toEqual($expectedFillable);
});

it('has proper casts', function () {
    $account = new HouseAccount;

    $expectedCasts = [
        'current_balance' => 'decimal:2',
        'total_credit' => 'decimal:2',
        'total_debit' => 'decimal:2',
        'last_transaction_date' => 'datetime',
    ];

    $actualCasts = $account->getCasts();

    foreach ($expectedCasts as $field => $cast) {
        expect($actualCasts[$field])->toEqual($cast);
    }
});
