<?php

declare(strict_types=1);

use App\Exports\AgingReportExport;
use App\Exports\RevenueReportExport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Maatwebsite\Excel\Facades\Excel;

uses(RefreshDatabase::class);

describe('AgingReportExport', function () {
    beforeEach(function () {
        $this->sampleAgingData = [
            'current' => 5000.00,
            'days_1_30' => 3000.00,
            'days_31_60' => 2000.00,
            'days_61_90' => 1000.00,
            'days_over_90' => 500.00,
            'total_outstanding' => 11500.00,
        ];
    });

    test('can be instantiated with aging report data', function () {
        $export = new AgingReportExport($this->sampleAgingData);

        expect($export)->toBeInstanceOf(AgingReportExport::class);
    });

    test('returns correct number of sheets', function () {
        $export = new AgingReportExport($this->sampleAgingData);
        $sheets = $export->sheets();

        expect($sheets)->toHaveCount(2);
    });

    test('sheets have correct titles', function () {
        $export = new AgingReportExport($this->sampleAgingData);
        $sheets = $export->sheets();

        expect($sheets[0]->title())->toBe('Aging Summary');
        expect($sheets[1]->title())->toBe('Aging Breakdown');
    });

    test('aging summary sheet has correct headings', function () {
        $export = new AgingReportExport($this->sampleAgingData);
        $sheets = $export->sheets();
        $summarySheet = $sheets[0];

        $headings = $summarySheet->headings();

        expect($headings)->toBe([
            'Current (KES)',
            '1-30 Days (KES)',
            '31-60 Days (KES)',
            '61-90 Days (KES)',
            'Over 90 Days (KES)',
            'Total Outstanding (KES)',
        ]);
    });

    test('aging summary sheet contains correct data', function () {
        $export = new AgingReportExport($this->sampleAgingData);
        $sheets = $export->sheets();
        $summarySheet = $sheets[0];

        $collection = $summarySheet->collection();
        $data = $collection->first();

        expect($data['current'])->toBe(5000.00);
        expect($data['days_1_30'])->toBe(3000.00);
        expect($data['days_31_60'])->toBe(2000.00);
        expect($data['days_61_90'])->toBe(1000.00);
        expect($data['days_over_90'])->toBe(500.00);
        expect($data['total_outstanding'])->toBe(11500.00);
    });

    test('aging breakdown sheet has correct headings', function () {
        $export = new AgingReportExport($this->sampleAgingData);
        $sheets = $export->sheets();
        $breakdownSheet = $sheets[1];

        $headings = $breakdownSheet->headings();

        expect($headings)->toBe([
            'Category',
            'Amount (KES)',
            'Percentage (%)',
        ]);
    });

    test('aging breakdown sheet contains correct categories', function () {
        $export = new AgingReportExport($this->sampleAgingData);
        $sheets = $export->sheets();
        $breakdownSheet = $sheets[1];

        $collection = $breakdownSheet->collection();

        expect($collection)->toHaveCount(5);

        $categories = $collection->pluck('category')->toArray();
        expect($categories)->toBe([
            'Current',
            '1-30 Days',
            '31-60 Days',
            '61-90 Days',
            'Over 90 Days',
        ]);
    });

    test('aging breakdown sheet calculates percentages correctly', function () {
        $export = new AgingReportExport($this->sampleAgingData);
        $sheets = $export->sheets();
        $breakdownSheet = $sheets[1];

        $collection = $breakdownSheet->collection();
        $mappedData = $collection->map(fn ($row) => $breakdownSheet->map($row));

        // Check that percentages are calculated correctly
        $currentRow = $mappedData->first();
        expect($currentRow[2])->toBe(round((5000.00 / 11500.00) * 100, 2)); // 43.48%

        $over90Row = $mappedData->last();
        expect($over90Row[2])->toBe(round((500.00 / 11500.00) * 100, 2)); // 4.35%
    });

    test('aging breakdown sheet handles zero total outstanding', function () {
        $zeroData = [
            'current' => 0,
            'days_1_30' => 0,
            'days_31_60' => 0,
            'days_61_90' => 0,
            'days_over_90' => 0,
            'total_outstanding' => 0,
        ];

        $export = new AgingReportExport($zeroData);
        $sheets = $export->sheets();
        $breakdownSheet = $sheets[1];

        $collection = $breakdownSheet->collection();
        $mappedData = $collection->map(fn ($row) => $breakdownSheet->map($row));

        // All percentages should be 0 when total is 0
        foreach ($mappedData as $row) {
            expect($row[2])->toBe(0.0);
        }
    });

    test('can export aging report to excel', function () {
        Excel::fake();

        $export = new AgingReportExport($this->sampleAgingData);

        Excel::download($export, 'aging-report.xlsx');

        Excel::assertDownloaded('aging-report.xlsx', function ($export) {
            return $export instanceof AgingReportExport;
        });
    });
});

describe('RevenueReportExport', function () {
    beforeEach(function () {
        $this->sampleRevenueData = [
            'total_revenue' => 50000.00,
            'total_invoices' => 25,
            'average_invoice_amount' => 2000.00,
            'collection_rate' => 85.5,
            'total_billed' => 58479.50,
            'total_collected' => 50000.00,
            'revenue_by_estate' => [
                'Estate A' => 30000.00,
                'Estate B' => 20000.00,
            ],
            'revenue_by_payment_method' => [
                'M-Pesa' => 35000.00,
                'Cash' => 15000.00,
            ],
            'revenue_by_month' => [
                '2024-01' => 25000.00,
                '2024-02' => 25000.00,
            ],
        ];
    });

    test('can be instantiated with revenue report data', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);

        expect($export)->toBeInstanceOf(RevenueReportExport::class);
    });

    test('returns correct number of sheets', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();

        expect($sheets)->toHaveCount(4);
    });

    test('sheets have correct titles', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();

        expect($sheets[0]->title())->toBe('Revenue Summary');
        expect($sheets[1]->title())->toBe('Revenue by Estate');
        expect($sheets[2]->title())->toBe('Revenue by Payment Method');
        expect($sheets[3]->title())->toBe('Revenue by Month');
    });

    test('revenue summary sheet has correct headings', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();
        $summarySheet = $sheets[0];

        $headings = $summarySheet->headings();

        expect($headings)->toBe([
            'Total Revenue (KES)',
            'Total Invoices',
            'Average Invoice Amount (KES)',
            'Collection Rate (%)',
            'Total Billed (KES)',
            'Total Collected (KES)',
        ]);
    });

    test('revenue summary sheet contains correct data', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();
        $summarySheet = $sheets[0];

        $collection = $summarySheet->collection();
        $data = $collection->first();

        expect($data['total_revenue'])->toBe(50000.00);
        expect($data['total_invoices'])->toBe(25);
        expect($data['average_invoice_amount'])->toBe(2000.00);
        expect($data['collection_rate'])->toBe(85.5);
        expect($data['total_billed'])->toBe(58479.50);
        expect($data['total_collected'])->toBe(50000.00);
    });

    test('revenue by estate sheet has correct headings', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();
        $estateSheet = $sheets[1];

        $headings = $estateSheet->headings();

        expect($headings)->toBe([
            'Estate',
            'Revenue (KES)',
            'Percentage (%)',
        ]);
    });

    test('revenue by estate sheet contains correct data', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();
        $estateSheet = $sheets[1];

        $collection = $estateSheet->collection();
        $mappedData = $collection->map(fn ($row) => $estateSheet->map($row));

        expect($collection)->toHaveCount(2);

        $firstRow = $mappedData->first();
        expect($firstRow[0])->toBe('Estate A');
        expect($firstRow[1])->toBe(30000.00);
        expect($firstRow[2])->toBe(60.0); // 30000/50000 * 100

        $secondRow = $mappedData->last();
        expect($secondRow[0])->toBe('Estate B');
        expect($secondRow[1])->toBe(20000.00);
        expect($secondRow[2])->toBe(40.0); // 20000/50000 * 100
    });

    test('revenue by payment method sheet has correct headings', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();
        $paymentSheet = $sheets[2];

        $headings = $paymentSheet->headings();

        expect($headings)->toBe([
            'Payment Method',
            'Revenue (KES)',
            'Percentage (%)',
        ]);
    });

    test('revenue by payment method sheet contains correct data', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();
        $paymentSheet = $sheets[2];

        $collection = $paymentSheet->collection();
        $mappedData = $collection->map(fn ($row) => $paymentSheet->map($row));

        expect($collection)->toHaveCount(2);

        $firstRow = $mappedData->first();
        expect($firstRow[0])->toBe('M-Pesa');
        expect($firstRow[1])->toBe(35000.00);
        expect($firstRow[2])->toBe(70.0); // 35000/50000 * 100

        $secondRow = $mappedData->last();
        expect($secondRow[0])->toBe('Cash');
        expect($secondRow[1])->toBe(15000.00);
        expect($secondRow[2])->toBe(30.0); // 15000/50000 * 100
    });

    test('revenue by month sheet has correct headings', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();
        $monthSheet = $sheets[3];

        $headings = $monthSheet->headings();

        expect($headings)->toBe([
            'Month',
            'Revenue (KES)',
            'Percentage (%)',
        ]);
    });

    test('revenue by month sheet contains correct data', function () {
        $export = new RevenueReportExport($this->sampleRevenueData);
        $sheets = $export->sheets();
        $monthSheet = $sheets[3];

        $collection = $monthSheet->collection();
        $mappedData = $collection->map(fn ($row) => $monthSheet->map($row));

        expect($collection)->toHaveCount(2);

        $firstRow = $mappedData->first();
        expect($firstRow[0])->toBe('Jan 2024');
        expect($firstRow[1])->toBe(25000.00);
        expect($firstRow[2])->toBe(50.0); // 25000/50000 * 100

        $secondRow = $mappedData->last();
        expect($secondRow[0])->toBe('Feb 2024');
        expect($secondRow[1])->toBe(25000.00);
        expect($secondRow[2])->toBe(50.0); // 25000/50000 * 100
    });

    test('revenue sheets handle zero total revenue', function () {
        $zeroData = [
            'total_revenue' => 0,
            'total_invoices' => 0,
            'average_invoice_amount' => 0,
            'collection_rate' => 0,
            'total_billed' => 0,
            'total_collected' => 0,
            'revenue_by_estate' => [
                'Estate A' => 0,
                'Estate B' => 0,
            ],
            'revenue_by_payment_method' => [
                'M-Pesa' => 0,
                'Cash' => 0,
            ],
            'revenue_by_month' => [
                '2024-01' => 0,
                '2024-02' => 0,
            ],
        ];

        $export = new RevenueReportExport($zeroData);
        $sheets = $export->sheets();

        // Test estate sheet
        $estateSheet = $sheets[1];
        $estateCollection = $estateSheet->collection();
        $estateMappedData = $estateCollection->map(fn ($row) => $estateSheet->map($row));

        foreach ($estateMappedData as $row) {
            expect($row[2])->toBe(0.0); // All percentages should be 0
        }

        // Test payment method sheet
        $paymentSheet = $sheets[2];
        $paymentCollection = $paymentSheet->collection();
        $paymentMappedData = $paymentCollection->map(fn ($row) => $paymentSheet->map($row));

        foreach ($paymentMappedData as $row) {
            expect($row[2])->toBe(0.0); // All percentages should be 0
        }

        // Test month sheet
        $monthSheet = $sheets[3];
        $monthCollection = $monthSheet->collection();
        $monthMappedData = $monthCollection->map(fn ($row) => $monthSheet->map($row));

        foreach ($monthMappedData as $row) {
            expect($row[2])->toBe(0.0); // All percentages should be 0
        }
    });

    test('can export revenue report to excel', function () {
        Excel::fake();

        $export = new RevenueReportExport($this->sampleRevenueData);

        Excel::download($export, 'revenue-report.xlsx');

        Excel::assertDownloaded('revenue-report.xlsx', function ($export) {
            return $export instanceof RevenueReportExport;
        });
    });

    test('handles empty revenue arrays gracefully', function () {
        $emptyData = [
            'total_revenue' => 0,
            'total_invoices' => 0,
            'average_invoice_amount' => 0,
            'collection_rate' => 0,
            'total_billed' => 0,
            'total_collected' => 0,
            'revenue_by_estate' => [],
            'revenue_by_payment_method' => [],
            'revenue_by_month' => [],
        ];

        $export = new RevenueReportExport($emptyData);
        $sheets = $export->sheets();

        // All breakdown sheets should have empty collections
        expect($sheets[1]->collection())->toHaveCount(0); // Estate
        expect($sheets[2]->collection())->toHaveCount(0); // Payment method
        expect($sheets[3]->collection())->toHaveCount(0); // Month
    });
});
