<?php

use App\Enums\UserRole;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('user has role method works with enum', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);

    expect($user->hasRole(UserRole::MANAGER))->toBeTrue();
    expect($user->hasRole(UserRole::REVIEWER))->toBeFalse();
    expect($user->hasRole(UserRole::CARETAKER))->toBeFalse();
});

test('is management method returns correct boolean', function () {
    $managementUser = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewerUser = User::factory()->create(['role' => UserRole::REVIEWER]);
    $caretakerUser = User::factory()->create(['role' => UserRole::CARETAKER]);

    expect($managementUser->isManager())->toBeTrue();
    expect($reviewerUser->isManager())->toBeFalse();
    expect($caretakerUser->isManager())->toBeFalse();
});

test('is reviewer method returns correct boolean', function () {
    $managementUser = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewerUser = User::factory()->create(['role' => UserRole::REVIEWER]);
    $caretakerUser = User::factory()->create(['role' => UserRole::CARETAKER]);

    expect($managementUser->isReviewer())->toBeFalse();
    expect($reviewerUser->isReviewer())->toBeTrue();
    expect($caretakerUser->isReviewer())->toBeFalse();
});

test('is caretaker method returns correct boolean', function () {
    $managementUser = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewerUser = User::factory()->create(['role' => UserRole::REVIEWER]);
    $caretakerUser = User::factory()->create(['role' => UserRole::CARETAKER]);

    expect($managementUser->isCaretaker())->toBeFalse();
    expect($reviewerUser->isCaretaker())->toBeFalse();
    expect($caretakerUser->isCaretaker())->toBeTrue();
});

test('role casting works correctly', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);

    expect($user->role)->toBeInstanceOf(UserRole::class);
    expect($user->role)->toEqual(UserRole::MANAGER);
});
