<?php

declare(strict_types=1);

use App\Enums\UserRole;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Enhanced Invoice Model', function () {
    beforeEach(function () {
        $this->estate = Estate::factory()->create();
        $this->house = House::factory()->create(['estate_id' => $this->estate->id]);
        $this->waterRate = WaterRate::factory()->create(['estate_id' => $this->estate->id]);
        $this->meterReading = MeterReading::factory()->create(['house_id' => $this->house->id]);
        $this->manager = User::factory()->create(['role' => UserRole::MANAGER]);
        $this->reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
    });

    it('can be submitted for approval', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
        ]);

        $invoice->submitForApproval($this->manager);

        expect($invoice->fresh()->status)->toBe('submitted');
        expect($invoice->fresh()->submitted_by)->toBe($this->manager->id);
        expect($invoice->fresh()->submitted_at)->not()->toBeNull();
    });

    it('can be approved by reviewer', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);

        $invoice->approve($this->reviewer);

        expect($invoice->fresh()->status)->toBe('approved');
        expect($invoice->fresh()->approved_by)->toBe($this->reviewer->id);
        expect($invoice->fresh()->approved_at)->not()->toBeNull();
    });

    it('cannot be approved if not submitted', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
        ]);

        expect(fn () => $invoice->approve($this->reviewer))
            ->toThrow(\InvalidArgumentException::class, 'Invoice must be submitted before approval');
    });

    it('can be sent after approval', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'approved',
            'approved_by' => $this->reviewer->id,
            'approved_at' => now(),
            'pdf_path' => 'invoices/test.pdf', // Required for canBeSent()
        ]);

        $invoice->markAsSent();

        expect($invoice->fresh()->status)->toBe('sent');
        expect($invoice->fresh()->sent_at)->not()->toBeNull();
    });

    it('cannot be sent if not approved', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
        ]);

        expect(fn () => $invoice->markAsSent())
            ->toThrow(\InvalidArgumentException::class, 'Invoice must be approved before sending');
    });

    it('tracks reminder count and last reminder', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'sent',
            'reminder_count' => 0,
        ]);

        $invoice->recordReminder();

        expect($invoice->fresh()->reminder_count)->toBe(1);
        expect($invoice->fresh()->last_reminder_at)->not()->toBeNull();
    });

    it('can schedule disconnection', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'overdue',
        ]);

        $disconnectionDate = now()->addDays(7)->startOfDay();
        $invoice->scheduleDisconnection($disconnectionDate);

        expect($invoice->fresh()->disconnection_scheduled)->toEqual($disconnectionDate);
    });

    it('has relationship with submitter', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
        ]);

        expect($invoice->submittedBy)->toBeInstanceOf(User::class);
        expect($invoice->submittedBy->id)->toBe($this->manager->id);
    });

    it('has relationship with approver', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'approved',
            'approved_by' => $this->reviewer->id,
        ]);

        expect($invoice->approvedBy)->toBeInstanceOf(User::class);
        expect($invoice->approvedBy->id)->toBe($this->reviewer->id);
    });

    it('can check if it can be submitted', function () {
        $draftInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
        ]);

        $submittedInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
        ]);

        expect($draftInvoice->canBeSubmitted())->toBeTrue();
        expect($submittedInvoice->canBeSubmitted())->toBeFalse();
    });

    it('can check if it can be approved', function () {
        $submittedInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
        ]);

        $approvedInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'approved',
        ]);

        expect($submittedInvoice->canBeApproved())->toBeTrue();
        expect($approvedInvoice->canBeApproved())->toBeFalse();
    });

    it('can check if it can be sent', function () {
        $approvedInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'approved',
            'pdf_path' => 'invoices/test.pdf', // Required for canBeSent()
        ]);

        $sentInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'sent',
        ]);

        expect($approvedInvoice->canBeSent())->toBeTrue();
        expect($sentInvoice->canBeSent())->toBeFalse();
    });

    it('returns correct status label', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
        ]);

        expect($invoice->status_label)->toBe('Submitted');
    });

    it('returns correct status color', function () {
        $submittedInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
        ]);

        $approvedInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'approved',
        ]);

        expect($submittedInvoice->status_color)->toBe('yellow');
        expect($approvedInvoice->status_color)->toBe('blue');
    });
});
