<?php

use App\Models\AccountTransaction;
use App\Models\HouseAccount;
use App\Models\Invoice;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

it('can create account transaction', function () {
    $houseAccount = HouseAccount::factory()->create();
    $user = User::factory()->create();

    $transaction = AccountTransaction::create([
        'house_account_id' => $houseAccount->id,
        'transaction_type' => 'invoice',
        'reference_type' => 'Invoice',
        'reference_id' => 1,
        'amount' => 1500.00,
        'balance_before' => 1000.00,
        'balance_after' => 2500.00,
        'description' => 'Invoice #INV-001',
        'user_id' => $user->id,
    ]);

    expect($transaction)->not->toBeNull();
    expect($transaction->house_account_id)->toEqual($houseAccount->id);
    expect($transaction->transaction_type)->toEqual('invoice');
    expect($transaction->reference_type)->toEqual('Invoice');
    expect($transaction->reference_id)->toEqual(1);
    expect($transaction->amount)->toEqual(1500.00);
    expect($transaction->balance_before)->toEqual(1000.00);
    expect($transaction->balance_after)->toEqual(2500.00);
    expect($transaction->description)->toEqual('Invoice #INV-001');
    expect($transaction->user_id)->toEqual($user->id);
});

it('belongs to house account', function () {
    $houseAccount = HouseAccount::factory()->create();
    $transaction = AccountTransaction::factory()->create(['house_account_id' => $houseAccount->id]);

    expect($transaction->houseAccount)->toBeInstanceOf(HouseAccount::class);
    expect($transaction->houseAccount->id)->toEqual($houseAccount->id);
});

it('belongs to user', function () {
    $user = User::factory()->create();
    $transaction = AccountTransaction::factory()->create(['user_id' => $user->id]);

    expect($transaction->user)->toBeInstanceOf(User::class);
    expect($transaction->user->id)->toEqual($user->id);
});

it('can have null user', function () {
    $transaction = AccountTransaction::factory()->create(['user_id' => null]);

    expect($transaction->user)->toBeNull();
});

it('has morphable reference', function () {
    $houseAccount = HouseAccount::factory()->create();
    $invoice = Invoice::factory()->create();

    $transaction = AccountTransaction::factory()->create([
        'house_account_id' => $houseAccount->id,
        'transaction_type' => 'invoice',
        'reference_type' => 'Invoice',
        'reference_id' => $invoice->id,
        'amount' => 1500.00,
        'balance_before' => 0,
        'balance_after' => 1500.00,
        'description' => 'Invoice #INV-001',
    ]);

    expect($transaction->reference)->toBeInstanceOf(Invoice::class);
    expect($transaction->reference->id)->toEqual($invoice->id);
});

it('supports different transaction types', function () {
    $houseAccount = HouseAccount::factory()->create();

    $types = ['invoice', 'payment', 'adjustment', 'credit_note'];

    foreach ($types as $type) {
        $transaction = AccountTransaction::factory()->create([
            'house_account_id' => $houseAccount->id,
            'transaction_type' => $type,
        ]);

        expect($transaction->transaction_type)->toEqual($type);
    }
});

it('supports different reference types', function () {
    $houseAccount = HouseAccount::factory()->create();

    $referenceTypes = ['Invoice', 'Payment', 'Adjustment'];

    foreach ($referenceTypes as $referenceType) {
        $transaction = AccountTransaction::factory()->create([
            'house_account_id' => $houseAccount->id,
            'reference_type' => $referenceType,
        ]);

        expect($transaction->reference_type)->toEqual($referenceType);
    }
});

it('has proper fillable fields', function () {
    $transaction = new AccountTransaction;

    $expectedFillable = [
        'house_account_id',
        'transaction_type',
        'reference_type',
        'reference_id',
        'amount',
        'balance_before',
        'balance_after',
        'description',
        'user_id',
    ];

    expect($transaction->getFillable())->toEqual($expectedFillable);
});

it('has proper casts', function () {
    $transaction = new AccountTransaction;

    $expectedCasts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
    ];

    $actualCasts = $transaction->getCasts();

    foreach ($expectedCasts as $field => $cast) {
        expect($actualCasts[$field])->toEqual($cast);
    }
});

it('can have nullable description', function () {
    $houseAccount = HouseAccount::factory()->create();

    $transaction = AccountTransaction::create([
        'house_account_id' => $houseAccount->id,
        'transaction_type' => 'invoice',
        'reference_type' => 'Invoice',
        'reference_id' => 1,
        'amount' => 1500.00,
        'balance_before' => 1000.00,
        'balance_after' => 2500.00,
        'description' => null,
    ]);

    expect($transaction->description)->toBeNull();
});

it('orders transactions by created at', function () {
    $houseAccount = HouseAccount::factory()->create();

    $transaction1 = AccountTransaction::factory()->create([
        'house_account_id' => $houseAccount->id,
        'created_at' => now()->subDays(2),
    ]);

    $transaction2 = AccountTransaction::factory()->create([
        'house_account_id' => $houseAccount->id,
        'created_at' => now()->subDays(1),
    ]);

    $transaction3 = AccountTransaction::factory()->create([
        'house_account_id' => $houseAccount->id,
        'created_at' => now(),
    ]);

    $transactions = $houseAccount->transactions()->orderBy('created_at')->get();

    expect($transactions[0]->id)->toEqual($transaction1->id);
    expect($transactions[1]->id)->toEqual($transaction2->id);
    expect($transactions[2]->id)->toEqual($transaction3->id);
});
