<?php

use App\Enums\UserRole;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('nav link component renders with basic attributes', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.nav-link href="/test" icon="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z">Test Link</x-navigation.nav-link>',
        ['title' => 'Test']
    );

    $view->assertSee('Test Link');
    $view->assertSee('href="/test"', false);
});

test('nav link component shows icon when provided', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.nav-link href="/test" icon="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z">Test Link</x-navigation.nav-link>',
        ['title' => 'Test']
    );

    $view->assertSee('stroke="currentColor"', false);
    $view->assertSee('viewBox="0 0 24 24"', false);
});

test('nav link component applies active state when route matches', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    // Simulate being on the dashboard route
    request()->setRouteResolver(function () {
        return new class
        {
            function getName()
            {
                return 'management.dashboard';
            }
            
            function named($name)
            {
                return $name === 'management.dashboard';
            }
        };
    });

    $view = $this->blade(
        '<x-navigation.nav-link href="/management/dashboard" active-route="management.dashboard">Dashboard</x-navigation.nav-link>',
        ['title' => 'Dashboard']
    );

    $view->assertSee('bg-blue-50 text-blue-600');
});

test('nav link component applies inactive state when route does not match', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    // Simulate being on a different route
    request()->setRouteResolver(function () {
        return new class
        {
            function getName()
            {
                return 'estates.index';
            }
        };
    });

    $view = $this->blade(
        '<x-navigation.nav-link href="/management/dashboard" active-route="management.dashboard">Dashboard</x-navigation.nav-link>',
        ['title' => 'Dashboard']
    );

    $view->assertSee('text-gray-700 hover:bg-gray-100');
});

test('nav link component respects permission checking', function () {
    $user = User::factory()->create(['role' => UserRole::CARETAKER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.nav-link href="/admin/users" permission="users.manage_all">Admin Users</x-navigation.nav-link>',
        ['title' => 'Test']
    );

    // Caretaker should not see admin users link
    $view->assertDontSee('Admin Users');
});

test('nav link component shows link when user has permission', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.nav-link href="/admin/users" permission="users.manage_all">Admin Users</x-navigation.nav-link>',
        ['title' => 'Test']
    );

    // Admin should see admin users link
    $view->assertSee('Admin Users');
});

test('nav link component works without permission attribute', function () {
    $user = User::factory()->create(['role' => UserRole::RESIDENT]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.nav-link href="/test">Test Link</x-navigation.nav-link>',
        ['title' => 'Test']
    );

    // Should show link when no permission is required
    $view->assertSee('Test Link');
});

test('nav link component handles dark mode classes', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.nav-link href="/test">Test Link</x-navigation.nav-link>',
        ['title' => 'Test']
    );

    $view->assertSee('dark:text-gray-300');
    $view->assertSee('dark:hover:bg-gray-800');
});

test('nav link component has proper accessibility attributes', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.nav-link href="/test">Test Link</x-navigation.nav-link>',
        ['title' => 'Test']
    );

    $view->assertSee('role="link"', false);
    $view->assertSee('tabindex="0"', false);
});
