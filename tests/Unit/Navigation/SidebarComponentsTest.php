<?php

use App\Enums\UserRole;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('admin sidebar component renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('Admin Dashboard');
    $view->assertSee('Users');
    $view->assertSee('System Settings');
    $view->assertSee('Estate Assignments');
    $view->assertSee('Team Management');
    $view->assertSee('Audit Logs');
    $view->assertSee('Estates');
    $view->assertSee('Houses');
    $view->assertSee('Contacts');
    $view->assertSee('Analytics');
    $view->assertSee('Reports');
    $view->assertSee('Aging Report');
    $view->assertSee('Revenue Report');
    $view->assertSee('Data Export');
});

test('manager sidebar component renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.manager-sidebar title="Management Dashboard" />',
        ['title' => 'Management Dashboard']
    );

    // Test basic structure and logo instead of permission-based navigation items
    $view->assertSee('Water Management');
    $view->assertSee('sidebar');
    $view->assertSee('navigation');
    $view->assertSee('Estate Management');
    $view->assertSee('Team Management');
    $view->assertSee('Reports & Analytics');
});

test('reviewer sidebar component renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::REVIEWER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.reviewer-sidebar title="Reviewer Dashboard" />',
        ['title' => 'Reviewer Dashboard']
    );

    // Test basic structure and section headers instead of permission-based navigation items
    $view->assertSee('Water Management');
    $view->assertSee('sidebar');
    $view->assertSee('navigation');
    $view->assertSee('Billing Management');
    $view->assertSee('Reading Review');
    $view->assertSee('Estate Information');
    $view->assertSee('Reports & Exports');
});

test('caretaker sidebar component renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::CARETAKER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.caretaker-sidebar title="Caretaker Dashboard" />',
        ['title' => 'Caretaker Dashboard']
    );

    // Test basic structure and section headers instead of permission-based navigation items
    $view->assertSee('Water Management');
    $view->assertSee('sidebar');
    $view->assertSee('navigation');
    $view->assertSee('Data Entry');
    $view->assertSee('Contact Management');
    $view->assertSee('Assigned Areas');
});

test('resident portal layout renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::RESIDENT]);
    $this->actingAs($user);

    $response = $this->get(route('resident.dashboard'));

    $response->assertSee('Resident Portal');
    $response->assertSee('Dashboard');
    $response->assertSee('Invoices');
    $response->assertSee('Readings');
    $response->assertSee('Messages');
    $response->assertSee('Contact Us');
});

test('admin sidebar includes user profile section', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN, 'name' => 'John Doe', 'email' => '<EMAIL>']);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('John Doe');
    $view->assertSee('<EMAIL>');
    $view->assertSee('JD');
    // First letters of name
});

test('manager sidebar includes user profile section', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER, 'name' => 'Jane Smith', 'email' => '<EMAIL>']);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.manager-sidebar title="Management Dashboard" />',
        ['title' => 'Management Dashboard']
    );

    $view->assertSee('Jane Smith');
    $view->assertSee('<EMAIL>');
    $view->assertSee('JS');
    // First letters of name
});

test('sidebar components have proper structure', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('sidebar');
    $view->assertSee('navigation');
    $view->assertSee('flex');
    $view->assertSee('h-screen');
    $view->assertSee('bg-white');
    $view->assertSee('border-r');
});

test('sidebar components have dark mode support', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('dark:bg-black');
    $view->assertSee('dark:border-gray-800');
    $view->assertSee('dark:text-white');
});

test('sidebar components have responsive design', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('lg:static');
    $view->assertSee('lg:translate-x-0');
    $view->assertSee('lg:w-64');
    $view->assertSee('lg:w-[90px]');
});

test('sidebar components display logo', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('Water Management');
    $view->assertSee('WM');
    $view->assertSee('application-logo');
});

test('sidebar components have collapsible logo', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-navigation.sidebar.admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('logo');
    $view->assertSee('logo-icon');
    $view->assertSee('hidden');
    $view->assertSee('lg:block');
});
