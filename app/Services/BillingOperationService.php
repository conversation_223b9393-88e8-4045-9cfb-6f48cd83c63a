<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class BillingOperationService
{
    public function __construct(
        private AccountBalanceService $accountBalanceService
    ) {}

    /**
     * Generate bulk invoices for multiple houses
     */
    public function generateBulkInvoices(
        array $houseIds,
        float $amount,
        string $description,
        User $user,
        ?\DateTime $dueDate = null
    ): array {
        if (empty($houseIds)) {
            return [];
        }

        return DB::transaction(function () use ($houseIds, $amount, $description, $user, $dueDate) {
            $invoices = [];
            $dueDate = $dueDate ?? now()->addDays(30);

            foreach ($houseIds as $houseId) {
                $house = House::find($houseId);
                if (! $house) {
                    continue;
                }

                // Create or get default water rate and meter reading
                $waterRate = WaterRate::firstOrCreate([
                    'estate_id' => $house->estate_id,
                    'name' => 'Default Bulk Rate',
                    'rate_per_unit' => 30.00,
                    'minimum_charge' => 100.00,
                    'minimum_units' => 0,
                    'fixed_charge' => 100.00,
                    'effective_from' => now()->subMonths(6),
                    'effective_to' => now()->addMonths(6),
                    'is_active' => true,
                ]);

                $meterReading = MeterReading::firstOrCreate([
                    'house_id' => $houseId,
                    'user_id' => $user->id,
                    'reading_date' => now(),
                    'current_reading' => 1000,
                    'previous_reading' => 950,
                    'consumption' => 50,
                    'status' => 'approved',
                ]);

                $invoice = Invoice::create([
                    'house_id' => $houseId,
                    'invoice_number' => $this->generateInvoiceNumber(),
                    'water_rate_id' => $waterRate->id,
                    'meter_reading_id' => $meterReading->id,
                    'billing_period_start' => now()->startOfMonth(),
                    'billing_period_end' => now()->endOfMonth(),
                    'previous_reading' => $meterReading->previous_reading,
                    'current_reading' => $meterReading->current_reading,
                    'consumption' => $meterReading->consumption,
                    'rate_per_unit' => $waterRate->rate_per_unit,
                    'amount' => $amount,
                    'fixed_charge' => $waterRate->fixed_charge,
                    'total_amount' => $amount + $waterRate->fixed_charge,
                    'status' => 'draft',
                    'due_date' => $dueDate,
                    'notes' => $description,
                ]);

                // Create account transaction
                $this->accountBalanceService->processInvoiceTransaction(
                    $houseId,
                    $invoice,
                    $amount,
                    $user
                );

                $invoices[] = $invoice;
            }

            Log::info('Bulk invoices generated', [
                'user_id' => $user->id,
                'house_count' => count($houseIds),
                'amount' => $amount,
                'description' => $description,
            ]);

            return $invoices;
        });
    }

    /**
     * Send bulk reminders for overdue invoices
     */
    public function sendBulkReminders(
        array $invoiceIds,
        string $message,
        User $user
    ): array {
        if (empty($invoiceIds)) {
            return [
                'success' => true,
                'sent_count' => 0,
                'failed_count' => 0,
                'message' => 'No invoices selected for reminders',
            ];
        }

        $invoices = Invoice::whereIn('id', $invoiceIds)
            ->whereIn('status', ['overdue', 'sent'])
            ->get();

        $sentCount = 0;
        $failedCount = 0;

        foreach ($invoices as $invoice) {
            try {
                // Send WhatsApp notification (placeholder for actual implementation)
                if ($this->sendReminderNotification($invoice, $message)) {
                    // Update invoice reminder tracking (if columns exist)
                    if (Schema::hasColumn('invoices', 'reminder_count')) {
                        $invoice->increment('reminder_count');
                    }
                    if (Schema::hasColumn('invoices', 'last_reminder_at')) {
                        $invoice->update(['last_reminder_at' => now()]);
                    }

                    $sentCount++;
                } else {
                    $failedCount++;
                }
            } catch (\Exception $e) {
                Log::error('Failed to send reminder', [
                    'invoice_id' => $invoice->id,
                    'error' => $e->getMessage(),
                ]);
                $failedCount++;
            }
        }

        Log::info('Bulk reminders sent', [
            'user_id' => $user->id,
            'sent_count' => $sentCount,
            'failed_count' => $failedCount,
        ]);

        return [
            'success' => true,
            'sent_count' => $sentCount,
            'failed_count' => $failedCount,
            'message' => "Reminders sent to {$sentCount} invoices",
        ];
    }

    /**
     * Bulk approve invoices (mark as sent)
     */
    public function bulkApproveInvoices(
        array $invoiceIds,
        User $user
    ): array {
        if (empty($invoiceIds)) {
            return [
                'success' => true,
                'approved_count' => 0,
                'skipped_count' => 0,
                'message' => 'No invoices selected for approval',
            ];
        }

        $invoices = Invoice::whereIn('id', $invoiceIds)->get();
        $approvedCount = 0;
        $skippedCount = 0;

        foreach ($invoices as $invoice) {
            if (in_array($invoice->status, ['draft', 'submitted'])) {
                $invoice->update([
                    'status' => 'approved',
                    'approved_at' => now(),
                    'approved_by' => $user->id,
                ]);
                $approvedCount++;
            } else {
                $skippedCount++;
            }
        }

        Log::info('Bulk invoices approved', [
            'user_id' => $user->id,
            'approved_count' => $approvedCount,
            'skipped_count' => $skippedCount,
        ]);

        return [
            'success' => true,
            'approved_count' => $approvedCount,
            'skipped_count' => $skippedCount,
            'message' => "Approved {$approvedCount} invoices, skipped {$skippedCount}",
        ];
    }

    /**
     * Bulk cancel invoices
     */
    public function bulkCancelInvoices(
        array $invoiceIds,
        User $user
    ): array {
        if (empty($invoiceIds)) {
            return [
                'success' => true,
                'cancelled_count' => 0,
                'skipped_count' => 0,
                'message' => 'No invoices selected for cancellation',
            ];
        }

        $invoices = Invoice::whereIn('id', $invoiceIds)->get();
        $cancelledCount = 0;
        $skippedCount = 0;

        foreach ($invoices as $invoice) {
            if (in_array($invoice->status, ['draft'])) {
                $invoice->update([
                    'status' => 'cancelled',
                ]);
                $cancelledCount++;
            } else {
                $skippedCount++;
            }
        }

        Log::info('Bulk invoices cancelled', [
            'user_id' => $user->id,
            'cancelled_count' => $cancelledCount,
            'skipped_count' => $skippedCount,
        ]);

        return [
            'success' => true,
            'cancelled_count' => $cancelledCount,
            'skipped_count' => $skippedCount,
            'message' => "Cancelled {$cancelledCount} invoices, skipped {$skippedCount}",
        ];
    }

    /**
     * Get billing summary for an estate
     */
    public function getBillingSummary(int $estateId): array
    {
        $houses = House::where('estate_id', $estateId)->get();
        $houseIds = $houses->pluck('id');

        $invoices = Invoice::whereIn('house_id', $houseIds)->get();

        return [
            'total_houses' => $houses->count(),
            'total_invoices' => $invoices->count(),
            'paid_invoices' => $invoices->where('status', 'paid')->count(),
            'overdue_invoices' => $invoices->where('status', 'overdue')->count(),
            'draft_invoices' => $invoices->where('status', 'draft')->count(),
            'submitted_invoices' => $invoices->where('status', 'submitted')->count(),
            'approved_invoices' => $invoices->where('status', 'approved')->count(),
            'sent_invoices' => $invoices->where('status', 'sent')->count(),
            'cancelled_invoices' => $invoices->where('status', 'cancelled')->count(),
            'total_amount' => $invoices->sum('total_amount'),
            'paid_amount' => $invoices->where('status', 'paid')->sum('total_amount'),
            'overdue_amount' => $invoices->where('status', 'overdue')->sum('total_amount'),
        ];
    }

    /**
     * Get aging analysis for overdue invoices
     */
    public function getAgingAnalysis(): array
    {
        $overdueInvoices = Invoice::where('status', 'overdue')->get();
        $now = now();

        $aging = [
            '0_30_days' => 0,
            '31_60_days' => 0,
            '61_90_days' => 0,
            '90_plus_days' => 0,
            'total_overdue' => 0,
        ];

        foreach ($overdueInvoices as $invoice) {
            if ($invoice->due_date > $now) {
                continue; // Skip invoices that aren't actually overdue
            }

            $daysOverdue = $invoice->due_date->diffInDays($now);
            $amount = $invoice->total_amount;

            if ($daysOverdue <= 30) {
                $aging['0_30_days'] += $amount;
            } elseif ($daysOverdue <= 60) {
                $aging['31_60_days'] += $amount;
            } elseif ($daysOverdue <= 90) {
                $aging['61_90_days'] += $amount;
            } else {
                $aging['90_plus_days'] += $amount;
            }

            $aging['total_overdue'] += $amount;
        }

        return $aging;
    }

    /**
     * Get invoices by status with filtering
     */
    public function getInvoicesByStatus(
        ?string $status = null,
        ?int $estateId = null,
        ?int $limit = null
    ): Collection {
        $query = Invoice::with(['house', 'house.estate']);

        if ($status) {
            $query->where('status', $status);
        }

        if ($estateId) {
            $query->whereHas('house', function ($q) use ($estateId) {
                $q->where('estate_id', $estateId);
            });
        }

        if ($limit) {
            $query->limit($limit);
        }

        return $query->latest()->get();
    }

    /**
     * Get bulk operation statistics
     */
    public function getBulkOperationStats(): array
    {
        return [
            'total_draft_invoices' => Invoice::where('status', 'draft')->count(),
            'total_sent_invoices' => Invoice::where('status', 'sent')->count(),
            'total_overdue_invoices' => Invoice::where('status', 'overdue')->count(),
            'total_unpaid_invoices' => Invoice::whereIn('status', ['sent', 'overdue'])->count(),
            'total_overdue_amount' => Invoice::where('status', 'overdue')->sum('total_amount'),
        ];
    }

    /**
     * Generate unique invoice number
     */
    private function generateInvoiceNumber(): string
    {
        $prefix = 'INV';
        $date = now()->format('Ymd');
        $sequence = Invoice::whereDate('created_at', today())->count() + 1;

        return sprintf('%s-%s-%04d', $prefix, $date, $sequence);
    }

    /**
     * Send reminder notification (placeholder for actual implementation)
     */
    private function sendReminderNotification(Invoice $invoice, string $message): bool
    {
        // This would integrate with WhatsApp service or other notification systems
        Log::info('Reminder notification sent', [
            'invoice_id' => $invoice->id,
            'house_id' => $invoice->house_id,
            'message' => $message,
        ]);

        // For testing purposes, always return success
        return true;
    }
}
