<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PDF;

class AgingReportPdfService
{
    public function generateAgingReportPdf(array $agingData): string
    {
        try {
            // Generate PDF
            $pdf = PDF::loadView('pdf.aging-report', [
                'agingData' => $agingData,
                'company' => $this->getCompanyDetails(),
                'generatedAt' => now(),
            ]);

            // Set paper size and orientation
            $pdf->setPaper('A4', 'portrait');

            // Generate filename
            $filename = 'aging-report-'.now()->format('Y-m-d-His').'.pdf';
            $path = "reports/aging/{$filename}";

            // Save to storage
            Storage::put($path, $pdf->output());

            Log::info('Aging report PDF generated successfully', [
                'path' => $path,
                'total_outstanding' => $agingData['total_outstanding'] ?? 0,
            ]);

            return $path;

        } catch (\Exception $e) {
            Log::error('Failed to generate aging report PDF', [
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Failed to generate aging report PDF: '.$e->getMessage());
        }
    }

    private function getCompanyDetails(): array
    {
        return [
            'name' => config('app.name', 'Water Management System'),
            'address' => config('app.company_address', '123 Main Street, City, Country'),
            'phone' => config('app.company_phone', '+*********** 789'),
            'email' => config('app.company_email', '<EMAIL>'),
            'logo' => config('app.company_logo', null),
        ];
    }
}
