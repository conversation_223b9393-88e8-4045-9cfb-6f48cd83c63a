<?php

namespace App\Services;

use App\Enums\UserRole;
use App\Models\Estate;
use App\Models\User;
use Illuminate\Support\Facades\Cache;

class PermissionValidationService
{
    /**
     * Validate if user has a specific permission with optional estate scoping
     */
    public function validateUserPermission(User $user, string $permission, ?Estate $estate = null): bool
    {
        // 1. Check direct permission first (<PERSON><PERSON> handles user overrides automatically)
        if (! $user->hasPermissionTo($permission)) {
            return false;
        }

        // 2. Validate estate assignment if required
        if ($this->permissionRequiresEstate($permission)) {
            return $this->validateEstateAccess($user, $estate);
        }

        return true;
    }

    /**
     * Get user's effective permissions (including overrides)
     */
    public function getUserPermissions(User $user): array
    {
        $cacheKey = "user_permissions_{$user->id}";

        return Cache::remember($cacheKey, now()->addHours(1), function () use ($user) {
            // <PERSON><PERSON> automatically handles role permissions and user overrides
            return $user->getAllPermissions()->pluck('name')->toArray();
        });
    }

    /**
     * Check if user can access a specific estate
     */
    public function canAccessEstate(User $user, Estate $estate): bool
    {
        if ($user->hasRole('admin')) {
            return true;
        }

        return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    /**
     * Check if user can access a specific house
     */
    public function canAccessHouse(User $user, $house): bool
    {
        if ($user->hasRole('admin')) {
            return true;
        }

        if ($user->hasRole('resident')) {
            return $user->contacts()->where('house_id', $house->id)->exists();
        }

        return $user->assignedEstates()->where('estates.id', $house->estate_id)->exists();
    }

    /**
     * Get user's accessible estates
     */
    public function getAccessibleEstates(User $user)
    {
        if ($user->hasRole('admin')) {
            return \App\Models\Estate::pluck('name', 'id');
        }

        return $user->assignedEstates()->with('houses')->pluck('name', 'id');
    }

    /**
     * Invalidate user's permission cache
     */
    public function invalidateUserCache(User $user): void
    {
        Cache::forget("user_permissions_{$user->id}");
        // Spatie has its own cache clearing mechanism
        $user->forgetCachedPermissions();
    }

    /**
     * Invalidate role cache for all users with a specific role
     */
    public function invalidateRoleCache(UserRole $role): void
    {
        $users = User::where('role', $role->value)->get();
        foreach ($users as $user) {
            $this->invalidateUserCache($user);
        }
    }

    /**
     * Check if permission requires estate assignment
     * This is a simplified version - in a real implementation, 
     * you might want to store this metadata with permissions
     */
    private function permissionRequiresEstate(string $permission): bool
    {
        // Permissions that typically require estate assignment
        $estateBasedPermissions = [
            'estates.view_assigned',
            'estates.manage_assigned',
            'estates.edit_assigned',
            'houses.view_assigned',
            'houses.manage_assigned',
            'houses.edit_assigned',
            'contacts.view_assigned',
            'contacts.manage_assigned',
            'contacts.edit_assigned',
            'contacts.create_assigned',
            'readings.view_assigned',
            'readings.review_assigned',
            'readings.approve_assigned',
            'readings.edit_assigned',
            'readings.create_assigned',
            'invoices.view_assigned',
            'invoices.adjust_assigned',
            'invoices.export_assigned',
            'invoices.approve_assigned',
            'invoices.send_assigned',
            'invoices.generate_assigned',
            'accounts.view_assigned',
            'accounts.view_balance_assigned',
            'accounts.view_transactions_assigned',
            'accounts.view_statement_assigned',
            'accounts.export_statement_assigned',
            'accounts.manage_assigned',
            'accounts.create_transaction_assigned',
            'accounts.edit_transaction_assigned',
            'accounts.adjust_balance_assigned',
            'rates.view_assigned',
            'rates.edit_assigned',
            'reports.view_assigned',
            'analytics.view_assigned',
            'export.data_assigned',
            'reports.generate_assigned',
            'payments.view_assigned',
            'payments.approve_assigned',
            'payments.export_assigned',
            'payments.create_assigned',
            'payments.edit_assigned',
            'payments.reconcile_assigned',
            'users.view_assigned',
            'users.create_assigned',
            'users.edit_assigned',
            'whatsapp.send_assigned',
            'whatsapp.send_invoices_assigned',
            'resident.inquiries.view_assigned',
        ];

        return in_array($permission, $estateBasedPermissions);
    }

    /**
     * Validate estate access for user
     */
    private function validateEstateAccess(User $user, ?Estate $estate): bool
    {
        if (! $estate) {
            return false;
        }

        // Admin users have access to all estates
        if ($user->hasRole('admin')) {
            return true;
        }

        return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    /**
     * Check if user can manage another user
     */
    public function canManageUser(User $manager, User $subordinate): bool
    {
        // Admin can manage everyone
        if ($manager->hasRole('admin')) {
            return true;
        }

        // Manager can manage reviewers and caretakers
        if ($manager->hasRole('manager')) {
            return $subordinate->hasRole('reviewer') ||
                   $subordinate->hasRole('caretaker');
        }

        // Reviewer can manage caretakers
        if ($manager->hasRole('reviewer')) {
            return $subordinate->hasRole('caretaker');
        }

        return false;
    }

    /**
     * Check if user has any of the given permissions
     */
    public function hasAnyPermission(User $user, array $permissions): bool
    {
        return $user->hasAnyPermission($permissions);
    }

    /**
     * Check if user has all of the given permissions
     */
    public function hasAllPermissions(User $user, array $permissions): bool
    {
        return $user->hasAllPermissions($permissions);
    }

    /**
     * Get user's roles
     */
    public function getUserRoles(User $user): array
    {
        return $user->getRoleNames()->toArray();
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(User $user, string $role): bool
    {
        return $user->hasRole($role);
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(User $user, array $roles): bool
    {
        return $user->hasAnyRole($roles);
    }

    /**
     * Check if user has all of the given roles
     */
    public function hasAllRoles(User $user, array $roles): bool
    {
        return $user->hasAllRoles($roles);
    }
}
