<?php

namespace App\Services;

use App\Models\Estate;
use App\Models\EstateValidationRule;
use App\Models\ValidationBaseline;
use App\Services\Validation\RuleEngine;
use Illuminate\Support\Collection;

class ValidationConfigurationService
{
    private array $defaultConfig = [
        'basic_validation' => [
            'max_reasonable_consumption' => 1000,
            'min_reasonable_consumption' => 1,
            'allow_future_dates' => false,
        ],
        'consumption_validation' => [
            'anomaly_threshold_high' => 2.5,
            'anomaly_threshold_medium' => 1.8,
            'min_history_readings' => 3,
            'max_history_readings' => 12,
        ],
        'statistical_anomaly' => [
            'min_history_readings' => 3,
            'iqr_multiplier' => 1.5,
            'z_score_threshold' => 2.0,
            'z_score_critical_threshold' => 3.0,
        ],
        'pattern_anomaly' => [
            'min_history_readings' => 6,
            'max_history_readings' => 24,
            'seasonal_deviation_threshold' => 1.5,
            'trend_deviation_threshold' => 0.5,
            'consistency_threshold' => 0.3,
        ],
        'contextual_validation' => [
            'min_reading_interval' => 20,
            'max_reading_interval' => 45,
            'expected_reading_interval' => [25, 35],
            'neighborhood_comparison_threshold' => 1.5,
            'min_neighborhood_samples' => 3,
        ],
    ];

    public function getEstateConfiguration(int $estateId): array
    {
        $rules = EstateValidationRule::where('estate_id', $estateId)
            ->active()
            ->get()
            ->keyBy('rule_name');

        $config = $this->defaultConfig;

        foreach ($rules as $rule) {
            if (isset($config[$rule->rule_name])) {
                $config[$rule->rule_name] = array_merge(
                    $config[$rule->rule_name],
                    $rule->config ?? []
                );
            }
        }

        return $config;
    }

    public function updateEstateRule(int $estateId, string $ruleName, array $config): EstateValidationRule
    {
        $rule = EstateValidationRule::updateOrCreate(
            [
                'estate_id' => $estateId,
                'rule_name' => $ruleName,
            ],
            [
                'rule_type' => $this->getRuleType($ruleName),
                'config' => $config,
                'is_active' => true,
                'updated_by' => auth()->id(),
            ]
        );

        if (! $rule->wasRecentlyCreated && ! $rule->created_by) {
            $rule->created_by = auth()->id();
            $rule->save();
        }

        return $rule;
    }

    public function deactivateEstateRule(int $estateId, string $ruleName): bool
    {
        $rule = EstateValidationRule::where('estate_id', $estateId)
            ->where('rule_name', $ruleName)
            ->first();

        if ($rule) {
            $rule->is_active = false;
            $rule->updated_by = auth()->id();

            return $rule->save();
        }

        return false;
    }

    public function getActiveRulesForEstate(int $estateId): Collection
    {
        return EstateValidationRule::where('estate_id', $estateId)
            ->active()
            ->get();
    }

    public function createRuleEngineForEstate(int $estateId): RuleEngine
    {
        $config = $this->getEstateConfiguration($estateId);
        $ruleEngine = new RuleEngine;

        // Add rules with estate-specific configuration
        $ruleEngine->addRule(new \App\Services\Validation\BasicValidationRule($config['basic_validation']));
        $ruleEngine->addRule(new \App\Services\Validation\ConsumptionValidationRule($config['consumption_validation']));
        $ruleEngine->addRule(new \App\Services\Validation\StatisticalAnomalyRule($config['statistical_anomaly']));
        $ruleEngine->addRule(new \App\Services\Validation\PatternAnomalyRule($config['pattern_anomaly']));
        $ruleEngine->addRule(new \App\Services\Validation\ContextualRule($config['contextual_validation']));

        return $ruleEngine;
    }

    public function calculateBaselines(int $estateId, ?int $houseId = null): void
    {
        $query = \App\Models\MeterReading::whereHas('house', function ($query) use ($estateId) {
            $query->where('estate_id', $estateId);
        })->where('status', 'approved');

        if ($houseId) {
            $query->where('house_id', $houseId);
        }

        $readings = $query->orderBy('reading_date')->get();

        if ($readings->isEmpty()) {
            return;
        }

        $houses = $houseId ? [\App\Models\House::find($houseId)] : \App\Models\House::where('estate_id', $estateId)->get();

        foreach ($houses as $house) {
            $houseReadings = $readings->where('house_id', $house->id);

            if ($houseReadings->count() < 3) {
                continue;
            }

            $consumptions = $houseReadings->pluck('consumption')->toArray();
            $monthlyAverages = $this->calculateMonthlyAverages($houseReadings);

            ValidationBaseline::create([
                'estate_id' => $estateId,
                'house_id' => $house->id,
                'baseline_type' => 'consumption',
                'average_consumption' => array_sum($consumptions) / count($consumptions),
                'median_consumption' => $this->calculateMedian($consumptions),
                'std_deviation' => $this->calculateStandardDeviation($consumptions),
                'q1_consumption' => $this->calculatePercentile($consumptions, 25),
                'q3_consumption' => $this->calculatePercentile($consumptions, 75),
                'sample_size' => count($consumptions),
                'baseline_date' => now(),
                'monthly_averages' => $monthlyAverages,
            ]);
        }
    }

    public function getLatestBaseline(int $houseId): ?ValidationBaseline
    {
        return ValidationBaseline::where('house_id', $houseId)
            ->latest()
            ->first();
    }

    public function getEstateBaselines(int $estateId): Collection
    {
        return ValidationBaseline::where('estate_id', $estateId)
            ->latest()
            ->get();
    }

    private function getRuleType(string $ruleName): string
    {
        $ruleTypes = [
            'basic_validation' => 'basic',
            'consumption_validation' => 'consumption',
            'statistical_anomaly' => 'statistical',
            'pattern_anomaly' => 'pattern',
            'contextual_validation' => 'contextual',
        ];

        return $ruleTypes[$ruleName] ?? 'custom';
    }

    private function calculateMonthlyAverages(Collection $readings): array
    {
        $monthlyData = [];

        for ($month = 1; $month <= 12; $month++) {
            $monthReadings = $readings->filter(function ($reading) use ($month) {
                return $reading->reading_date->month === $month;
            });

            $monthlyData[$month] = $monthReadings->isNotEmpty()
                ? round($monthReadings->avg('consumption'), 2)
                : null;
        }

        return $monthlyData;
    }

    private function calculateMedian(array $values): float
    {
        sort($values);
        $count = count($values);

        if ($count === 0) {
            return 0;
        }
        if ($count % 2 === 0) {
            return ($values[$count / 2 - 1] + $values[$count / 2]) / 2;
        }

        return $values[floor($count / 2)];
    }

    private function calculatePercentile(array $values, float $percentile): float
    {
        if (empty($values)) {
            return 0;
        }

        sort($values);
        $index = ($percentile / 100) * (count($values) - 1);

        if (floor($index) == $index) {
            return $values[$index];
        }

        $lower = $values[floor($index)];
        $upper = $values[ceil($index)];
        $weight = $index - floor($index);

        return $lower + ($weight * ($upper - $lower));
    }

    private function calculateStandardDeviation(array $values): float
    {
        if (count($values) < 2) {
            return 0;
        }

        $mean = array_sum($values) / count($values);
        $squaredDiffs = array_map(function ($value) use ($mean) {
            return pow($value - $mean, 2);
        }, $values);

        return sqrt(array_sum($squaredDiffs) / count($values));
    }
}
