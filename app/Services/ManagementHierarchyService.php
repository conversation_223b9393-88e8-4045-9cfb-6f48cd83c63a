<?php

namespace App\Services;

use App\Enums\UserRole;
use App\Models\PermissionAuditLog;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class ManagementHierarchyService
{
    /**
     * Assign subordinates to a manager
     */
    public function assignSubordinates(User $manager, array $subordinateIds, string $relationship): void
    {
        DB::transaction(function () use ($manager, $subordinateIds, $relationship) {
            // Get current relationships for comparison
            $currentSubordinates = $manager->subordinates()->pluck('subordinate_id')->toArray();

            // Remove existing relationships
            $manager->subordinates()->detach();

            // Add new relationships
            $relationships = collect($subordinateIds)->map(function ($subordinateId) use ($relationship) {
                return [
                    'subordinate_id' => $subordinateId,
                    'relationship' => $relationship,
                ];
            });

            if ($relationships->isNotEmpty()) {
                $manager->subordinates()->attach($relationships);
            }

            // Ensure estate assignments align with hierarchy
            $this->syncEstateAssignments($manager, $subordinateIds);

            // Log the hierarchy changes
            $this->logHierarchyChanges($manager, $currentSubordinates, $subordinateIds, $relationship);
        });
    }

    /**
     * Get managed users for a manager
     */
    public function getManagedUsers(User $manager)
    {
        return $manager->subordinates()->with('assignedEstates')->get();
    }

    /**
     * Validate management relationship
     */
    public function validateManagementRelationship(User $manager, User $subordinate): bool
    {
        return $manager->subordinates()->where('subordinate_id', $subordinate->id)->exists();
    }

    /**
     * Get managers for a user
     */
    public function getUserManagers(User $user)
    {
        return $user->managers()->get();
    }

    /**
     * Check if user can manage another user
     */
    public function canManageUser(User $manager, User $subordinate): bool
    {
        // Admin can manage everyone
        if ($manager->hasRole(UserRole::ADMIN)) {
            return true;
        }

        // Manager can manage reviewers and caretakers
        if ($manager->hasRole(UserRole::MANAGER)) {
            return $subordinate->hasRole(UserRole::REVIEWER) ||
                   $subordinate->hasRole(UserRole::CARETAKER);
        }

        // Reviewer can manage caretakers
        if ($manager->hasRole(UserRole::REVIEWER)) {
            return $subordinate->hasRole(UserRole::CARETAKER);
        }

        return false;
    }

    /**
     * Get hierarchy level for user
     */
    public function getHierarchyLevel(User $user): int
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return 1;
        }

        if ($user->hasRole(UserRole::MANAGER)) {
            return 2;
        }

        if ($user->hasRole(UserRole::REVIEWER)) {
            return 3;
        }

        if ($user->hasRole(UserRole::CARETAKER)) {
            return 4;
        }

        return 5; // Resident
    }

    /**
     * Get all users in management chain
     */
    public function getManagementChain(User $user): array
    {
        $chain = [];
        $currentManagers = $this->getUserManagers($user);

        foreach ($currentManagers as $manager) {
            $chain[] = $manager;
            $chain = array_merge($chain, $this->getManagementChain($manager)->toArray());
        }

        return array_unique($chain);
    }

    /**
     * Get all subordinates in hierarchy
     */
    public function getAllSubordinates(User $manager): array
    {
        $subordinates = [];
        $directSubordinates = $this->getManagedUsers($manager);

        foreach ($directSubordinates as $subordinate) {
            $subordinates[] = $subordinate;
            $subordinates = array_merge($subordinates, $this->getAllSubordinates($subordinate)->toArray());
        }

        return array_unique($subordinates);
    }

    /**
     * Remove management relationship
     */
    public function removeManagementRelationship(User $manager, User $subordinate): void
    {
        DB::transaction(function () use ($manager, $subordinate) {
            $relationship = $manager->subordinates()->where('subordinate_id', $subordinate->id)->first();

            if ($relationship) {
                $manager->subordinates()->detach($subordinate->id);

                // Log the removal
                PermissionAuditLog::create([
                    'user_id' => $manager->id,
                    'action' => 'management_relationship_removed',
                    'details' => json_encode([
                        'manager_id' => $manager->id,
                        'manager_name' => $manager->name,
                        'subordinate_id' => $subordinate->id,
                        'subordinate_name' => $subordinate->name,
                        'relationship' => $relationship->pivot->relationship,
                    ]),
                    'target_type' => 'management_hierarchy',
                    'target_id' => "{$manager->id}_{$subordinate->id}",
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                ]);
            }
        });
    }

    /**
     * Sync estate assignments based on hierarchy
     */
    private function syncEstateAssignments(User $manager, array $subordinateIds): void
    {
        $managerEstates = $manager->assignedEstates()->pluck('estates.id')->toArray();

        foreach ($subordinateIds as $subordinateId) {
            $subordinate = User::findOrFail($subordinateId);

            // Assign manager's estates to subordinate with required pivot fields
            $pivotData = [];
            foreach ($managerEstates as $estateId) {
                $pivotData[$estateId] = [
                    'assigned_by' => $manager->id,
                    'assigned_at' => now(),
                ];
            }

            if (!empty($pivotData)) {
                $subordinate->assignedEstates()->syncWithoutDetaching($pivotData);
            }
        }
    }

    /**
     * Log hierarchy changes
     */
    private function logHierarchyChanges(User $manager, array $oldSubordinates, array $newSubordinates, string $relationship): void
    {
        $addedSubordinates = array_diff($newSubordinates, $oldSubordinates);
        $removedSubordinates = array_diff($oldSubordinates, $newSubordinates);

        if (! empty($addedSubordinates) || ! empty($removedSubordinates)) {
            PermissionAuditLog::create([
                'user_id' => $manager->id,
                'action' => 'management_hierarchy_updated',
                'details' => json_encode([
                    'manager_id' => $manager->id,
                    'manager_name' => $manager->name,
                    'relationship' => $relationship,
                    'added_subordinates' => $addedSubordinates,
                    'removed_subordinates' => $removedSubordinates,
                    'total_subordinates' => count($newSubordinates),
                ]),
                'target_type' => 'management_hierarchy',
                'target_id' => (string) $manager->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        }
    }
}
