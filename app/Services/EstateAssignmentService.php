<?php

namespace App\Services;

use App\Models\Estate;
use App\Models\PermissionAuditLog;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class EstateAssignmentService
{
    /**
     * Assign user to estates
     */
    public function assignUserToEstates(User $user, array $estateIds, User $assignedBy): void
    {
        DB::transaction(function () use ($user, $estateIds, $assignedBy) {
            // Get current assignments for comparison
            $currentAssignments = $user->assignedEstates()->pluck('estates.id')->toArray();

            // Remove existing assignments
            $user->assignedEstates()->detach();

            // Add new assignments
            $pivotData = [];
            foreach ($estateIds as $estateId) {
                $pivotData[$estateId] = [
                    'assigned_by' => $assignedBy->id,
                    'assigned_at' => now(),
                ];
            }

            if (!empty($pivotData)) {
                $user->assignedEstates()->attach($pivotData);
            }

            // Log the assignment changes
            $this->logAssignmentChanges($user, $currentAssignments, $estateIds, $assignedBy);
        });
    }

    /**
     * Get accessible estates for user
     */
    public function getAccessibleEstates(User $user)
    {
        if ($user->hasRole(\App\Enums\UserRole::ADMIN)) {
            return Estate::pluck('name', 'id');
        }

        return $user->assignedEstates()->with('houses')->pluck('name', 'id');
    }

    /**
     * Validate estate access for user
     */
    public function validateEstateAccess(User $user, Estate $estate): bool
    {
        if ($user->hasRole(\App\Enums\UserRole::ADMIN)) {
            return true;
        }

        return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    /**
     * Bulk assign users to estates
     */
    public function bulkAssignUsersToEstates(array $userIds, array $estateIds, User $assignedBy): void
    {
        DB::transaction(function () use ($userIds, $estateIds, $assignedBy) {
            foreach ($userIds as $userId) {
                $user = User::findOrFail($userId);
                $this->assignUserToEstates($user, $estateIds, $assignedBy);
            }
        });
    }

    /**
     * Remove user from estate
     */
    public function removeUserFromEstate(User $user, Estate $estate, User $removedBy): void
    {
        DB::transaction(function () use ($user, $estate, $removedBy) {
            $user->assignedEstates()->detach($estate->id);

            // Log the removal
            PermissionAuditLog::create([
                'user_id' => $removedBy->id,
                'action' => 'estate_assignment_removed',
                'details' => json_encode([
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'estate_id' => $estate->id,
                    'estate_name' => $estate->name,
                ]),
                'target_type' => 'estate_assignment',
                'target_id' => "{$user->id}_{$estate->id}",
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        });
    }

    /**
     * Get users assigned to estate
     */
    public function getUsersAssignedToEstate(Estate $estate)
    {
        return User::whereHas('assignedEstates', function ($query) use ($estate) {
            $query->where('estates.id', $estate->id);
        })->get();
    }

    /**
     * Check if user is assigned to any estate
     */
    public function isUserAssignedToAnyEstate(User $user): bool
    {
        return $user->assignedEstates()->exists();
    }

    /**
     * Get assignment history for user
     */
    public function getUserAssignmentHistory(User $user)
    {
        return PermissionAuditLog::where('target_type', 'estate_assignment')
            ->where('target_id', 'like', "{$user->id}_%")
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Log assignment changes
     */
    private function logAssignmentChanges(User $user, array $oldAssignments, array $newAssignments, User $assignedBy): void
    {
        $addedEstates = array_diff($newAssignments, $oldAssignments);
        $removedEstates = array_diff($oldAssignments, $newAssignments);

        if (! empty($addedEstates) || ! empty($removedEstates)) {
            PermissionAuditLog::create([
                'user_id' => $assignedBy->id,
                'action' => 'estate_assignment_updated',
                'details' => json_encode([
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'added_estates' => $addedEstates,
                    'removed_estates' => $removedEstates,
                    'total_estates' => count($newAssignments),
                ]),
                'target_type' => 'estate_assignment',
                'target_id' => (string) $user->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        }
    }
}
