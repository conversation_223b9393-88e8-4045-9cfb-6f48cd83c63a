<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\HouseContact;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ImportExportService
{
    public function exportEstates(array $filters = []): array
    {
        $searchService = new HouseSearchService;
        $query = $searchService->searchEstates($filters);

        $estates = $query->get();

        $data = $estates->map(function (Estate $estate) {
            return [
                'ID' => $estate->id,
                'Name' => $estate->name,
                'Code' => $estate->code,
                'Location' => $estate->location,
                'Manager Name' => $estate->manager_name,
                'Manager Phone' => $estate->manager_phone,
                'Manager Email' => $estate->manager_email,
                'Status' => $estate->status,
                'Total Houses' => $estate->houses_count,
                'Created At' => $estate->created_at->format('Y-m-d H:i:s'),
                'Updated At' => $estate->updated_at->format('Y-m-d H:i:s'),
            ];
        });

        return [
            'headers' => array_keys($data->first() ?? []),
            'data' => $data,
            'filename' => 'estates_'.now()->format('Y-m-d_H-i-s').'.csv',
        ];
    }

    public function exportHouses(array $filters = []): array
    {
        $searchService = new HouseSearchService;
        $query = $searchService->searchHouses($filters);

        $houses = $query->get();

        $data = $houses->map(function (House $house) {
            return [
                'ID' => $house->id,
                'Estate' => $house->estate->name,
                'House Number' => $house->house_number,
                'Meter Number' => $house->meter_number,
                'House Type' => $house->house_type,
                'Bedrooms' => $house->bedrooms,
                'Monthly Rent' => $house->monthly_rent,
                'Occupancy Status' => $house->occupancy_status,
                'Occupancy Date' => $house->occupancy_date?->format('Y-m-d'),
                'Vacancy Date' => $house->vacancy_date?->format('Y-m-d'),
                'Primary Contact' => $house->contacts()->wherePivot('is_primary', true)->first()?->full_name,
                'Contact Phone' => $house->contacts()->wherePivot('is_primary', true)->first()?->phone,
                'Contact Email' => $house->contacts()->wherePivot('is_primary', true)->first()?->email,
                'Total Contacts' => $house->contacts_count,
                'Created At' => $house->created_at->format('Y-m-d H:i:s'),
                'Updated At' => $house->updated_at->format('Y-m-d H:i:s'),
            ];
        });

        return [
            'headers' => array_keys($data->first() ?? []),
            'data' => $data,
            'filename' => 'houses_'.now()->format('Y-m-d_H-i-s').'.csv',
        ];
    }

    public function exportContacts(array $filters = []): array
    {
        $searchService = new HouseSearchService;
        $query = $searchService->searchContacts($filters);

        $contacts = $query->get();

        $data = $contacts->map(function (Contact $contact) {
            $primaryHouse = $contact->houses()->wherePivot('is_primary', true)->first();

            return [
                'ID' => $contact->id,
                'First Name' => $contact->first_name,
                'Last Name' => $contact->last_name,
                'Email' => $contact->email,
                'Phone' => $contact->phone,
                'ID Number' => $contact->id_number,
                'Contact Type' => $contact->contact_type,
                'Company Name' => $contact->company_name,
                'Address' => $contact->address,
                'Primary House' => $primaryHouse ? $primaryHouse->full_address : '',
                'Primary Estate' => $primaryHouse ? $primaryHouse->estate->name : '',
                'Total Houses' => $contact->houses_count,
                'Is Active' => $contact->is_active ? 'Yes' : 'No',
                'Created At' => $contact->created_at->format('Y-m-d H:i:s'),
                'Updated At' => $contact->updated_at->format('Y-m-d H:i:s'),
            ];
        });

        return [
            'headers' => array_keys($data->first() ?? []),
            'data' => $data,
            'filename' => 'contacts_'.now()->format('Y-m-d_H-i-s').'.csv',
        ];
    }

    public function importEstates(array $data): array
    {
        $results = [
            'imported' => 0,
            'updated' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        DB::beginTransaction();

        try {
            foreach ($data as $index => $row) {
                try {
                    $validator = Validator::make($row, [
                        'name' => 'required|string|max:255',
                        'code' => 'required|string|max:50|unique:estates,code',
                        'location' => 'nullable|string|max:255',
                        'manager_name' => 'nullable|string|max:255',
                        'manager_phone' => 'nullable|string|max:20',
                        'manager_email' => 'nullable|email|max:255',
                        'status' => 'nullable|in:active,inactive',
                    ]);

                    if ($validator->fails()) {
                        $results['failed']++;
                        $results['errors'][] = 'Row '.($index + 1).': '.implode(', ', $validator->errors()->all());

                        continue;
                    }

                    $estate = Estate::updateOrCreate(
                        ['code' => $row['code']],
                        $validator->validated()
                    );

                    if ($estate->wasRecentlyCreated) {
                        $results['imported']++;
                    } else {
                        $results['updated']++;
                    }

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = 'Row '.($index + 1).': '.$e->getMessage();
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

        return $results;
    }

    public function importHouses(array $data): array
    {
        $results = [
            'imported' => 0,
            'updated' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        DB::beginTransaction();

        try {
            foreach ($data as $index => $row) {
                try {
                    $validator = Validator::make($row, [
                        'estate_code' => 'required|string|exists:estates,code',
                        'house_number' => 'required|string|max:50',
                        'meter_number' => 'nullable|string|max:50|unique:houses,meter_number',
                        'house_type' => 'nullable|in:apartment,bungalow,maisonette,townhouse,studio',
                        'bedrooms' => 'nullable|integer|min:0',
                        'monthly_rent' => 'nullable|numeric|min:0',
                        'occupancy_status' => 'nullable|in:vacant,occupied,maintenance',
                    ]);

                    if ($validator->fails()) {
                        $results['failed']++;
                        $results['errors'][] = 'Row '.($index + 1).': '.implode(', ', $validator->errors()->all());

                        continue;
                    }

                    $estate = Estate::where('code', $row['estate_code'])->first();

                    $house = House::updateOrCreate(
                        [
                            'estate_id' => $estate->id,
                            'house_number' => $row['house_number'],
                        ],
                        array_merge($validator->validated(), ['estate_id' => $estate->id])
                    );

                    if ($house->wasRecentlyCreated) {
                        $results['imported']++;
                    } else {
                        $results['updated']++;
                    }

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = 'Row '.($index + 1).': '.$e->getMessage();
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

        return $results;
    }

    public function importContacts(array $data): array
    {
        $results = [
            'imported' => 0,
            'updated' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        DB::beginTransaction();

        try {
            foreach ($data as $index => $row) {
                try {
                    $validator = Validator::make($row, [
                        'first_name' => 'required|string|max:255',
                        'last_name' => 'required|string|max:255',
                        'email' => 'nullable|email|max:255|unique:contacts,email',
                        'phone' => 'nullable|string|max:20',
                        'id_number' => 'nullable|string|max:50|unique:contacts,id_number',
                        'contact_type' => 'nullable|in:tenant,owner,agent,emergency',
                        'company_name' => 'nullable|string|max:255',
                        'address' => 'nullable|string|max:500',
                    ]);

                    if ($validator->fails()) {
                        $results['failed']++;
                        $results['errors'][] = 'Row '.($index + 1).': '.implode(', ', $validator->errors()->all());

                        continue;
                    }

                    $contact = Contact::updateOrCreate(
                        ['email' => $row['email'] ?? null],
                        $validator->validated()
                    );

                    if ($contact->wasRecentlyCreated) {
                        $results['imported']++;
                    } else {
                        $results['updated']++;
                    }

                    // Handle house assignments if provided
                    if (! empty($row['house_numbers'])) {
                        $this->assignContactToHouses($contact, $row['house_numbers'], $row['is_primary'] ?? false);
                    }

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = 'Row '.($index + 1).': '.$e->getMessage();
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

        return $results;
    }

    public function assignContactToHouses(Contact $contact, string $houseNumbers, bool $isPrimary = false): void
    {
        $houseNumbers = array_map('trim', explode(',', $houseNumbers));

        foreach ($houseNumbers as $houseNumber) {
            $house = House::where('house_number', $houseNumber)->first();

            if ($house) {
                HouseContact::updateOrCreate(
                    [
                        'house_id' => $house->id,
                        'contact_id' => $contact->id,
                    ],
                    [
                        'is_primary' => $isPrimary,
                        'relationship' => 'tenant',
                        'start_date' => now(),
                    ]
                );
            }
        }
    }

    public function getImportTemplates(): array
    {
        return [
            'estates' => [
                'headers' => ['name', 'code', 'location', 'manager_name', 'manager_phone', 'manager_email', 'status'],
                'sample' => [
                    ['Sunset Estate', 'SE001', '123 Main St, City', 'John Doe', '+1234567890', '<EMAIL>', 'active'],
                    ['Green Valley', 'GV002', '456 Oak Ave, Town', 'Jane Smith', '+0987654321', '<EMAIL>', 'active'],
                ],
            ],
            'houses' => [
                'headers' => ['estate_code', 'house_number', 'meter_number', 'house_type', 'bedrooms', 'monthly_rent', 'occupancy_status'],
                'sample' => [
                    ['SE001', 'A101', 'MTR001', 'apartment', 2, 15000, 'vacant'],
                    ['SE001', 'A102', 'MTR002', 'apartment', 3, 20000, 'occupied'],
                ],
            ],
            'contacts' => [
                'headers' => ['first_name', 'last_name', 'email', 'phone', 'id_number', 'contact_type', 'company_name', 'address', 'house_numbers', 'is_primary'],
                'sample' => [
                    ['John', 'Doe', '<EMAIL>', '+1234567890', 'ID123456', 'tenant', '', '123 Main St', 'A101,A102', 'yes'],
                    ['Jane', 'Smith', '<EMAIL>', '+0987654321', 'ID654321', 'owner', 'ABC Corp', '456 Oak Ave', 'A103', 'no'],
                ],
            ],
        ];
    }
}
