<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\AccountTransaction;
use App\Models\HouseAccount;
use App\Models\Invoice;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AccountBalanceService
{
    public function __construct()
    {
        // Constructor for dependency injection if needed
    }

    /**
     * Create a new house account for a house
     */
    public function createHouseAccount(int $houseId, float $initialBalance = 0): HouseAccount
    {
        return HouseAccount::create([
            'house_id' => $houseId,
            'current_balance' => $initialBalance,
            'total_credit' => $initialBalance > 0 ? $initialBalance : 0,
            'total_debit' => $initialBalance < 0 ? abs($initialBalance) : 0,
            'last_transaction_date' => null,
        ]);
    }

    /**
     * Get or create house account for a house
     */
    public function getOrCreateHouseAccount(int $houseId): HouseAccount
    {
        $account = HouseAccount::where('house_id', $houseId)->first();

        if (! $account) {
            $account = $this->createHouseAccount($houseId);
        }

        return $account;
    }

    /**
     * Process an invoice transaction (debit)
     */
    public function processInvoiceTransaction(int $houseId, Invoice $invoice, float $amount, ?User $user = null): AccountTransaction
    {
        return DB::transaction(function () use ($houseId, $invoice, $amount, $user) {
            $account = $this->getOrCreateHouseAccount($houseId);

            $balanceBefore = $account->current_balance;
            $balanceAfter = $balanceBefore - $amount;

            $transaction = AccountTransaction::create([
                'house_account_id' => $account->id,
                'transaction_type' => 'invoice',
                'reference_type' => 'Invoice',
                'reference_id' => $invoice->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => "Invoice #{$invoice->invoice_number}",
                'user_id' => $user?->id,
            ]);

            $account->updateBalance(-$amount);

            Log::info('Invoice transaction processed', [
                'transaction_id' => $transaction->id,
                'house_account_id' => $account->id,
                'invoice_id' => $invoice->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            return $transaction;
        });
    }

    /**
     * Process a payment transaction (credit)
     */
    public function processPaymentTransaction(int $houseId, float $amount, string $description, ?User $user = null, ?string $referenceType = null, ?int $referenceId = null): AccountTransaction
    {
        return DB::transaction(function () use ($houseId, $amount, $description, $user, $referenceType, $referenceId) {
            $account = $this->getOrCreateHouseAccount($houseId);

            $balanceBefore = $account->current_balance;
            $balanceAfter = $balanceBefore + $amount;

            $transaction = AccountTransaction::create([
                'house_account_id' => $account->id,
                'transaction_type' => 'payment',
                'reference_type' => $referenceType ?? 'Payment',
                'reference_id' => $referenceId ?? 0, // Provide default value
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'user_id' => $user?->id,
            ]);

            $account->updateBalance($amount);

            Log::info('Payment transaction processed', [
                'transaction_id' => $transaction->id,
                'house_account_id' => $account->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            return $transaction;
        });
    }

    /**
     * Process an adjustment transaction
     */
    public function processAdjustmentTransaction(int $houseId, float $amount, string $description, ?User $user = null): AccountTransaction
    {
        return DB::transaction(function () use ($houseId, $amount, $description, $user) {
            $account = $this->getOrCreateHouseAccount($houseId);

            $balanceBefore = $account->current_balance;
            $balanceAfter = $balanceBefore + $amount; // Positive amount reduces debt, negative increases debt

            $transaction = AccountTransaction::create([
                'house_account_id' => $account->id,
                'transaction_type' => 'adjustment',
                'reference_type' => 'Adjustment',
                'reference_id' => null,
                'amount' => abs($amount),
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'user_id' => $user?->id,
            ]);

            $account->updateBalance($amount);

            Log::info('Adjustment transaction processed', [
                'transaction_id' => $transaction->id,
                'house_account_id' => $account->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            return $transaction;
        });
    }

    /**
     * Process a credit note transaction
     */
    public function processCreditNoteTransaction(int $houseId, float $amount, string $description, ?User $user = null, ?int $referenceId = null): AccountTransaction
    {
        return DB::transaction(function () use ($houseId, $amount, $description, $user, $referenceId) {
            $account = $this->getOrCreateHouseAccount($houseId);

            $balanceBefore = $account->current_balance;
            $balanceAfter = $balanceBefore + $amount;

            $transaction = AccountTransaction::create([
                'house_account_id' => $account->id,
                'transaction_type' => 'credit_note',
                'reference_type' => 'CreditNote',
                'reference_id' => $referenceId,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'user_id' => $user?->id,
            ]);

            $account->updateBalance($amount);

            Log::info('Credit note transaction processed', [
                'transaction_id' => $transaction->id,
                'house_account_id' => $account->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            return $transaction;
        });
    }

    /**
     * Get current balance for a house
     */
    public function getCurrentBalance(int $houseId): float
    {
        $account = $this->getOrCreateHouseAccount($houseId);

        return $account->current_balance;
    }

    /**
     * Get account statement for a house
     */
    public function getAccountStatement(int $houseId, ?\DateTime $startDate = null, ?\DateTime $endDate = null)
    {
        $account = $this->getOrCreateHouseAccount($houseId);

        $query = $account->transactions();

        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }

        return [
            'account' => $account,
            'transactions' => $query->orderBy('created_at', 'desc')->get(),
            'opening_balance' => $this->calculateOpeningBalance($account, $startDate),
            'closing_balance' => $account->current_balance,
        ];
    }

    /**
     * Calculate opening balance for a given date
     */
    private function calculateOpeningBalance(HouseAccount $account, ?\DateTime $date): float
    {
        if (! $date) {
            return 0;
        }

        $transactionsBeforeDate = $account->transactions()
            ->where('created_at', '<', $date)
            ->get();

        $totalCredits = $transactionsBeforeDate->credits()->sum('amount');
        $totalDebits = $transactionsBeforeDate->debits()->sum('amount');

        return $totalCredits - $totalDebits;
    }

    /**
     * Check if house has sufficient balance for a given amount
     */
    public function hasSufficientBalance(int $houseId, float $amount): bool
    {
        $account = $this->getOrCreateHouseAccount($houseId);

        return $account->hasSufficientBalance($amount);
    }

    /**
     * Get outstanding balance (amount owed)
     */
    public function getOutstandingBalance(int $houseId): float
    {
        $account = $this->getOrCreateHouseAccount($houseId);

        return $account->getOutstandingBalance();
    }

    /**
     * Get credit balance (amount in credit)
     */
    public function getCreditBalance(int $houseId): float
    {
        $account = $this->getOrCreateHouseAccount($houseId);

        return $account->getCreditBalance();
    }
}
