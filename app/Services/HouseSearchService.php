<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class HouseSearchService
{
    public function searchEstates(array $filters = []): Builder
    {
        $query = Estate::query();

        if (! empty($filters['search'])) {
            $query->where(function (Builder $q) use ($filters) {
                $q->where('name', 'like', "%{$filters['search']}%")
                    ->orWhere('code', 'like', "%{$filters['search']}%")
                    ->orWhere('location', 'like', "%{$filters['search']}%")
                    ->orWhere('manager_name', 'like', "%{$filters['search']}%");
            });
        }

        if (! empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (! empty($filters['manager_name'])) {
            $query->where('manager_name', 'like', "%{$filters['manager_name']}%");
        }

        if (! empty($filters['location'])) {
            $query->where('location', 'like', "%{$filters['location']}%");
        }

        if (isset($filters['has_vacant_houses'])) {
            $query->whereHas('houses', function (Builder $q) {
                $q->where('occupancy_status', 'vacant');
            });
        }

        return $query;
    }

    public function searchHouses(array $filters = []): Builder
    {
        $query = House::query()->with(['estate', 'contacts']);

        if (! empty($filters['search'])) {
            $query->where(function (Builder $q) use ($filters) {
                $q->where('house_number', 'like', "%{$filters['search']}%")
                    ->orWhere('meter_number', 'like', "%{$filters['search']}%")
                    ->orWhereHas('estate', function (Builder $eq) use ($filters) {
                        $eq->where('name', 'like', "%{$filters['search']}%")
                            ->orWhere('code', 'like', "%{$filters['search']}%");
                    })
                    ->orWhereHas('contacts', function (Builder $cq) use ($filters) {
                        $cq->where('first_name', 'like', "%{$filters['search']}%")
                            ->orWhere('last_name', 'like', "%{$filters['search']}%")
                            ->orWhere('email', 'like', "%{$filters['search']}%")
                            ->orWhere('phone', 'like', "%{$filters['search']}%");
                    });
            });
        }

        if (! empty($filters['estate_id'])) {
            $query->where('estate_id', $filters['estate_id']);
        }

        if (! empty($filters['occupancy_status'])) {
            $query->where('occupancy_status', $filters['occupancy_status']);
        }

        if (! empty($filters['house_type'])) {
            $query->where('house_type', $filters['house_type']);
        }

        if (! empty($filters['min_bedrooms'])) {
            $query->where('bedrooms', '>=', $filters['min_bedrooms']);
        }

        if (! empty($filters['max_bedrooms'])) {
            $query->where('bedrooms', '<=', $filters['max_bedrooms']);
        }

        if (! empty($filters['min_rent'])) {
            $query->where('monthly_rent', '>=', $filters['min_rent']);
        }

        if (! empty($filters['max_rent'])) {
            $query->where('monthly_rent', '<=', $filters['max_rent']);
        }

        if (isset($filters['has_meter'])) {
            $query->whereNotNull('meter_number');
        }

        if (isset($filters['has_contacts'])) {
            $query->has('contacts');
        }

        if (isset($filters['contact_count'])) {
            $query->withCount('contacts')
                ->having('contacts_count', $filters['contact_count']['operator'] ?? '=', $filters['contact_count']['value']);
        }

        return $query;
    }

    public function searchContacts(array $filters = []): Builder
    {
        $query = Contact::query()->with(['houses.estate']);

        if (! empty($filters['search'])) {
            $query->where(function (Builder $q) use ($filters) {
                $q->where('first_name', 'like', "%{$filters['search']}%")
                    ->orWhere('last_name', 'like', "%{$filters['search']}%")
                    ->orWhere('email', 'like', "%{$filters['search']}%")
                    ->orWhere('phone', 'like', "%{$filters['search']}%")
                    ->orWhere('id_number', 'like', "%{$filters['search']}%")
                    ->orWhere('company_name', 'like', "%{$filters['search']}%");
            });
        }

        if (! empty($filters['first_name'])) {
            $query->where('first_name', 'like', "%{$filters['first_name']}%");
        }

        if (! empty($filters['last_name'])) {
            $query->where('last_name', 'like', "%{$filters['last_name']}%");
        }

        if (! empty($filters['email'])) {
            $query->where('email', 'like', "%{$filters['email']}%");
        }

        if (! empty($filters['phone'])) {
            $query->where('phone', 'like', "%{$filters['phone']}%");
        }

        if (! empty($filters['contact_type'])) {
            $query->where('contact_type', $filters['contact_type']);
        }

        if (! empty($filters['house_id'])) {
            $query->whereHas('houses', function (Builder $q) use ($filters) {
                $q->where('houses.id', $filters['house_id']);
            });
        }

        if (! empty($filters['estate_id'])) {
            $query->whereHas('houses.estate', function (Builder $q) use ($filters) {
                $q->where('estates.id', $filters['estate_id']);
            });
        }

        if (isset($filters['is_primary'])) {
            $query->whereHas('houses', function (Builder $q) use ($filters) {
                $q->where('is_primary', $filters['is_primary']);
            });
        }

        if (isset($filters['active_only'])) {
            $query->where('is_active', true);
        }

        return $query;
    }

    public function getEstateStatistics(int $estateId): array
    {
        $estate = Estate::withCount([
            'houses',
            'houses as occupied_houses_count' => function (Builder $query) {
                $query->where('occupancy_status', 'occupied');
            },
            'houses as vacant_houses_count' => function (Builder $query) {
                $query->where('occupancy_status', 'vacant');
            },
            'houses as maintenance_houses_count' => function (Builder $query) {
                $query->where('occupancy_status', 'maintenance');
            },
        ])->findOrFail($estateId);

        return [
            'total_houses' => $estate->houses_count,
            'occupied_houses' => $estate->occupied_houses_count,
            'vacant_houses' => $estate->vacant_houses_count,
            'maintenance_houses' => $estate->maintenance_houses_count,
            'occupancy_rate' => $estate->houses_count > 0
                ? round(($estate->occupied_houses_count / $estate->houses_count) * 100, 2)
                : 0,
        ];
    }

    public function getHouseOccupancyReport(): Collection
    {
        return Estate::withCount([
            'houses as total_houses',
            'houses as occupied_houses' => function (Builder $query) {
                $query->where('occupancy_status', 'occupied');
            },
            'houses as vacant_houses' => function (Builder $query) {
                $query->where('occupancy_status', 'vacant');
            },
        ])->get()->map(function (Estate $estate) {
            return [
                'estate_id' => $estate->id,
                'estate_name' => $estate->name,
                'total_houses' => $estate->total_houses,
                'occupied_houses' => $estate->occupied_houses,
                'vacant_houses' => $estate->vacant_houses,
                'occupancy_rate' => $estate->total_houses > 0
                    ? round(($estate->occupied_houses / $estate->total_houses) * 100, 2)
                    : 0,
            ];
        });
    }

    public function getContactHouseAssignments(int $contactId): Collection
    {
        $contact = Contact::with(['houses' => function ($query) {
            $query->withPivot(['is_primary', 'relationship', 'start_date', 'end_date'])
                ->orderBy('pivot_is_primary', 'desc')
                ->orderBy('pivot_start_date', 'desc');
        }])->findOrFail($contactId);

        return $contact->houses->map(function ($house) {
            return [
                'house_id' => $house->id,
                'house_number' => $house->house_number,
                'estate_name' => $house->estate->name,
                'is_primary' => $house->pivot->is_primary,
                'relationship' => $house->pivot->relationship,
                'start_date' => $house->pivot->start_date,
                'end_date' => $house->pivot->end_date,
                'current' => $house->pivot->end_date === null,
            ];
        });
    }
}
