<?php

namespace App\Services;

use App\Models\MeterReading;
use App\Services\Validation\BasicValidationRule;
use App\Services\Validation\ConsumptionValidationRule;
use App\Services\Validation\ContextualRule;
use App\Services\Validation\PatternAnomalyRule;
use App\Services\Validation\RuleEngine;
use App\Services\Validation\StatisticalAnomalyRule;
use Illuminate\Support\Collection;

class ReadingValidationService
{
    private RuleEngine $ruleEngine;

    public function __construct()
    {
        $this->ruleEngine = new RuleEngine;
        $this->initializeDefaultRules();
    }

    private function initializeDefaultRules(): void
    {
        $this->ruleEngine
            ->addRule(new BasicValidationRule)
            ->addRule(new ConsumptionValidationRule)
            ->addRule(new StatisticalAnomalyRule)
            ->addRule(new PatternAnomalyRule)
            ->addRule(new ContextualRule);
    }

    public function validateReading(MeterReading $reading): array
    {
        $result = $this->ruleEngine->validate($reading);

        return $result->toArray();
    }

    public function getRuleEngine(): RuleEngine
    {
        return $this->ruleEngine;
    }

    public function addCustomRule($rule): self
    {
        $this->ruleEngine->addRule($rule);

        return $this;
    }

    public function removeRule(string $ruleName): self
    {
        $this->ruleEngine->removeRule($ruleName);

        return $this;
    }

    public function getAvailableRules(): array
    {
        return $this->ruleEngine->getRules()->map(function ($rule) {
            return [
                'name' => $rule->getName(),
                'description' => $rule->getDescription(),
                'severity' => $rule->getSeverity(),
            ];
        })->toArray();
    }

    public function validateAndSave(MeterReading $reading): bool
    {
        $validationResult = $this->validateReading($reading);

        // Update reading with validation results
        $reading->validation_results = $validationResult;
        $reading->validation_status = $validationResult['risk_level'] === 'low' && $validationResult['confidence_score'] >= 80 ? 'approved' : 'pending';
        $reading->confidence_score = $validationResult['confidence_score'];
        $reading->risk_level = $validationResult['risk_level'];
        $reading->validated_at = now();
        $reading->validated_by = auth()->id();

        return $reading->save();
    }

    public function getFlaggedReadings(?string $riskLevel = null, int $limit = 50): Collection
    {
        $query = MeterReading::where('validation_status', '!=', 'approved')
            ->orWhere('risk_level', '!=', 'low');

        if ($riskLevel) {
            $query->where('risk_level', $riskLevel);
        }

        return $query->with(['house', 'house.estate'])
            ->orderBy('risk_level', 'desc')
            ->orderBy('confidence_score', 'asc')
            ->limit($limit)
            ->get();
    }

    public function getValidationStats(): array
    {
        $total = MeterReading::count();
        $approved = MeterReading::where('validation_status', 'approved')->count();
        $pending = MeterReading::where('validation_status', 'pending')->count();
        $flagged = MeterReading::where('risk_level', '!=', 'low')->count();

        return [
            'total_readings' => $total,
            'approved_count' => $approved,
            'pending_count' => $pending,
            'flagged_count' => $flagged,
            'approval_rate' => $total > 0 ? round(($approved / $total) * 100, 2) : 0,
            'flag_rate' => $total > 0 ? round(($flagged / $total) * 100, 2) : 0,
        ];
    }
}
