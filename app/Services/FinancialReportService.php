<?php

namespace App\Services;

use App\Models\AccountTransaction;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\InvoiceAdjustment;
use App\Models\InvoicePayment;
use Carbon\Carbon;

class FinancialReportService
{
    /**
     * Generate aging report for outstanding invoices
     *
     * @param  int|null  $estateId  Optional estate ID to filter by
     */
    public function generateAgingReport(?int $estateId = null): array
    {
        $query = Invoice::whereNotIn('status', ['paid', 'cancelled', 'draft']);

        if ($estateId) {
            $query->whereHas('house.estate', function ($q) use ($estateId) {
                $q->where('id', $estateId);
            });
        }

        $invoices = $query->get();

        $agingBuckets = [
            'current' => 0,
            'days_1_30' => 0,
            'days_31_60' => 0,
            'days_61_90' => 0,
            'days_over_90' => 0,
        ];

        foreach ($invoices as $invoice) {
            if ($invoice->status === 'paid') {
                continue; // Skip paid invoices
            }

            $daysOverdue = max(0, $invoice->due_date->diffInDays(now()));
            $outstandingAmount = $invoice->balance_due;

            if ($daysOverdue === 0) {
                $agingBuckets['current'] += $outstandingAmount;
            } elseif ($daysOverdue <= 30) {
                $agingBuckets['days_1_30'] += $outstandingAmount;
            } elseif ($daysOverdue <= 60) {
                $agingBuckets['days_31_60'] += $outstandingAmount;
            } elseif ($daysOverdue <= 90) {
                $agingBuckets['days_61_90'] += $outstandingAmount;
            } else {
                $agingBuckets['days_over_90'] += $outstandingAmount;
            }
        }

        return [
            'current' => $agingBuckets['current'],
            'days_1_30' => $agingBuckets['days_1_30'],
            'days_31_60' => $agingBuckets['days_31_60'],
            'days_61_90' => $agingBuckets['days_61_90'],
            'days_over_90' => $agingBuckets['days_over_90'],
            'total_outstanding' => array_sum($agingBuckets),
            'generated_at' => now(),
            'estate_filter' => $estateId,
        ];
    }

    /**
     * Generate revenue report for a date range
     *
     * @param  int|null  $estateId  Optional estate ID to filter by
     */
    public function generateRevenueReport(Carbon $startDate, Carbon $endDate, ?int $estateId = null): array
    {
        $query = Invoice::whereBetween('paid_at', [$startDate, $endDate])
            ->where('status', 'paid');

        if ($estateId) {
            $query->whereHas('house.estate', function ($q) use ($estateId) {
                $q->where('id', $estateId);
            });
        }

        $invoices = $query->get();

        $totalRevenue = $invoices->sum('total_amount');
        $totalInvoices = $invoices->count();
        $averageInvoiceAmount = $totalInvoices > 0 ? $totalRevenue / $totalInvoices : 0;

        // Group by status
        $invoicesByStatus = $invoices->groupBy('status')->map(function ($group) {
            return $group->sum('total_amount');
        })->toArray();

        // Group by estate
        $revenueByEstate = $invoices->groupBy('house.estate.name')->map(function ($group) {
            return $group->sum('total_amount');
        })->toArray();

        // Group by month
        $revenueByMonth = $invoices->groupBy(function ($invoice) {
            return $invoice->paid_at->format('Y-m');
        })->map(function ($group) {
            return $group->sum('total_amount');
        })->toArray();

        // Group by payment method (extract from payment_reference)
        $revenueByPaymentMethod = [];
        foreach ($invoices as $invoice) {
            if ($invoice->payment_reference) {
                $paymentMethod = $this->extractPaymentMethod($invoice->payment_reference);
                $revenueByPaymentMethod[$paymentMethod] = ($revenueByPaymentMethod[$paymentMethod] ?? 0) + $invoice->total_amount;
            }
        }

        // Calculate collection rate
        $billedInvoices = Invoice::whereBetween('billing_period_end', [$startDate, $endDate])
            ->when($estateId, function ($q) use ($estateId) {
                return $q->whereHas('house.estate', function ($query) use ($estateId) {
                    $query->where('id', $estateId);
                });
            })
            ->get();

        $totalBilled = $billedInvoices->sum('total_amount');
        $totalCollected = $totalRevenue;
        $collectionRate = $totalBilled > 0 ? ($totalCollected / $totalBilled) * 100 : 0;

        return [
            'total_revenue' => $totalRevenue,
            'total_invoices' => $totalInvoices,
            'average_invoice_amount' => round($averageInvoiceAmount, 2),
            'invoices_by_status' => $invoicesByStatus,
            'revenue_by_estate' => $revenueByEstate,
            'revenue_by_month' => $revenueByMonth,
            'revenue_by_payment_method' => $revenueByPaymentMethod,
            'collection_rate' => round($collectionRate, 2),
            'total_billed' => $totalBilled,
            'total_collected' => $totalCollected,
            'period_start' => $startDate,
            'period_end' => $endDate,
            'estate_filter' => $estateId,
            'generated_at' => now(),
        ];
    }

    /**
     * Generate customer statement for a house
     */
    public function generateCustomerStatement(int $houseId, Carbon $startDate, Carbon $endDate): array
    {
        $house = House::findOrFail($houseId);

        // Get house account
        $houseAccount = $house->houseAccount()->first();
        if (! $houseAccount) {
            // Create house account if it doesn't exist
            $houseAccount = HouseAccount::create([
                'house_id' => $house->id,
                'current_balance' => 0,
                'total_credit' => 0,
                'total_debit' => 0,
            ]);
        }

        // Get opening balance (transactions before start date)
        $openingBalance = AccountTransaction::where('house_account_id', $houseAccount->id)
            ->where('created_at', '<', $startDate)
            ->sum('amount');

        // Get transactions within date range
        $transactions = AccountTransaction::where('house_account_id', $houseAccount->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'asc')
            ->get();

        // Also include invoices, payments, and adjustments for completeness
        $invoices = Invoice::where('house_id', $houseId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $payments = InvoicePayment::whereHas('invoice', function ($query) use ($houseId) {
            $query->where('house_id', $houseId);
        })
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->get();

        $adjustments = InvoiceAdjustment::whereHas('invoice', function ($query) use ($houseId) {
            $query->where('house_id', $houseId);
        })
            ->whereBetween('adjustment_date', [$startDate, $endDate])
            ->get();

        // Combine all transactions
        $allTransactions = collect();

        // Add account transactions
        foreach ($transactions as $transaction) {
            $allTransactions->push([
                'date' => $transaction->created_at,
                'type' => $transaction->transaction_type,
                'description' => $transaction->description ?? ucfirst($transaction->transaction_type),
                'reference' => $transaction->reference_type.' #'.$transaction->reference_id,
                'amount' => $transaction->amount,
                'balance' => $transaction->balance_after,
            ]);
        }

        // Add invoices
        foreach ($invoices as $invoice) {
            $allTransactions->push([
                'date' => $invoice->created_at,
                'type' => 'invoice',
                'description' => 'Invoice #'.$invoice->invoice_number,
                'reference' => 'Invoice #'.$invoice->invoice_number,
                'amount' => $invoice->total_amount,
                'balance' => null, // Will be calculated
            ]);
        }

        // Add payments
        foreach ($payments as $payment) {
            $allTransactions->push([
                'date' => $payment->payment_date,
                'type' => 'payment',
                'description' => 'Payment for Invoice #'.$payment->invoice->invoice_number,
                'reference' => $payment->reference_number,
                'amount' => -$payment->amount,
                'balance' => null, // Will be calculated
            ]);
        }

        // Add adjustments
        foreach ($adjustments as $adjustment) {
            $allTransactions->push([
                'date' => $adjustment->adjustment_date,
                'type' => 'adjustment',
                'description' => ucfirst($adjustment->type).' Adjustment',
                'reference' => $adjustment->reference_number,
                'amount' => $adjustment->amount,
                'balance' => null, // Will be calculated
            ]);
        }

        // Sort all transactions by date
        $allTransactions = $allTransactions->sortBy('date');

        // Calculate running balance
        $runningBalance = $openingBalance;
        foreach ($allTransactions as &$transaction) {
            if ($transaction['balance'] === null) {
                $runningBalance += $transaction['amount'];
                $transaction['balance'] = $runningBalance;
            }
        }

        $closingBalance = $runningBalance;

        return [
            'house_info' => [
                'id' => $house->id,
                'house_number' => $house->house_number,
                'estate_name' => $house->estate->name,
                'primary_contact' => $house->primaryContact?->name,
            ],
            'opening_balance' => $openingBalance,
            'transactions' => $allTransactions->values()->all(),
            'closing_balance' => $closingBalance,
            'period_start' => $startDate,
            'period_end' => $endDate,
            'generated_at' => now(),
        ];
    }

    /**
     * Generate account transactions report
     */
    public function generateAccountTransactionsReport(int $houseId, Carbon $startDate, Carbon $endDate): array
    {
        $house = House::findOrFail($houseId);

        $houseAccount = $house->houseAccount()->first();
        if (! $houseAccount) {
            // Create house account if it doesn't exist
            $houseAccount = HouseAccount::create([
                'house_id' => $house->id,
                'current_balance' => 0,
                'total_credit' => 0,
                'total_debit' => 0,
            ]);
        }

        $transactions = AccountTransaction::where('house_account_id', $houseAccount->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'asc')
            ->get();

        $totalDebits = $transactions->where('amount', '>', 0)->sum('amount');
        $totalCredits = abs($transactions->where('amount', '<', 0)->sum('amount'));
        $netChange = $totalDebits - $totalCredits;

        return [
            'house_info' => [
                'id' => $house->id,
                'house_number' => $house->house_number,
                'estate_name' => $house->estate->name,
            ],
            'transactions' => $transactions->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'date' => $transaction->created_at,
                    'type' => $transaction->transaction_type,
                    'description' => $transaction->description,
                    'reference_type' => $transaction->reference_type,
                    'reference_id' => $transaction->reference_id,
                    'amount' => $transaction->amount,
                    'balance_before' => $transaction->balance_before,
                    'balance_after' => $transaction->balance_after,
                    'user' => $transaction->user?->name,
                ];
            })->all(),
            'total_debits' => $totalDebits,
            'total_credits' => $totalCredits,
            'net_change' => $netChange,
            'period_start' => $startDate,
            'period_end' => $endDate,
            'generated_at' => now(),
        ];
    }

    /**
     * Extract payment method from payment reference
     */
    private function extractPaymentMethod(string $paymentReference): string
    {
        $reference = strtoupper($paymentReference);

        if (str_contains($reference, 'MPESA') || str_contains($reference, 'M-PESA')) {
            return 'MPESA';
        } elseif (str_contains($reference, 'BANK') || str_contains($reference, 'TRANSFER')) {
            return 'BANK';
        } elseif (str_contains($reference, 'CASH')) {
            return 'CASH';
        } elseif (str_contains($reference, 'CHEQUE')) {
            return 'CHEQUE';
        } else {
            return 'OTHER';
        }
    }
}
