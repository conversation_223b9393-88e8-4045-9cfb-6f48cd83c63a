<?php

namespace App\Services;

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ReportExportService
{
    public function generateManagementReport(array $filters = []): array
    {
        $dateRange = $filters['date_range'] ?? 30;
        $selectedEstate = $filters['estate_id'] ?? null;
        $startDate = Carbon::now()->subDays($dateRange);

        return [
            'summary' => $this->getSummaryData($startDate, $selectedEstate),
            'estate_analytics' => $this->getEstateAnalytics($startDate, $selectedEstate),
            'consumption_trends' => $this->getConsumptionTrends($startDate, $selectedEstate),
            'revenue_analysis' => $this->getRevenueAnalysis($startDate, $selectedEstate),
            'top_consumers' => $this->getTopConsumers($startDate, $selectedEstate),
            'overdue_accounts' => $this->getOverdueAccounts($selectedEstate),
            'date_range' => $dateRange,
            'generated_at' => Carbon::now(),
        ];
    }

    public function generateEstateReport(Estate $estate, array $filters = []): array
    {
        $dateRange = $filters['date_range'] ?? 30;
        $startDate = Carbon::now()->subDays($dateRange);

        return [
            'estate_info' => [
                'name' => $estate->name,
                'location' => $estate->location,
                'total_houses' => $estate->houses->count(),
                'occupied_houses' => $estate->houses->where('status', 'occupied')->count(),
                'occupancy_rate' => ($estate->houses->where('status', 'occupied')->count() / max(1, $estate->houses->count())) * 100,
            ],
            'consumption_summary' => $this->getEstateConsumptionSummary($estate, $startDate),
            'house_analytics' => $this->getHouseAnalytics($estate, $startDate),
            'billing_summary' => $this->getEstateBillingSummary($estate, $startDate),
            'maintenance_alerts' => $this->getMaintenanceAlerts($estate),
            'date_range' => $dateRange,
            'generated_at' => Carbon::now(),
        ];
    }

    private function getSummaryData(Carbon $startDate, ?int $estateId): array
    {
        $baseQuery = function ($query) use ($startDate, $estateId) {
            $query->where('created_at', '>=', $startDate);

            if ($estateId) {
                $query->whereHas('house', function ($q) use ($estateId) {
                    $q->where('estate_id', $estateId);
                });
            }
        };

        return [
            'total_houses' => $estateId
                ? House::where('estate_id', $estateId)->count()
                : House::count(),
            'total_estates' => Estate::count(),
            'total_consumption' => MeterReading::where($baseQuery)->sum('consumption'),
            'total_revenue' => Invoice::where($baseQuery)->sum('amount'),
            'avg_daily_consumption' => MeterReading::where($baseQuery)
                ->selectRaw('AVG(consumption) as avg, COUNT(DISTINCT DATE(created_at)) as days')
                ->first()
                ->avg ?? 0,
            'pending_readings' => MeterReading::where('status', 'pending')
                ->when($estateId, function ($query) use ($estateId) {
                    $query->whereHas('house', function ($q) use ($estateId) {
                        $q->where('estate_id', $estateId);
                    });
                })
                ->count(),
            'overdue_invoices' => Invoice::where('status', 'overdue')
                ->when($estateId, function ($query) use ($estateId) {
                    $query->whereHas('house', function ($q) use ($estateId) {
                        $q->where('estate_id', $estateId);
                    });
                })
                ->count(),
        ];
    }

    private function getEstateAnalytics(Carbon $startDate, ?int $estateId): Collection
    {
        $query = Estate::with(['houses.meterReadings' => function ($query) use ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }, 'houses.invoices' => function ($query) use ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }]);

        if ($estateId) {
            $query->where('id', $estateId);
        }

        return $query->get()->map(function ($estate) {
            $totalConsumption = $estate->houses->flatMap->meterReadings->sum('consumption');
            $totalRevenue = $estate->houses->flatMap->invoices->sum('amount');
            $totalHouses = $estate->houses->count();

            return [
                'estate' => $estate->name,
                'total_houses' => $totalHouses,
                'total_consumption' => $totalConsumption,
                'total_revenue' => $totalRevenue,
                'avg_consumption_per_house' => $totalHouses > 0 ? $totalConsumption / $totalHouses : 0,
                'revenue_per_house' => $totalHouses > 0 ? $totalRevenue / $totalHouses : 0,
                'occupancy_rate' => $totalHouses > 0
                    ? ($estate->houses->where('status', 'occupied')->count() / $totalHouses) * 100
                    : 0,
            ];
        })->sortByDesc('total_consumption');
    }

    private function getConsumptionTrends(Carbon $startDate, ?int $estateId): Collection
    {
        $query = MeterReading::selectRaw('DATE(created_at) as date, SUM(consumption) as total_consumption, COUNT(*) as reading_count')
            ->where('created_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date');

        if ($estateId) {
            $query->whereHas('house', function ($q) use ($estateId) {
                $q->where('estate_id', $estateId);
            });
        }

        return $query->get()->map(function ($item) {
            return [
                'date' => $item->date,
                'total_consumption' => $item->total_consumption,
                'reading_count' => $item->reading_count,
                'avg_consumption' => $item->reading_count > 0 ? $item->total_consumption / $item->reading_count : 0,
            ];
        });
    }

    private function getRevenueAnalysis(Carbon $startDate, ?int $estateId): array
    {
        $query = Invoice::where('created_at', '>=', $startDate);

        if ($estateId) {
            $query->whereHas('house', function ($q) use ($estateId) {
                $q->where('estate_id', $estateId);
            });
        }

        $revenueByStatus = $query->selectRaw('status, SUM(amount) as total, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->keyBy('status');

        $revenueByDate = $query->selectRaw('DATE(created_at) as date, SUM(amount) as total, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'by_status' => $revenueByStatus,
            'by_date' => $revenueByDate,
            'total_revenue' => $revenueByStatus->sum('total'),
            'total_invoices' => $revenueByStatus->sum('count'),
            'collection_rate' => $this->calculateCollectionRate($revenueByStatus),
        ];
    }

    private function getTopConsumers(Carbon $startDate, ?int $estateId): Collection
    {
        $query = House::with(['meterReadings' => function ($query) use ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }, 'estate'])
            ->when($estateId, function ($query) use ($estateId) {
                $query->where('estate_id', $estateId);
            });

        return $query->get()->map(function ($house) {
            $totalConsumption = $house->meterReadings->sum('consumption');
            $readingCount = $house->meterReadings->count();

            return [
                'house' => $house->house_number,
                'estate' => $house->estate->name,
                'total_consumption' => $totalConsumption,
                'reading_count' => $readingCount,
                'avg_daily_consumption' => $readingCount > 0 ? $totalConsumption / $readingCount : 0,
                'status' => $house->status,
            ];
        })->sortByDesc('total_consumption')->take(20);
    }

    private function getOverdueAccounts(?int $estateId): Collection
    {
        return Invoice::with(['house.estate', 'house.contacts'])
            ->where('status', 'overdue')
            ->when($estateId, function ($query) use ($estateId) {
                $query->whereHas('house', function ($q) use ($estateId) {
                    $q->where('estate_id', $estateId);
                });
            })
            ->get()
            ->map(function ($invoice) {
                return [
                    'house' => $invoice->house->house_number,
                    'estate' => $invoice->house->estate->name,
                    'amount' => $invoice->amount,
                    'due_date' => $invoice->due_date,
                    'days_overdue' => Carbon::parse($invoice->due_date)->diffInDays(Carbon::now()),
                    'contact' => $invoice->house->contacts->first()?->name ?? 'N/A',
                ];
            })
            ->sortByDesc('days_overdue');
    }

    private function getEstateConsumptionSummary(Estate $estate, Carbon $startDate): array
    {
        $readings = $estate->houses->flatMap->meterReadings
            ->where('created_at', '>=', $startDate);

        return [
            'total_consumption' => $readings->sum('consumption'),
            'reading_count' => $readings->count(),
            'avg_daily_consumption' => $readings->count() > 0 ? $readings->sum('consumption') / $readings->count() : 0,
            'max_consumption' => $readings->max('consumption') ?? 0,
            'min_consumption' => $readings->min('consumption') ?? 0,
            'houses_with_readings' => $readings->groupBy('house_id')->count(),
        ];
    }

    private function getHouseAnalytics(Estate $estate, Carbon $startDate): Collection
    {
        return $estate->houses->map(function ($house) use ($startDate) {
            $readings = $house->meterReadings->where('created_at', '>=', $startDate);
            $invoices = $house->invoices->where('created_at', '>=', $startDate);

            return [
                'house_number' => $house->house_number,
                'status' => $house->status,
                'total_consumption' => $readings->sum('consumption'),
                'reading_count' => $readings->count(),
                'total_billed' => $invoices->sum('amount'),
                'total_paid' => $invoices->where('status', 'paid')->sum('amount'),
                'outstanding_balance' => $invoices->where('status', '!=', 'paid')->sum('amount'),
                'last_reading_date' => $readings->max('created_at'),
            ];
        })->sortByDesc('total_consumption');
    }

    private function getEstateBillingSummary(Estate $estate, Carbon $startDate): array
    {
        $invoices = $estate->houses->flatMap->invoices
            ->where('created_at', '>=', $startDate);

        return [
            'total_invoiced' => $invoices->sum('amount'),
            'total_paid' => $invoices->where('status', 'paid')->sum('amount'),
            'total_outstanding' => $invoices->where('status', '!=', 'paid')->sum('amount'),
            'collection_rate' => $invoices->sum('amount') > 0
                ? ($invoices->where('status', 'paid')->sum('amount') / $invoices->sum('amount')) * 100
                : 0,
            'invoice_count' => $invoices->count(),
            'paid_invoices' => $invoices->where('status', 'paid')->count(),
            'overdue_invoices' => $invoices->where('status', 'overdue')->count(),
        ];
    }

    private function getMaintenanceAlerts(Estate $estate): Collection
    {
        return $estate->houses->filter(function ($house) {
            // Check for unusual consumption patterns
            $recentReadings = $house->meterReadings->take(3);
            if ($recentReadings->count() < 3) {
                return false;
            }

            $avgConsumption = $recentReadings->avg('consumption');
            $lastReading = $recentReadings->first();

            return $lastReading && $lastReading->consumption > $avgConsumption * 2;
        })->map(function ($house) {
            return [
                'house' => $house->house_number,
                'alert_type' => 'High Consumption',
                'message' => 'Unusual water consumption detected',
                'severity' => 'warning',
            ];
        });
    }

    private function calculateCollectionRate(Collection $revenueByStatus): float
    {
        $total = $revenueByStatus->sum('total');
        $paid = $revenueByStatus->get('paid')?->total ?? 0;

        return $total > 0 ? ($paid / $total) * 100 : 0;
    }
}
