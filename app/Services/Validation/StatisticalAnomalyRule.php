<?php

namespace App\Services\Validation;

use App\Models\MeterReading;
use Illuminate\Support\Collection;

class StatisticalAnomalyRule implements ValidationRule
{
    private const MIN_HISTORY_READINGS = 3;

    public function validate(MeterReading $reading): ValidationResult
    {
        $result = new ValidationResult;
        $house = $reading->house;
        $history = $this->getReadingHistory($house->id, $reading->reading_date);

        if ($history->count() < self::MIN_HISTORY_READINGS) {
            return $result;
        }

        $consumptions = $history->pluck('consumption')->toArray();
        $median = $this->calculateMedian($consumptions);
        $q1 = $this->calculatePercentile($consumptions, 25);
        $q3 = $this->calculatePercentile($consumptions, 75);
        $iqr = $q3 - $q1;

        // IQR method for outlier detection
        $lowerBound = $q1 - (1.5 * $iqr);
        $upperBound = $q3 + (1.5 * $iqr);

        if ($reading->consumption < $lowerBound || $reading->consumption > $upperBound) {
            $result->addAnomaly([
                'type' => 'statistical_outlier',
                'severity' => 'medium',
                'message' => 'Reading is a statistical outlier',
                'value' => $reading->consumption,
                'lower_bound' => round($lowerBound, 2),
                'upper_bound' => round($upperBound, 2),
                'median' => round($median, 2),
                'q1' => round($q1, 2),
                'q3' => round($q3, 2),
                'iqr' => round($iqr, 2),
            ]);
        }

        // Z-score method for additional validation
        $mean = array_sum($consumptions) / count($consumptions);
        $stdDev = $this->calculateStandardDeviation($consumptions);

        if ($stdDev > 0) {
            $zScore = abs($reading->consumption - $mean) / $stdDev;

            if ($zScore > 3) {
                $result->addAnomaly([
                    'type' => 'z_score_outlier',
                    'severity' => 'high',
                    'message' => 'Reading is a statistical outlier based on Z-score',
                    'value' => $reading->consumption,
                    'z_score' => round($zScore, 2),
                    'mean' => round($mean, 2),
                    'std_dev' => round($stdDev, 2),
                ]);
            } elseif ($zScore > 2) {
                $result->addWarning([
                    'type' => 'z_score_warning',
                    'severity' => 'medium',
                    'message' => 'Reading shows moderate statistical deviation',
                    'value' => $reading->consumption,
                    'z_score' => round($zScore, 2),
                    'mean' => round($mean, 2),
                    'std_dev' => round($stdDev, 2),
                ]);
            }
        }

        return $result;
    }

    public function getName(): string
    {
        return 'statistical_anomaly';
    }

    public function getDescription(): string
    {
        return 'Detects statistical outliers using IQR and Z-score methods';
    }

    public function getSeverity(): string
    {
        return 'medium';
    }

    private function getReadingHistory(int $houseId, \Carbon\Carbon $beforeDate, int $limit = 12): Collection
    {
        return MeterReading::where('house_id', $houseId)
            ->where('status', 'approved')
            ->where('reading_date', '<', $beforeDate)
            ->orderBy('reading_date', 'desc')
            ->limit($limit)
            ->get();
    }

    private function calculateMedian(array $values): float
    {
        sort($values);
        $count = count($values);

        if ($count === 0) {
            return 0;
        }
        if ($count % 2 === 0) {
            return ($values[$count / 2 - 1] + $values[$count / 2]) / 2;
        }

        return $values[floor($count / 2)];
    }

    private function calculatePercentile(array $values, float $percentile): float
    {
        if (empty($values)) {
            return 0;
        }

        sort($values);
        $index = ($percentile / 100) * (count($values) - 1);

        if (floor($index) == $index) {
            return $values[$index];
        }

        $lower = $values[floor($index)];
        $upper = $values[ceil($index)];
        $weight = $index - floor($index);

        return $lower + ($weight * ($upper - $lower));
    }

    private function calculateStandardDeviation(array $values): float
    {
        if (count($values) < 2) {
            return 0;
        }

        $mean = array_sum($values) / count($values);
        $squaredDiffs = array_map(function ($value) use ($mean) {
            return pow($value - $mean, 2);
        }, $values);

        return sqrt(array_sum($squaredDiffs) / count($values));
    }
}
