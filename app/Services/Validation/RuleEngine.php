<?php

namespace App\Services\Validation;

use App\Models\MeterReading;
use Illuminate\Support\Collection;

class RuleEngine
{
    private Collection $rules;

    public function __construct()
    {
        $this->rules = collect();
    }

    public function addRule(ValidationRule $rule): self
    {
        $this->rules->push($rule);

        return $this;
    }

    public function addRules(array $rules): self
    {
        foreach ($rules as $rule) {
            $this->addRule($rule);
        }

        return $this;
    }

    public function validate(MeterReading $reading): ValidationResult
    {
        $result = new ValidationResult;

        foreach ($this->rules as $rule) {
            $ruleResult = $rule->validate($reading);

            // Merge anomalies and warnings
            foreach ($ruleResult->anomalies as $anomaly) {
                $result->addAnomaly(array_merge($anomaly, ['rule' => $rule->getName()]));
            }

            foreach ($ruleResult->warnings as $warning) {
                $result->addWarning(array_merge($warning, ['rule' => $rule->getName()]));
            }

            // If any rule marks as invalid, the whole result is invalid
            if (! $ruleResult->isValid) {
                $result->isValid = false;
            }
        }

        // Calculate final risk level and confidence score
        $result->calculateRiskLevel();
        $result->calculateConfidenceScore();

        return $result;
    }

    public function getRules(): Collection
    {
        return $this->rules;
    }

    public function getRuleByName(string $name): ?ValidationRule
    {
        return $this->rules->first(fn ($rule) => $rule->getName() === $name);
    }

    public function removeRule(string $name): self
    {
        $this->rules = $this->rules->reject(fn ($rule) => $rule->getName() === $name);

        return $this;
    }

    public function clearRules(): self
    {
        $this->rules = collect();

        return $this;
    }
}
