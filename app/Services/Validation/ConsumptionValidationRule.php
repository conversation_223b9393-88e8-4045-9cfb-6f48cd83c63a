<?php

namespace App\Services\Validation;

use App\Models\MeterReading;
use Illuminate\Support\Collection;

class ConsumptionValidationRule implements ValidationRule
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'anomaly_threshold_high' => 2.5,
            'anomaly_threshold_medium' => 1.8,
            'min_history_readings' => 3,
            'max_history_readings' => 12,
        ], $config);
    }

    public function validate(MeterReading $reading): ValidationResult
    {
        $result = new ValidationResult;
        $house = $reading->house;
        $history = $this->getReadingHistory($house->id, $reading->reading_date);

        if ($history->count() < $this->config['min_history_readings']) {
            $result->addWarning([
                'type' => 'insufficient_history',
                'severity' => 'low',
                'message' => 'Insufficient historical data for consumption analysis',
                'required_readings' => $this->config['min_history_readings'],
                'available_readings' => $history->count(),
            ]);

            return $result;
        }

        $avgConsumption = $history->avg('consumption');
        $stdDeviation = $this->calculateStandardDeviation($history->pluck('consumption')->toArray());

        // Calculate deviation from historical average
        $deviation = abs($reading->consumption - $avgConsumption);
        $deviationRatio = $avgConsumption > 0 ? $deviation / $avgConsumption : 0;

        if ($deviationRatio > $this->config['anomaly_threshold_high']) {
            $result->addAnomaly([
                'type' => 'high_deviation',
                'severity' => 'high',
                'message' => 'Consumption significantly deviates from historical average',
                'value' => $reading->consumption,
                'average' => round($avgConsumption, 2),
                'deviation' => round($deviation, 2),
                'deviation_percentage' => round($deviationRatio * 100, 1),
            ]);
        } elseif ($deviationRatio > $this->config['anomaly_threshold_medium']) {
            $result->addWarning([
                'type' => 'medium_deviation',
                'severity' => 'medium',
                'message' => 'Consumption moderately deviates from historical average',
                'value' => $reading->consumption,
                'average' => round($avgConsumption, 2),
                'deviation' => round($deviation, 2),
                'deviation_percentage' => round($deviationRatio * 100, 1),
            ]);
        }

        // Check for sudden spikes or drops
        $latestReading = $history->first();
        if ($latestReading) {
            $changeRatio = $latestReading->consumption > 0
                ? abs($reading->consumption - $latestReading->consumption) / $latestReading->consumption
                : 0;

            if ($changeRatio > 2) {
                $result->addAnomaly([
                    'type' => 'sudden_change',
                    'severity' => 'high',
                    'message' => 'Sudden significant change in consumption pattern',
                    'previous' => $latestReading->consumption,
                    'current' => $reading->consumption,
                    'change_percentage' => round($changeRatio * 100, 1),
                ]);
            }
        }

        return $result;
    }

    public function getName(): string
    {
        return 'consumption_validation';
    }

    public function getDescription(): string
    {
        return 'Validates consumption patterns against historical data and detects sudden changes';
    }

    public function getSeverity(): string
    {
        return 'high';
    }

    private function getReadingHistory(int $houseId, \Carbon\Carbon $beforeDate, ?int $limit = null): Collection
    {
        $limit = $limit ?? $this->config['max_history_readings'];

        return MeterReading::where('house_id', $houseId)
            ->where('status', 'approved')
            ->where('reading_date', '<', $beforeDate)
            ->orderBy('reading_date', 'desc')
            ->limit($limit)
            ->get();
    }

    private function calculateStandardDeviation(array $values): float
    {
        if (count($values) < 2) {
            return 0;
        }

        $mean = array_sum($values) / count($values);
        $squaredDiffs = array_map(function ($value) use ($mean) {
            return pow($value - $mean, 2);
        }, $values);

        return sqrt(array_sum($squaredDiffs) / count($values));
    }
}
