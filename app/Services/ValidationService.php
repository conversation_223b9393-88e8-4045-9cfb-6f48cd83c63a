<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\House;
use App\Models\HouseContact;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ValidationService
{
    public function validateEstate(array $data, ?int $excludeId = null): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:estates,code'.($excludeId ? ",{$excludeId}" : ''),
            'location' => 'nullable|string|max:255',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:20',
            'manager_email' => 'nullable|email|max:255',
            'status' => 'required|in:active,inactive',
            'description' => 'nullable|string|max:1000',
            'total_houses' => 'nullable|integer|min:0',
            'occupied_houses' => 'nullable|integer|min:0',
            'vacant_houses' => 'nullable|integer|min:0',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateHouse(array $data, ?int $excludeId = null): array
    {
        $rules = [
            'estate_id' => 'required|exists:estates,id',
            'house_number' => [
                'required',
                'string',
                'max:50',
                new \App\Rules\UniqueHouseNumber($data['estate_id'] ?? null, $excludeId),
            ],
            'meter_number' => [
                'nullable',
                'string',
                'max:50',
                new \App\Rules\UniqueMeterNumber($excludeId),
            ],
            'house_type' => 'required|in:apartment,bungalow,maisonette,townhouse,studio',
            'bedrooms' => 'nullable|integer|min:0|max:20',
            'bathrooms' => 'nullable|integer|min:0|max:10',
            'square_footage' => 'nullable|numeric|min:0',
            'monthly_rent' => 'nullable|numeric|min:0',
            'security_deposit' => 'nullable|numeric|min:0',
            'occupancy_status' => 'required|in:vacant,occupied,maintenance',
            'occupancy_date' => 'nullable|date',
            'vacancy_date' => 'nullable|date|after_or_equal:occupancy_date',
            'description' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:2000',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateContact(array $data, ?int $excludeId = null): array
    {
        $rules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255|unique:contacts,email'.($excludeId ? ",{$excludeId}" : ''),
            'phone' => 'nullable|string|max:20',
            'id_number' => 'nullable|string|max:50|unique:contacts,id_number'.($excludeId ? ",{$excludeId}" : ''),
            'contact_type' => 'required|in:tenant,owner,agent,emergency',
            'company_name' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'date_of_birth' => 'nullable|date',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:2000',
            'is_active' => 'boolean',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateHouseContactAssignment(array $data): array
    {
        $rules = [
            'house_id' => 'required|exists:houses,id',
            'contact_id' => 'required|exists:contacts,id',
            'is_primary' => 'boolean',
            'relationship' => 'required|string|max:50',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateContactHouseAssignment(array $data): array
    {
        $rules = [
            'contact_id' => 'required|exists:contacts,id',
            'house_ids' => 'required|array',
            'house_ids.*' => 'exists:houses,id',
            'is_primary' => 'boolean',
            'relationship' => 'required|string|max:50',
            'start_date' => 'required|date',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateHouseNumberUniqueness(string $houseNumber, int $estateId, ?int $excludeId = null): bool
    {
        $query = House::where('house_number', $houseNumber)
            ->where('estate_id', $estateId);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return ! $query->exists();
    }

    public function validateMeterNumberUniqueness(?string $meterNumber, ?int $excludeId = null): bool
    {
        if (empty($meterNumber)) {
            return true;
        }

        $query = House::where('meter_number', $meterNumber);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return ! $query->exists();
    }

    public function validateContactEmailUniqueness(?string $email, ?int $excludeId = null): bool
    {
        if (empty($email)) {
            return true;
        }

        $query = Contact::where('email', $email);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return ! $query->exists();
    }

    public function validateContactIdNumberUniqueness(?string $idNumber, ?int $excludeId = null): bool
    {
        if (empty($idNumber)) {
            return true;
        }

        $query = Contact::where('id_number', $idNumber);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return ! $query->exists();
    }

    public function validateHouseContactUniqueness(int $houseId, int $contactId, ?int $excludeId = null): bool
    {
        $query = HouseContact::where('house_id', $houseId)
            ->where('contact_id', $contactId);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return ! $query->exists();
    }

    public function validatePrimaryContactLimit(int $houseId, ?int $excludeId = null): bool
    {
        $query = HouseContact::where('house_id', $houseId)
            ->where('is_primary', true);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->count() < 1;
    }

    public function validateDateRange(array $data): bool
    {
        if (empty($data['start_date']) || empty($data['end_date'])) {
            return true;
        }

        return strtotime($data['start_date']) <= strtotime($data['end_date']);
    }

    public function validateHouseOccupancyStatus(int $houseId, string $newStatus): bool
    {
        $house = House::find($houseId);

        if (! $house) {
            return false;
        }

        // Cannot mark as occupied if no primary contact
        if ($newStatus === 'occupied') {
            $hasPrimaryContact = HouseContact::where('house_id', $houseId)
                ->where('is_primary', true)
                ->whereNull('end_date')
                ->exists();

            return $hasPrimaryContact;
        }

        return true;
    }

    public function validateContactAssignmentDates(int $contactId, array $assignments): bool
    {
        foreach ($assignments as $assignment) {
            if (! $this->validateDateRange($assignment)) {
                return false;
            }

            // Check for overlapping assignments
            $overlapping = HouseContact::where('contact_id', $contactId)
                ->where('house_id', $assignment['house_id'])
                ->where(function ($query) use ($assignment) {
                    $query->whereNull('end_date')
                        ->orWhere('end_date', '>', $assignment['start_date']);
                })
                ->exists();

            if ($overlapping) {
                return false;
            }
        }

        return true;
    }

    public function getValidationMessages(): array
    {
        return [
            'estate' => [
                'name.required' => 'Estate name is required.',
                'code.required' => 'Estate code is required.',
                'code.unique' => 'Estate code already exists.',
                'status.required' => 'Estate status is required.',
                'status.in' => 'Invalid estate status.',
            ],
            'house' => [
                'estate_id.required' => 'Estate is required.',
                'estate_id.exists' => 'Selected estate does not exist.',
                'house_number.required' => 'House number is required.',
                'house_type.required' => 'House type is required.',
                'house_type.in' => 'Invalid house type.',
                'occupancy_status.required' => 'Occupancy status is required.',
                'occupancy_status.in' => 'Invalid occupancy status.',
                'vacancy_date.after_or_equal' => 'Vacancy date must be after or equal to occupancy date.',
            ],
            'contact' => [
                'first_name.required' => 'First name is required.',
                'last_name.required' => 'Last name is required.',
                'email.email' => 'Please provide a valid email address.',
                'email.unique' => 'Email address already exists.',
                'contact_type.required' => 'Contact type is required.',
                'contact_type.in' => 'Invalid contact type.',
            ],
            'house_contact' => [
                'house_id.required' => 'House is required.',
                'house_id.exists' => 'Selected house does not exist.',
                'contact_id.required' => 'Contact is required.',
                'contact_id.exists' => 'Selected contact does not exist.',
                'relationship.required' => 'Relationship is required.',
                'start_date.required' => 'Start date is required.',
                'start_date.date' => 'Invalid start date format.',
                'end_date.date' => 'Invalid end date format.',
                'end_date.after_or_equal' => 'End date must be after or equal to start date.',
            ],
        ];
    }
}
