<?php

namespace App\Services;

use App\Models\MessageTemplate;
use Illuminate\Support\Facades\Log;

class WhatsAppTemplates
{
    /**
     * Get a template by name
     *
     * @param  string  $name  The template name
     */
    public function getTemplate(string $name): ?MessageTemplate
    {
        return MessageTemplate::where('name', $name)
            ->where('type', 'whatsapp')
            ->where('is_active', true)
            ->first();
    }

    /**
     * Render a template with parameters
     *
     * @param  string  $templateName  The template name
     * @param  array  $parameters  The parameters to replace in the template
     * @return string|null The rendered template or null if template not found
     */
    public function renderTemplate(string $templateName, array $parameters = []): ?string
    {
        $template = $this->getTemplate($templateName);

        if (! $template) {
            Log::warning("WhatsApp template not found: {$templateName}");

            return null;
        }

        $content = $template->content;

        // Replace parameters in the format {{parameter_name}}
        foreach ($parameters as $key => $value) {
            $content = str_replace("{{$key}}", $value, $content);
        }

        return $content;
    }

    /**
     * Create or update a template
     *
     * @param  string  $name  The template name
     * @param  string  $content  The template content
     * @param  string  $description  The template description
     * @param  bool  $active  Whether the template is active
     */
    public function createOrUpdateTemplate(string $name, string $content, string $description = '', bool $active = true): MessageTemplate
    {
        return MessageTemplate::updateOrCreate(
            ['name' => $name, 'type' => 'whatsapp'],
            [
                'content' => $content,
                'subject' => $description,
                'is_active' => $active,
            ]
        );
    }

    /**
     * Get all templates
     *
     * @param  bool|null  $active  Filter by active status
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllTemplates(?bool $active = null)
    {
        $query = MessageTemplate::where('type', 'whatsapp');

        if ($active !== null) {
            $query->where('is_active', $active);
        }

        return $query->orderBy('name')->get();
    }

    /**
     * Deactivate a template
     *
     * @param  string  $name  The template name
     */
    public function deactivateTemplate(string $name): bool
    {
        $template = $this->getTemplate($name);

        if (! $template) {
            return false;
        }

        $template->is_active = false;

        return $template->save();
    }

    /**
     * Activate a template
     *
     * @param  string  $name  The template name
     */
    public function activateTemplate(string $name): bool
    {
        $template = MessageTemplate::where('name', $name)
            ->where('type', 'whatsapp')
            ->first();

        if (! $template) {
            return false;
        }

        $template->is_active = true;

        return $template->save();
    }

    /**
     * Delete a template
     *
     * @param  string  $name  The template name
     */
    public function deleteTemplate(string $name): bool
    {
        $template = MessageTemplate::where('name', $name)
            ->where('type', 'whatsapp')
            ->first();

        if (! $template) {
            return false;
        }

        return $template->delete();
    }

    /**
     * Initialize default templates
     */
    public function initializeDefaultTemplates(): void
    {
        // Invoice delivery template
        $this->createOrUpdateTemplate(
            'invoice_delivery',
            "Hello {{customer_name}},\n\nYour water bill (Invoice #{{invoice_number}}) for KSh {{amount}} has been generated. Payment is due by {{due_date}}.\n\nThank you for your prompt payment.",
            'Template for delivering new invoices to customers',
            true
        );

        // Payment reminder template
        $this->createOrUpdateTemplate(
            'payment_reminder',
            "Hello {{customer_name}},\n\nThis is a reminder that your water bill (Invoice #{{invoice_number}}) for KSh {{amount}} is overdue by {{days_overdue}} days. Please make payment as soon as possible to avoid service interruption.\n\nThank you.",
            'Template for reminding customers about overdue payments',
            true
        );

        // Payment confirmation template
        $this->createOrUpdateTemplate(
            'payment_confirmation',
            "Hello {{customer_name}},\n\nWe have received your payment of KSh {{amount}} for Invoice #{{invoice_number}}. Thank you for your payment.\n\nYour current balance is KSh {{balance}}.",
            'Template for confirming payments',
            true
        );

        // Meter reading reminder template
        $this->createOrUpdateTemplate(
            'meter_reading_reminder',
            "Hello {{customer_name}},\n\nIt's time to submit your water meter reading for House #{{house_number}}. Please reply with a clear photo of your meter or submit through our app.\n\nThank you.",
            'Template for reminding customers to submit meter readings',
            true
        );

        // Meter reading confirmation template
        $this->createOrUpdateTemplate(
            'meter_reading_confirmation',
            "Hello {{customer_name}},\n\nYour meter reading of {{reading}} for House #{{house_number}} has been received and is being processed. Thank you for your submission.",
            'Template for confirming receipt of meter readings',
            true
        );

        // Invoice submitted for approval template
        $this->createOrUpdateTemplate(
            'invoice_submitted_for_approval',
            "Hello {{reviewer_name}},\n\nA new invoice (Invoice #{{invoice_number}}) for House #{{house_number}} has been submitted for approval by {{manager_name}}.\n\nAmount: KSh {{amount}}\nCustomer: {{customer_name}}\n\nPlease review and approve or reject this invoice at your earliest convenience.",
            'Template for notifying reviewers about invoices submitted for approval',
            true
        );

        // Invoice approved template
        $this->createOrUpdateTemplate(
            'invoice_approved',
            "Hello {{manager_name}},\n\nInvoice #{{invoice_number}} for House #{{house_number}} has been approved by {{reviewer_name}}.\n\nAmount: KSh {{amount}}\nCustomer: {{customer_name}}\n\nThe invoice is now ready to be sent to the customer.",
            'Template for notifying managers about approved invoices',
            true
        );

        // Invoice rejected template
        $this->createOrUpdateTemplate(
            'invoice_rejected',
            "Hello {{manager_name}},\n\nInvoice #{{invoice_number}} for House #{{house_number}} has been rejected by {{reviewer_name}}.\n\nAmount: KSh {{amount}}\nCustomer: {{customer_name}}\n\nReason: {{rejection_reason}}\n\nPlease review the rejection reason and make necessary corrections before resubmitting.",
            'Template for notifying managers about rejected invoices',
            true
        );
    }
}
