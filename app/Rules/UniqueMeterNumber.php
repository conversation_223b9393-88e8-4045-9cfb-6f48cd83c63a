<?php

namespace App\Rules;

use App\Models\House;
use Illuminate\Contracts\Validation\Rule;

class UniqueMeterNumber implements Rule
{
    private $ignoreId;

    public function __construct($ignoreId = null)
    {
        $this->ignoreId = $ignoreId;
    }

    public function passes($attribute, $value)
    {
        $query = House::where('meter_number', $value);

        if ($this->ignoreId) {
            $query->where('id', '!=', $this->ignoreId);
        }

        return ! $query->exists();
    }

    public function message()
    {
        return 'The meter number already exists.';
    }
}
