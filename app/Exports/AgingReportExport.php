<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;

class AgingReportExport implements WithMultipleSheets
{
    protected $agingReport;

    public function __construct(array $agingReport)
    {
        $this->agingReport = $agingReport;
    }

    public function sheets(): array
    {
        return [
            new AgingSummarySheet($this->agingReport),
            new AgingBreakdownSheet($this->agingReport),
        ];
    }
}

class AgingSummarySheet implements FromCollection, WithHeadings, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return collect([[
            'current' => $this->data['current'],
            'days_1_30' => $this->data['days_1_30'],
            'days_31_60' => $this->data['days_31_60'],
            'days_61_90' => $this->data['days_61_90'],
            'days_over_90' => $this->data['days_over_90'],
            'total_outstanding' => $this->data['total_outstanding'],
        ]]);
    }

    public function headings(): array
    {
        return [
            'Current (KES)',
            '1-30 Days (KES)',
            '31-60 Days (KES)',
            '61-90 Days (KES)',
            'Over 90 Days (KES)',
            'Total Outstanding (KES)',
        ];
    }

    public function title(): string
    {
        return 'Aging Summary';
    }
}

class AgingBreakdownSheet implements FromCollection, WithHeadings, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $breakdown = [
            [
                'category' => 'Current',
                'amount' => $this->data['current'],
                'percentage' => $this->data['total_outstanding'] > 0 ? ($this->data['current'] / $this->data['total_outstanding']) * 100 : 0,
            ],
            [
                'category' => '1-30 Days',
                'amount' => $this->data['days_1_30'],
                'percentage' => $this->data['total_outstanding'] > 0 ? ($this->data['days_1_30'] / $this->data['total_outstanding']) * 100 : 0,
            ],
            [
                'category' => '31-60 Days',
                'amount' => $this->data['days_31_60'],
                'percentage' => $this->data['total_outstanding'] > 0 ? ($this->data['days_31_60'] / $this->data['total_outstanding']) * 100 : 0,
            ],
            [
                'category' => '61-90 Days',
                'amount' => $this->data['days_61_90'],
                'percentage' => $this->data['total_outstanding'] > 0 ? ($this->data['days_61_90'] / $this->data['total_outstanding']) * 100 : 0,
            ],
            [
                'category' => 'Over 90 Days',
                'amount' => $this->data['days_over_90'],
                'percentage' => $this->data['total_outstanding'] > 0 ? ($this->data['days_over_90'] / $this->data['total_outstanding']) * 100 : 0,
            ],
        ];

        return collect($breakdown);
    }

    public function headings(): array
    {
        return [
            'Category',
            'Amount (KES)',
            'Percentage (%)',
        ];
    }

    public function map($row): array
    {
        return [
            $row['category'],
            $row['amount'],
            round($row['percentage'], 2),
        ];
    }

    public function title(): string
    {
        return 'Aging Breakdown';
    }
}
