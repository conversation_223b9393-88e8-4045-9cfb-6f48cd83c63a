<?php

declare(strict_types=1);

namespace App\Exports;

use App\Models\AccountTransaction;
use App\Models\House;
use App\Models\Invoice;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CustomerStatementExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected House $house;

    protected Collection $invoices;

    protected Collection $transactions;

    protected float $openingBalance;

    public function __construct(House $house, Collection $invoices, Collection $transactions)
    {
        $this->house = $house;
        $this->invoices = $invoices;
        $this->transactions = $transactions;
        $this->openingBalance = $this->calculateOpeningBalance();
    }

    public function collection(): Collection
    {
        $data = collect();

        // Add header information
        $data->push(['Customer Statement']);
        $data->push(['']);
        $data->push(['House Information']);
        $data->push(['House Number', $this->house->house_number]);
        $data->push(['Estate', $this->house->estate->name]);
        $data->push(['Contact', $this->house->contact?->name ?? 'N/A']);
        $data->push(['Phone', $this->house->contact?->phone ?? 'N/A']);
        $data->push(['']);
        $data->push(['Statement Period', request()->input('startDate').' to '.request()->input('endDate')]);
        $data->push(['']);

        // Add summary
        $data->push(['Account Summary']);
        $data->push(['Opening Balance', number_format($this->openingBalance, 2)]);
        $data->push(['Total Invoices', number_format($this->invoices->sum('total_due'), 2)]);
        $data->push(['Total Payments', number_format($this->transactions->where('transaction_type', 'payment')->sum('amount'), 2)]);
        $data->push(['Total Adjustments', number_format($this->transactions->where('transaction_type', 'adjustment')->sum('amount'), 2)]);
        $data->push(['Closing Balance', number_format($this->calculateClosingBalance(), 2)]);
        $data->push(['']);

        // Add invoices
        $data->push(['Invoices']);
        $data->push(['Date', 'Invoice #', 'Period', 'Amount', 'Status']);

        foreach ($this->invoices as $invoice) {
            $data->push([
                $invoice->created_at->format('Y-m-d'),
                $invoice->invoice_number,
                $invoice->billing_period_start->format('M Y'),
                number_format($invoice->total_due, 2),
                ucfirst($invoice->status),
            ]);
        }
        $data->push(['']);

        // Add transactions
        $data->push(['Transactions']);
        $data->push(['Date', 'Type', 'Description', 'Amount', 'Balance After']);

        foreach ($this->transactions as $transaction) {
            $data->push([
                $transaction->created_at->format('Y-m-d'),
                ucfirst($transaction->transaction_type),
                $transaction->description,
                number_format($transaction->amount, 2),
                number_format($transaction->balance_after, 2),
            ]);
        }

        return $data;
    }

    public function headings(): array
    {
        return [];
    }

    public function map($row): array
    {
        return $row;
    }

    public function styles(Worksheet $sheet): array
    {
        $styles = [];

        // Style headers
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A10')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A17')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A'.(18 + $this->invoices->count() + 2))->getFont()->setBold(true)->setSize(14);

        // Style invoice and transaction headers
        $invoiceHeaderRow = 18;
        $transactionHeaderRow = 18 + $this->invoices->count() + 3;

        if ($this->invoices->count() > 0) {
            $sheet->getStyle("A{$invoiceHeaderRow}:E{$invoiceHeaderRow}")->getFont()->setBold(true);
            $sheet->getStyle("A{$invoiceHeaderRow}:E{$invoiceHeaderRow}")->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setARGB('FFE0E0E0');
        }

        if ($this->transactions->count() > 0) {
            $sheet->getStyle("A{$transactionHeaderRow}:E{$transactionHeaderRow}")->getFont()->setBold(true);
            $sheet->getStyle("A{$transactionHeaderRow}:E{$transactionHeaderRow}")->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setARGB('FFE0E0E0');
        }

        // Auto-size columns
        foreach (range('A', 'E') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        return $styles;
    }

    public function title(): string
    {
        return "Statement - {$this->house->house_number}";
    }

    private function calculateOpeningBalance(): float
    {
        $startDate = request()->input('startDate');
        if (! $startDate) {
            return 0;
        }

        $transactionsBefore = AccountTransaction::where('house_account_id', $this->house->account->id)
            ->whereDate('created_at', '<', $startDate)
            ->sum('amount');

        $invoicesBefore = Invoice::where('house_id', $this->house->id)
            ->whereDate('created_at', '<', $startDate)
            ->sum('total_due');

        return $invoicesBefore - $transactionsBefore;
    }

    private function calculateClosingBalance(): float
    {
        $totalInvoices = $this->invoices->sum('total_due');
        $totalPayments = $this->transactions->where('transaction_type', 'payment')->sum('amount');
        $totalAdjustments = $this->transactions->where('transaction_type', 'adjustment')->sum('amount');

        return $this->openingBalance + $totalInvoices - $totalPayments + $totalAdjustments;
    }
}
