<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;

class RevenueReportExport implements WithMultipleSheets
{
    protected $revenueReport;

    public function __construct(array $revenueReport)
    {
        $this->revenueReport = $revenueReport;
    }

    public function sheets(): array
    {
        return [
            new RevenueSummarySheet($this->revenueReport),
            new RevenueByEstateSheet($this->revenueReport),
            new RevenueByPaymentMethodSheet($this->revenueReport),
            new RevenueByMonthSheet($this->revenueReport),
        ];
    }
}

class RevenueSummarySheet implements FromCollection, WithHeadings, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return collect([[
            'total_revenue' => $this->data['total_revenue'],
            'total_invoices' => $this->data['total_invoices'],
            'average_invoice_amount' => $this->data['average_invoice_amount'],
            'collection_rate' => $this->data['collection_rate'],
            'total_billed' => $this->data['total_billed'],
            'total_collected' => $this->data['total_collected'],
        ]]);
    }

    public function headings(): array
    {
        return [
            'Total Revenue (KES)',
            'Total Invoices',
            'Average Invoice Amount (KES)',
            'Collection Rate (%)',
            'Total Billed (KES)',
            'Total Collected (KES)',
        ];
    }

    public function title(): string
    {
        return 'Revenue Summary';
    }
}

class RevenueByEstateSheet implements FromCollection, WithHeadings, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $collection = collect();
        foreach ($this->data['revenue_by_estate'] as $estate => $revenue) {
            $collection->push([
                'estate' => $estate,
                'revenue' => $revenue,
                'percentage' => $this->data['total_revenue'] > 0 ? ($revenue / $this->data['total_revenue']) * 100 : 0,
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Estate',
            'Revenue (KES)',
            'Percentage (%)',
        ];
    }

    public function map($row): array
    {
        return [
            $row['estate'],
            $row['revenue'],
            round($row['percentage'], 2),
        ];
    }

    public function title(): string
    {
        return 'Revenue by Estate';
    }
}

class RevenueByPaymentMethodSheet implements FromCollection, WithHeadings, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $collection = collect();
        foreach ($this->data['revenue_by_payment_method'] as $method => $revenue) {
            $collection->push([
                'payment_method' => $method,
                'revenue' => $revenue,
                'percentage' => $this->data['total_revenue'] > 0 ? ($revenue / $this->data['total_revenue']) * 100 : 0,
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Payment Method',
            'Revenue (KES)',
            'Percentage (%)',
        ];
    }

    public function map($row): array
    {
        return [
            $row['payment_method'],
            $row['revenue'],
            round($row['percentage'], 2),
        ];
    }

    public function title(): string
    {
        return 'Revenue by Payment Method';
    }
}

class RevenueByMonthSheet implements FromCollection, WithHeadings, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $collection = collect();
        foreach ($this->data['revenue_by_month'] as $month => $revenue) {
            $collection->push([
                'month' => \Carbon\Carbon::parse($month.'-01')->format('M Y'),
                'revenue' => $revenue,
                'percentage' => $this->data['total_revenue'] > 0 ? ($revenue / $this->data['total_revenue']) * 100 : 0,
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Month',
            'Revenue (KES)',
            'Percentage (%)',
        ];
    }

    public function map($row): array
    {
        return [
            $row['month'],
            $row['revenue'],
            round($row['percentage'], 2),
        ];
    }

    public function title(): string
    {
        return 'Revenue by Month';
    }
}
