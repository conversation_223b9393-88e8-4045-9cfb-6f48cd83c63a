<?php

namespace App\Exports;

use App\Services\ReportExportService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;

class ManagementReportExport implements WithMultipleSheets
{
    protected $filters;

    protected $reportService;

    protected $reportData;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
        $this->reportService = new ReportExportService;
        $this->reportData = $this->reportService->generateManagementReport($filters);
    }

    public function sheets(): array
    {
        return [
            new SummarySheet($this->reportData),
            new EstateAnalyticsSheet($this->reportData),
            new ConsumptionTrendsSheet($this->reportData),
            new RevenueAnalysisSheet($this->reportData),
            new TopConsumersSheet($this->reportData),
            new OverdueAccountsSheet($this->reportData),
        ];
    }
}

class SummarySheet implements FromCollection, WithHeadings, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return collect([$this->data['summary']]);
    }

    public function headings(): array
    {
        return [
            'Total Houses',
            'Total Estates',
            'Total Consumption (L)',
            'Total Revenue (KES)',
            'Avg Daily Consumption (L)',
            'Pending Readings',
            'Overdue Invoices',
        ];
    }

    public function title(): string
    {
        return 'Summary';
    }
}

class EstateAnalyticsSheet implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data['estate_analytics'];
    }

    public function headings(): array
    {
        return [
            'Estate',
            'Total Houses',
            'Total Consumption (L)',
            'Total Revenue (KES)',
            'Avg Consumption per House (L)',
            'Revenue per House (KES)',
            'Occupancy Rate (%)',
        ];
    }

    public function map($row): array
    {
        return [
            $row['estate'],
            $row['total_houses'],
            $row['total_consumption'],
            $row['total_revenue'],
            $row['avg_consumption_per_house'],
            $row['revenue_per_house'],
            $row['occupancy_rate'],
        ];
    }

    public function title(): string
    {
        return 'Estate Analytics';
    }
}

class ConsumptionTrendsSheet implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data['consumption_trends'];
    }

    public function headings(): array
    {
        return [
            'Date',
            'Total Consumption (L)',
            'Reading Count',
            'Avg Consumption per Reading (L)',
        ];
    }

    public function map($row): array
    {
        return [
            $row['date'],
            $row['total_consumption'],
            $row['reading_count'],
            $row['avg_consumption'],
        ];
    }

    public function title(): string
    {
        return 'Consumption Trends';
    }
}

class RevenueAnalysisSheet implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data['revenue_analysis']['by_date'];
    }

    public function headings(): array
    {
        return [
            'Date',
            'Total Revenue (KES)',
            'Invoice Count',
        ];
    }

    public function map($row): array
    {
        return [
            $row->date,
            $row->total,
            $row->count,
        ];
    }

    public function title(): string
    {
        return 'Revenue Analysis';
    }
}

class TopConsumersSheet implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data['top_consumers'];
    }

    public function headings(): array
    {
        return [
            'House',
            'Estate',
            'Total Consumption (L)',
            'Reading Count',
            'Avg Daily Consumption (L)',
            'Status',
        ];
    }

    public function map($row): array
    {
        return [
            $row['house'],
            $row['estate'],
            $row['total_consumption'],
            $row['reading_count'],
            $row['avg_daily_consumption'],
            $row['status'],
        ];
    }

    public function title(): string
    {
        return 'Top Consumers';
    }
}

class OverdueAccountsSheet implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data['overdue_accounts'];
    }

    public function headings(): array
    {
        return [
            'House',
            'Estate',
            'Amount (KES)',
            'Due Date',
            'Days Overdue',
            'Contact',
        ];
    }

    public function map($row): array
    {
        return [
            $row['house'],
            $row['estate'],
            $row['amount'],
            $row['due_date'],
            $row['days_overdue'],
            $row['contact'],
        ];
    }

    public function title(): string
    {
        return 'Overdue Accounts';
    }
}
