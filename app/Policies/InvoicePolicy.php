<?php

declare(strict_types=1);

namespace App\Policies;

use App\Enums\UserRole;
use App\Models\Invoice;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class InvoicePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the invoice.
     */
    public function view(User $user, Invoice $invoice): bool
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return true;
        }

        if ($user->hasRole(UserRole::MANAGER) || $user->hasRole(UserRole::REVIEWER)) {
            return $user->canAccessEstate($invoice->house->estate);
        }

        if ($user->hasRole(UserRole::RESIDENT)) {
            return $user->canAccessHouse($invoice->house);
        }

        return false;
    }

    /**
     * Determine whether the user can create invoices.
     */
    public function create(User $user): bool
    {
        return $user->hasRole(UserRole::ADMIN) ||
               $user->hasRole(UserRole::MANAGER) ||
               $user->hasRole(UserRole::CARETAKER);
    }

    /**
     * Determine whether the user can update the invoice.
     */
    public function update(User $user, Invoice $invoice): bool
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return true;
        }

        if ($user->hasRole(UserRole::MANAGER) || $user->hasRole(UserRole::CARETAKER)) {
            return $user->canAccessEstate($invoice->house->estate) &&
                   in_array($invoice->status, ['draft', 'submitted']);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the invoice.
     */
    public function delete(User $user, Invoice $invoice): bool
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return true;
        }

        if ($user->hasRole(UserRole::MANAGER)) {
            return $user->canAccessEstate($invoice->house->estate) &&
                   $invoice->status === 'draft';
        }

        return false;
    }

    /**
     * Determine whether the user can submit the invoice for approval.
     */
    public function submitForApproval(User $user, Invoice $invoice): bool
    {
        return ($user->hasRole(UserRole::ADMIN) || $user->hasRole(UserRole::MANAGER)) &&
               $user->canAccessEstate($invoice->house->estate) &&
               $invoice->status === 'draft';
    }

    /**
     * Determine whether the user can approve the invoice.
     */
    public function approve(User $user, Invoice $invoice): bool
    {
        return ($user->hasRole(UserRole::ADMIN) || $user->hasRole(UserRole::REVIEWER)) &&
               $user->canAccessEstate($invoice->house->estate) &&
               $invoice->status === 'submitted';
    }

    /**
     * Determine whether the user can reject the invoice.
     */
    public function reject(User $user, Invoice $invoice): bool
    {
        return ($user->hasRole(UserRole::ADMIN) || $user->hasRole(UserRole::REVIEWER)) &&
               $user->canAccessEstate($invoice->house->estate) &&
               $invoice->status === 'submitted';
    }

    /**
     * Determine whether the user can send the invoice.
     */
    public function send(User $user, Invoice $invoice): bool
    {
        return ($user->hasRole(UserRole::ADMIN) || $user->hasRole(UserRole::MANAGER)) &&
               $user->canAccessEstate($invoice->house->estate) &&
               $invoice->status === 'approved' &&
               ! is_null($invoice->pdf_path);
    }

    /**
     * Determine whether the user can bulk approve invoices.
     */
    public function bulkApprove(User $user): bool
    {
        return $user->hasRole(UserRole::ADMIN) || $user->hasRole(UserRole::REVIEWER);
    }

    /**
     * Determine whether the user can bulk send invoices.
     */
    public function bulkSend(User $user): bool
    {
        return $user->hasRole(UserRole::ADMIN) || $user->hasRole(UserRole::MANAGER);
    }

    /**
     * Determine whether the user can view invoice statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return $user->hasRole(UserRole::ADMIN) ||
               $user->hasRole(UserRole::MANAGER) ||
               $user->hasRole(UserRole::REVIEWER);
    }
}
