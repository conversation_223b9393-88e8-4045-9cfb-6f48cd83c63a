<?php

namespace App\Http\Middleware;

use App\Models\Estate;
use App\Services\PermissionValidationService;
use Closure;
use Illuminate\Http\Request;

class EstateAccessMiddleware
{
    protected $permissionValidationService;

    public function __construct(PermissionValidationService $permissionValidationService)
    {
        $this->permissionValidationService = $permissionValidationService;
    }

    public function handle(Request $request, Closure $next)
    {
        $estateId = $request->route('estate');

        if ($estateId) {
            $estate = Estate::findOrFail($estateId);
            $user = $request->user();

            if (! $this->permissionValidationService->canAccessEstate($user, $estate)) {
                abort(403, 'You do not have permission to access this estate.');
            }
        }

        return $next($request);
    }
}
