<?php

namespace App\Http\Middleware;

use App\Models\House;
use App\Services\PermissionValidationService;
use Closure;
use Illuminate\Http\Request;

class HouseAccessMiddleware
{
    protected $permissionValidationService;

    public function __construct(PermissionValidationService $permissionValidationService)
    {
        $this->permissionValidationService = $permissionValidationService;
    }

    public function handle(Request $request, Closure $next)
    {
        $houseId = $request->route('house');

        if ($houseId) {
            $house = House::findOrFail($houseId);
            $user = $request->user();

            if (! $this->permissionValidationService->canAccessHouse($user, $house)) {
                abort(403, 'You do not have permission to access this house.');
            }
        }

        return $next($request);
    }
}
