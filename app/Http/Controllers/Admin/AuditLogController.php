<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AuditLogController extends Controller
{
    public function export(Request $request)
    {
        // Get filters from session
        $filters = session('activity_logs_export_filters', []);
        
        // Build query with filters
        $query = ActivityLog::with('user');

        // Apply search filter
        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('description', 'like', '%'.$filters['search'].'%')
                    ->orWhere('action', 'like', '%'.$filters['search'].'%')
                    ->orWhere('entity_type', 'like', '%'.$filters['search'].'%');
            });
        }

        // Apply user filter
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        // Apply action filter
        if (!empty($filters['action'])) {
            $query->where('action', $filters['action']);
        }

        // Apply entity filter
        if (!empty($filters['entity_type'])) {
            $query->where('entity_type', $filters['entity_type']);
        }

        // Apply date filters
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }
        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        // Get all matching logs (limit to prevent memory issues)
        $logs = $query->limit(10000)->get();

        // Generate CSV content
        $filename = 'activity_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
            'Pragma' => 'public',
        ];

        $callback = function() use ($logs) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for proper UTF-8 encoding in Excel
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Add CSV headers
            fputcsv($file, [
                'ID',
                'Timestamp',
                'User',
                'User Role',
                'Action',
                'Entity Type',
                'Entity ID',
                'Description',
                'IP Address',
                'User Agent'
            ]);

            // Add data rows
            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->created_at->format('Y-m-d H:i:s'),
                    $log->user ? $log->user->name : 'System',
                    $log->user ? $log->user->role->label() : 'N/A',
                    $log->action,
                    $log->entity_type ?? '',
                    $log->entity_id ?? '',
                    $log->description,
                    $log->ip_address ?? '',
                    $log->user_agent ?? ''
                ]);
            }

            fclose($file);
        };

        // Clear the session filters
        session()->forget('activity_logs_export_filters');

        return response()->stream($callback, 200, $headers);
    }
}
