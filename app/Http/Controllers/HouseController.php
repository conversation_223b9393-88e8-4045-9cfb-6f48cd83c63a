<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\House;
use App\Models\HouseContact;
use App\Services\HouseSearchService;
use App\Services\ImportExportService;
use App\Services\ValidationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class HouseController extends Controller
{
    public function __construct(
        private HouseSearchService $searchService,
        private ImportExportService $importExportService,
        private ValidationService $validationService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $filters = $request->only([
            'search', 'estate_id', 'occupancy_status', 'house_type',
            'min_bedrooms', 'max_bedrooms', 'min_rent', 'max_rent',
            'has_meter', 'has_contacts', 'contact_count',
        ]);

        $query = $this->searchService->searchHouses($filters);

        $houses = $query->with(['estate', 'contacts'])
            ->orderBy('house_number')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'data' => $houses->items(),
            'pagination' => [
                'current_page' => $houses->currentPage(),
                'per_page' => $houses->perPage(),
                'total' => $houses->total(),
                'last_page' => $houses->lastPage(),
            ],
        ]);
    }

    public function show(int $id): JsonResponse
    {
        $house = House::with(['estate', 'contacts'])->findOrFail($id);

        return response()->json([
            'data' => $house,
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $validated = $this->validationService->validateHouse($request->all());
            $house = House::create($validated);

            DB::commit();

            return response()->json([
                'message' => 'House created successfully',
                'data' => $house->load(['estate', 'contacts']),
            ], 201);

        } catch (ValidationException $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to create house',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $house = House::findOrFail($id);
            $validated = $this->validationService->validateHouse($request->all(), $id);

            $house->update($validated);

            DB::commit();

            return response()->json([
                'message' => 'House updated successfully',
                'data' => $house->fresh()->load(['estate', 'contacts']),
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to update house',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $house = House::findOrFail($id);

            if ($house->contacts()->exists()) {
                return response()->json([
                    'message' => 'Cannot delete house with assigned contacts',
                ], 422);
            }

            $house->delete();

            DB::commit();

            return response()->json([
                'message' => 'House deleted successfully',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to delete house',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getContacts(int $id, Request $request): JsonResponse
    {
        $house = House::findOrFail($id);

        $contacts = $house->contacts()
            ->withPivot(['is_primary', 'relationship', 'start_date', 'end_date'])
            ->orderByPivot('is_primary', 'desc')
            ->orderByPivot('start_date', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'data' => $contacts->items(),
            'pagination' => [
                'current_page' => $contacts->currentPage(),
                'per_page' => $contacts->perPage(),
                'total' => $contacts->total(),
                'last_page' => $contacts->lastPage(),
            ],
        ]);
    }

    public function assignContact(Request $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $house = House::findOrFail($id);

            $validated = $this->validationService->validateHouseContactAssignment(
                array_merge($request->all(), ['house_id' => $id])
            );

            // Check if contact already assigned
            if (! $this->validationService->validateHouseContactUniqueness(
                $id,
                $validated['contact_id']
            )) {
                return response()->json([
                    'message' => 'Contact already assigned to this house',
                ], 422);
            }

            // Check primary contact limit
            if ($validated['is_primary'] &&
                ! $this->validationService->validatePrimaryContactLimit($id)) {
                return response()->json([
                    'message' => 'House already has a primary contact',
                ], 422);
            }

            $houseContact = HouseContact::create($validated);

            DB::commit();

            return response()->json([
                'message' => 'Contact assigned to house successfully',
                'data' => $houseContact->load(['contact', 'house']),
            ], 201);

        } catch (ValidationException $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to assign contact',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function updateContactAssignment(Request $request, int $id, int $contactId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $houseContact = HouseContact::where('house_id', $id)
                ->where('contact_id', $contactId)
                ->firstOrFail();

            $validated = $this->validationService->validateHouseContactAssignment(
                array_merge($request->all(), [
                    'house_id' => $id,
                    'contact_id' => $contactId,
                ])
            );

            // Check primary contact limit if changing to primary
            if ($validated['is_primary'] &&
                ! $this->validationService->validatePrimaryContactLimit($id, $houseContact->id)) {
                return response()->json([
                    'message' => 'House already has a primary contact',
                ], 422);
            }

            $houseContact->update($validated);

            DB::commit();

            return response()->json([
                'message' => 'Contact assignment updated successfully',
                'data' => $houseContact->load(['contact', 'house']),
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to update contact assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function removeContact(int $id, int $contactId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $houseContact = HouseContact::where('house_id', $id)
                ->where('contact_id', $contactId)
                ->firstOrFail();

            $houseContact->delete();

            DB::commit();

            return response()->json([
                'message' => 'Contact removed from house successfully',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to remove contact',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function export(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'search', 'estate_id', 'occupancy_status', 'house_type',
                'min_bedrooms', 'max_bedrooms', 'min_rent', 'max_rent',
                'has_meter', 'has_contacts', 'contact_count',
            ]);

            $exportData = $this->importExportService->exportHouses($filters);

            return response()->json([
                'data' => $exportData['data'],
                'headers' => $exportData['headers'],
                'filename' => $exportData['filename'],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to export houses',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function import(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'file' => 'required|file|mimes:csv,txt|max:2048',
            ]);

            $file = $request->file('file');
            $data = array_map('str_getcsv', file($file->getRealPath()));

            if (empty($data)) {
                return response()->json([
                    'message' => 'File is empty',
                ], 422);
            }

            $headers = array_shift($data);
            $rows = [];

            foreach ($data as $row) {
                $rows[] = array_combine($headers, $row);
            }

            $results = $this->importExportService->importHouses($rows);

            return response()->json([
                'message' => 'Import completed',
                'results' => $results,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to import houses',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getOptions(Request $request): JsonResponse
    {
        $estateId = $request->get('estate_id');

        $query = House::select('id', 'house_number', 'estate_id')
            ->with('estate:id,name');

        if ($estateId) {
            $query->where('estate_id', $estateId);
        }

        $houses = $query->orderBy('house_number')->get();

        return response()->json([
            'data' => $houses,
        ]);
    }
}
