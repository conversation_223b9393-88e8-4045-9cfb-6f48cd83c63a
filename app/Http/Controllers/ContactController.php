<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\House;
use App\Models\HouseContact;
use App\Services\ImportExportService;
use App\Services\ValidationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    public function __construct(
        private ValidationService $validationService,
        private ImportExportService $importExportService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $query = Contact::query();

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }

        // Filter by estate
        if ($request->has('estate_id')) {
            $query->whereHas('houses', function ($q) use ($request) {
                $q->where('estate_id', $request->get('estate_id'));
            });
        }

        // Filter by house
        if ($request->has('house_id')) {
            $query->whereHas('houses', function ($q) use ($request) {
                $q->where('house_id', $request->get('house_id'));
            });
        }

        // Include relationships
        $query->with(['houses.estate']);

        $contacts = $query->orderBy('name')->paginate($request->get('per_page', 15));

        return response()->json($contacts);
    }

    public function show(Contact $contact): JsonResponse
    {
        $contact->load(['houses.estate']);

        return response()->json($contact);
    }

    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:contacts,email',
            'phone' => 'nullable|string|max:20',
            'type' => 'required|in:owner,tenant,agent,emergency',
            'id_number' => 'nullable|string|max:50|unique:contacts,id_number',
            'address' => 'nullable|string',
            'notes' => 'nullable|string',
            'house_ids' => 'nullable|array',
            'house_ids.*' => 'exists:houses,id',
            'is_primary' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        DB::beginTransaction();
        try {
            $contact = Contact::create($validator->validated());

            // Attach to houses if provided
            if ($request->has('house_ids')) {
                foreach ($request->get('house_ids') as $houseId) {
                    HouseContact::create([
                        'house_id' => $houseId,
                        'contact_id' => $contact->id,
                        'type' => $request->get('type'),
                        'is_primary' => $request->get('is_primary', false),
                    ]);
                }
            }

            DB::commit();

            return response()->json($contact->load('houses'), 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['error' => 'Failed to create contact'], 500);
        }
    }

    public function update(Request $request, Contact $contact): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'nullable|email|unique:contacts,email,'.$contact->id,
            'phone' => 'nullable|string|max:20',
            'type' => 'sometimes|required|in:owner,tenant,agent,emergency',
            'id_number' => 'nullable|string|max:50|unique:contacts,id_number,'.$contact->id,
            'address' => 'nullable|string',
            'notes' => 'nullable|string',
            'house_ids' => 'nullable|array',
            'house_ids.*' => 'exists:houses,id',
            'is_primary' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        DB::beginTransaction();
        try {
            $contact->update($validator->validated());

            // Update house relationships if provided
            if ($request->has('house_ids')) {
                // Remove existing relationships
                HouseContact::where('contact_id', $contact->id)->delete();

                // Add new relationships
                foreach ($request->get('house_ids') as $houseId) {
                    HouseContact::create([
                        'house_id' => $houseId,
                        'contact_id' => $contact->id,
                        'type' => $request->get('type', $contact->type),
                        'is_primary' => $request->get('is_primary', false),
                    ]);
                }
            }

            DB::commit();

            return response()->json($contact->load('houses'));
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['error' => 'Failed to update contact'], 500);
        }
    }

    public function destroy(Contact $contact): JsonResponse
    {
        DB::beginTransaction();
        try {
            // Remove house relationships
            HouseContact::where('contact_id', $contact->id)->delete();

            $contact->delete();

            DB::commit();

            return response()->json(['message' => 'Contact deleted successfully']);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['error' => 'Failed to delete contact'], 500);
        }
    }

    public function attachToHouse(Request $request, Contact $contact): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'house_id' => 'required|exists:houses,id',
            'type' => 'required|in:owner,tenant,agent,emergency',
            'is_primary' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $houseContact = HouseContact::create([
            'house_id' => $request->get('house_id'),
            'contact_id' => $contact->id,
            'type' => $request->get('type'),
            'is_primary' => $request->get('is_primary', false),
        ]);

        return response()->json($houseContact, 201);
    }

    public function detachFromHouse(Request $request, Contact $contact): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'house_id' => 'required|exists:houses,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        HouseContact::where('contact_id', $contact->id)
            ->where('house_id', $request->get('house_id'))
            ->delete();

        return response()->json(['message' => 'Contact detached from house successfully']);
    }

    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');
        $contacts = Contact::with(['houses.estate'])->get();

        return $this->importExportService->exportContacts($contacts, $format);
    }

    public function import(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,xlsx,xls',
            'update_existing' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->fails()], 422);
        }

        try {
            $result = $this->importExportService->importContacts(
                $request->file('file'),
                $request->get('update_existing', false)
            );

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 422);
        }
    }

    public function downloadTemplate(Request $request)
    {
        return $this->importExportService->downloadContactTemplate();
    }

    public function getByHouse(House $house): JsonResponse
    {
        $contacts = $house->contacts()->withPivot(['type', 'is_primary'])->get();

        return response()->json($contacts);
    }

    public function getByType(Request $request): JsonResponse
    {
        $type = $request->get('type');
        $contacts = Contact::where('type', $type)
            ->with(['houses.estate'])
            ->orderBy('name')
            ->get();

        return response()->json($contacts);
    }
}
