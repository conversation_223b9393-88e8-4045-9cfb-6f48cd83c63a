<?php

namespace App\Http\Controllers;

use App\Models\Estate;
use App\Services\HouseSearchService;
use App\Services\ImportExportService;
use App\Services\ValidationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class EstateController extends Controller
{
    public function __construct(
        private HouseSearchService $searchService,
        private ImportExportService $importExportService,
        private ValidationService $validationService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $filters = $request->only([
            'search', 'status', 'manager_name', 'location', 'has_vacant_houses',
        ]);

        $query = $this->searchService->searchEstates($filters);

        $estates = $query->withCount('houses')
            ->orderBy('name')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'data' => $estates->items(),
            'pagination' => [
                'current_page' => $estates->currentPage(),
                'per_page' => $estates->perPage(),
                'total' => $estates->total(),
                'last_page' => $estates->lastPage(),
            ],
        ]);
    }

    public function show(int $id): JsonResponse
    {
        $estate = Estate::withCount([
            'houses',
            'houses as occupied_houses_count' => function ($query) {
                $query->where('occupancy_status', 'occupied');
            },
            'houses as vacant_houses_count' => function ($query) {
                $query->where('occupancy_status', 'vacant');
            },
            'houses as maintenance_houses_count' => function ($query) {
                $query->where('occupancy_status', 'maintenance');
            },
        ])->findOrFail($id);

        $statistics = $this->searchService->getEstateStatistics($id);

        return response()->json([
            'data' => $estate,
            'statistics' => $statistics,
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $validated = $this->validationService->validateEstate($request->all());
            $estate = Estate::create($validated);

            DB::commit();

            return response()->json([
                'message' => 'Estate created successfully',
                'data' => $estate,
            ], 201);

        } catch (ValidationException $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to create estate',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $estate = Estate::findOrFail($id);
            $validated = $this->validationService->validateEstate($request->all(), $id);

            $estate->update($validated);

            DB::commit();

            return response()->json([
                'message' => 'Estate updated successfully',
                'data' => $estate->fresh(),
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to update estate',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $estate = Estate::findOrFail($id);

            if ($estate->houses()->exists()) {
                return response()->json([
                    'message' => 'Cannot delete estate with existing houses',
                ], 422);
            }

            $estate->delete();

            DB::commit();

            return response()->json([
                'message' => 'Estate deleted successfully',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to delete estate',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getStatistics(int $id): JsonResponse
    {
        $statistics = $this->searchService->getEstateStatistics($id);

        return response()->json([
            'data' => $statistics,
        ]);
    }

    public function getHouses(int $id, Request $request): JsonResponse
    {
        $estate = Estate::findOrFail($id);

        $filters = array_merge(
            $request->only(['search', 'occupancy_status', 'house_type']),
            ['estate_id' => $id]
        );

        $query = $this->searchService->searchHouses($filters);

        $houses = $query->with(['contacts'])
            ->orderBy('house_number')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'data' => $houses->items(),
            'pagination' => [
                'current_page' => $houses->currentPage(),
                'per_page' => $houses->perPage(),
                'total' => $houses->total(),
                'last_page' => $houses->lastPage(),
            ],
        ]);
    }

    public function export(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'search', 'status', 'manager_name', 'location', 'has_vacant_houses',
            ]);

            $exportData = $this->importExportService->exportEstates($filters);

            return response()->json([
                'data' => $exportData['data'],
                'headers' => $exportData['headers'],
                'filename' => $exportData['filename'],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to export estates',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function import(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'file' => 'required|file|mimes:csv,txt|max:2048',
            ]);

            $file = $request->file('file');
            $data = array_map('str_getcsv', file($file->getRealPath()));

            if (empty($data)) {
                return response()->json([
                    'message' => 'File is empty',
                ], 422);
            }

            $headers = array_shift($data);
            $rows = [];

            foreach ($data as $row) {
                $rows[] = array_combine($headers, $row);
            }

            $results = $this->importExportService->importEstates($rows);

            return response()->json([
                'message' => 'Import completed',
                'results' => $results,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to import estates',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getOptions(): JsonResponse
    {
        $estates = Estate::select('id', 'name', 'code')
            ->where('status', 'active')
            ->orderBy('name')
            ->get();

        return response()->json([
            'data' => $estates,
        ]);
    }
}
