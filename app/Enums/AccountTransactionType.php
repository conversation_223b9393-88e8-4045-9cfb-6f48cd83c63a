<?php

declare(strict_types=1);

namespace App\Enums;

enum AccountTransactionType: string
{
    case CREDIT = 'credit';
    case DEBIT = 'debit';

    public function label(): string
    {
        return match ($this) {
            self::CREDIT => 'Credit',
            self::DEBIT => 'Debit',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::CREDIT => 'Payment received or credit added to account',
            self::DEBIT => 'Invoice charged or debit deducted from account',
        };
    }

    public function isPositive(): bool
    {
        return $this === self::CREDIT;
    }

    public function isNegative(): bool
    {
        return $this === self::DEBIT;
    }

    public function getMultiplier(): int
    {
        return match ($this) {
            self::CREDIT => 1,
            self::DEBIT => -1,
        };
    }
}
