<?php

namespace App\Enums;

enum UserRole: string
{
    case ADMIN = 'admin';
    case MANAGER = 'manager';
    case REVIEWER = 'reviewer';
    case CARETAKER = 'caretaker';
    case RESIDENT = 'resident';

    public function label(): string
    {
        return match ($this) {
            self::ADMIN => 'System Administrator',
            self::MANAGER => 'Estate Manager',
            self::REVIEWER => 'Reviewer/Accountant',
            self::CARETAKER => 'Caretaker Staff',
            self::RESIDENT => 'Resident/Tenant',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::ADMIN => 'System-wide administrator with full access to all features and settings',
            self::MANAGER => 'Estate manager with oversight of assigned estates and team management',
            self::REVIEWER => 'Accountant who reviews readings and manages billing for assigned estates',
            self::CARETAKER => 'Field staff who enters meter readings and updates contact information',
            self::RESIDENT => 'Tenant or house owner with access to personal billing and usage information',
        };
    }

    public function permissions(): array
    {
        return match ($this) {
            self::ADMIN => $this->getAdminPermissions(),
            self::MANAGER => $this->getManagerPermissions(),
            self::REVIEWER => $this->getReviewerPermissions(),
            self::CARETAKER => $this->getCaretakerPermissions(),
            self::RESIDENT => $this->getResidentPermissions(),
        };
    }

    private function getAdminPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-admin-dashboard', 'view-manager-dashboard', 'view-reviewer-dashboard',
            'view-caretaker-dashboard', 'view-resident-dashboard',

            // Estate permissions
            'estates.view_all', 'estates.manage_all', 'estates.create', 'estates.edit_all', 'estates.delete',

            // House permissions
            'houses.view_all', 'houses.manage_all', 'houses.create', 'houses.edit_all', 'houses.delete',

            // Contact permissions
            'contacts.view_all', 'contacts.manage_all', 'contacts.create', 'contacts.delete',

            // Reading permissions
            'readings.view_all', 'readings.create_all', 'readings.edit_all', 'readings.delete',
            'readings.review_all', 'readings.approve_all', 'readings.validate',

            // Invoice permissions
            'invoices.view_all', 'invoices.generate_all', 'invoices.create_manual', 'invoices.edit_all',
            'invoices.delete', 'invoices.send_all', 'invoices.adjust_all', 'invoices.export_all',
            'invoices.approve_all',

            // Account permissions
            'accounts.view_all', 'accounts.manage_all', 'accounts.view_balance_all',
            'accounts.view_transactions_all', 'accounts.view_statement_all', 'accounts.export_statement_all',

            // Payment permissions
            'payments.view_all', 'payments.approve_all', 'payments.export_all',

            // Rate permissions
            'rates.view_all', 'rates.manage_all', 'rates.create', 'rates.edit_all', 'rates.delete',

            // Report permissions
            'reports.view_all', 'reports.generate_all', 'analytics.view_all', 'export.data_all',
            'reports.aging_all', 'reports.revenue_all', 'reports.billing_all',
            'reports.customer_statements_all', 'reports.financial_all',

            // User permissions
            'users.view_all', 'users.create_all', 'users.edit_all', 'users.delete_all',
            'users.assign_estates', 'users.assign_roles', 'users.manage_all',

            // System permissions
            'system.settings.view', 'system.settings.manage', 'audit.logs.view', 'audit.logs.export',
            'whatsapp.settings', 'whatsapp.send_all', 'whatsapp.logs.view',

            // Resident permissions
            'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
            'resident.messages.view', 'resident.messages.send',
        ];
    }

    private function getManagerPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-manager-dashboard', 'view-reviewer-dashboard', 'view-caretaker-dashboard', 'view-resident-dashboard',

            // Estate permissions
            'estates.view_assigned', 'estates.manage_assigned', 'estates.edit_assigned', 'estates.analytics',

            // House permissions
            'houses.view_assigned', 'houses.manage_assigned', 'houses.edit_assigned',

            // Contact permissions
            'contacts.view_assigned', 'contacts.manage_assigned',

            // Reading permissions
            'readings.view_assigned', 'readings.review_assigned', 'readings.validate',

            // Invoice permissions
            'invoices.view_assigned', 'invoices.adjust_assigned', 'invoices.export_assigned',
            'invoices.approve_assigned', 'invoices.send_assigned', 'invoices.generate_assigned',

            // Account permissions
            'accounts.view_assigned', 'accounts.view_balance_assigned', 'accounts.view_transactions_assigned',
            'accounts.view_statement_assigned', 'accounts.export_statement_assigned',

            // Rate permissions
            'rates.view_assigned',

            // Report permissions
            'reports.view_assigned', 'analytics.view_assigned', 'export.data_assigned',
            'reports.generate_assigned', 'reports.aging_assigned', 'reports.revenue_assigned',
            'reports.billing_assigned', 'reports.customer_statements_assigned',

            // Payment permissions
            'payments.view_assigned', 'payments.approve_assigned', 'payments.export_assigned',

            // User permissions
            'users.view_assigned', 'users.create_assigned', 'users.edit_assigned', 'users.assign_estates',

            // WhatsApp permissions
            'whatsapp.send_assigned', 'whatsapp.logs.view',

            // Resident permissions
            'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
            'resident.messages.view', 'resident.messages.send',
        ];
    }

    private function getReviewerPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-reviewer-dashboard', 'view-resident-dashboard',

            // Estate permissions
            'estates.view_assigned',

            // House permissions
            'houses.view_assigned', 'houses.edit_assigned',

            // Contact permissions
            'contacts.view_assigned', 'contacts.edit_assigned',

            // Reading permissions
            'readings.view_assigned', 'readings.approve_assigned', 'readings.validate',
            'readings.edit_assigned', 'readings.create_assigned',

            // Invoice permissions
            'invoices.view_assigned', 'invoices.generate_assigned', 'invoices.edit_assigned',
            'invoices.send_assigned', 'invoices.export_assigned', 'invoices.approve_assigned',
            'invoices.adjust_assigned', 'invoices.create_manual', 'invoices.delete_assigned',

            // Account permissions
            'accounts.view_assigned', 'accounts.manage_assigned', 'accounts.view_balance_assigned',
            'accounts.view_transactions_assigned', 'accounts.create_transaction_assigned',
            'accounts.edit_transaction_assigned', 'accounts.view_statement_assigned',
            'accounts.export_statement_assigned', 'accounts.adjust_balance_assigned',

            // Rate permissions
            'rates.view_assigned', 'rates.edit_assigned',

            // Report permissions
            'reports.view_assigned', 'export.data_assigned', 'reports.generate_assigned',
            'reports.aging_assigned', 'reports.revenue_assigned', 'reports.billing_assigned',
            'reports.customer_statements_assigned', 'reports.financial_assigned',

            // Payment permissions
            'payments.view_assigned', 'payments.approve_assigned', 'payments.create_assigned',
            'payments.edit_assigned', 'payments.export_assigned', 'payments.reconcile_assigned',

            // WhatsApp permissions
            'whatsapp.send_assigned', 'whatsapp.logs.view', 'whatsapp.send_invoices_assigned',

            // Resident permissions
            'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
            'resident.messages.view', 'resident.messages.send',
        ];
    }

    private function getCaretakerPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-caretaker-dashboard',

            // Estate permissions
            'estates.view_assigned',

            // House permissions
            'houses.view_assigned', 'houses.edit_assigned',

            // Contact permissions
            'contacts.view_assigned', 'contacts.manage_assigned', 'contacts.create_assigned',
            'contacts.edit_assigned',

            // Reading permissions
            'readings.view_assigned', 'readings.create_assigned', 'readings.edit_assigned', 'readings.validate',

            // Account permissions (balance viewing only)
            'accounts.view_balance_assigned', 'accounts.view_balance_list_assigned',

            // Invoice permissions (view only)
            'invoices.view_assigned', 'invoices.view_status_assigned',

            // Basic report permissions
            'reports.view_assigned', 'reports.balance_list_assigned',

            // Resident permissions
            'resident.portal.access', 'resident.inquiries.view_assigned',
        ];
    }

    private function getResidentPermissions(): array
    {
        return [
            // Dashboard permissions
            'view-resident-dashboard',

            // Personal data permissions
            'houses.view_own', 'contacts.view_own', 'readings.view_own', 'invoices.view_own',

            // Account access permissions
            'accounts.view_own', 'accounts.view_balance_own', 'accounts.view_transactions_own',
            'accounts.view_statement_own', 'accounts.export_statement_own',

            // Invoice permissions
            'invoices.view_own', 'invoices.download_own', 'invoices.view_payments_own',
            'invoices.view_adjustments_own',

            // Report permissions
            'reports.view_own', 'analytics.view_own', 'export.data_own',

            // Payment permissions
            'payments.view_own', 'payments.view_history_own',

            // Resident permissions
            'resident.portal.access', 'resident.inquiries.create', 'resident.messages.view',
            'resident.payments.create', 'resident.invoices.download',
        ];
    }
}
