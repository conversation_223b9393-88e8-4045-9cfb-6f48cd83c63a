<?php

declare(strict_types=1);

namespace App\Enums;

enum AccountTransactionStatus: string
{
    case PENDING = 'pending';
    case PROCESSED = 'processed';
    case FAILED = 'failed';

    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::PROCESSED => 'Processed',
            self::FAILED => 'Failed',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::PENDING => 'Transaction is awaiting processing',
            self::PROCESSED => 'Transaction has been successfully processed',
            self::FAILED => 'Transaction processing failed',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'yellow',
            self::PROCESSED => 'green',
            self::FAILED => 'red',
        };
    }

    public function isFinal(): bool
    {
        return in_array($this, [self::PROCESSED, self::FAILED]);
    }

    public function canBeProcessed(): bool
    {
        return $this === self::PENDING;
    }

    public function canBeRetried(): bool
    {
        return $this === self::FAILED;
    }
}
