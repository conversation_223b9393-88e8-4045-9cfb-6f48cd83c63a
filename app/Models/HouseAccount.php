<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class HouseAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'house_id',
        'current_balance',
        'total_credit',
        'total_debit',
        'last_transaction_date',
    ];

    protected $casts = [
        'current_balance' => 'decimal:2',
        'total_credit' => 'decimal:2',
        'total_debit' => 'decimal:2',
        'last_transaction_date' => 'datetime',
    ];

    public function house(): BelongsTo
    {
        return $this->belongsTo(House::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(AccountTransaction::class);
    }

    public function getCurrentBalanceAttribute()
    {
        return $this->attributes['current_balance'] ?? 0;
    }

    public function getFormattedBalanceAttribute(): string
    {
        return number_format($this->current_balance, 2);
    }

    public function getBalanceStatusAttribute(): string
    {
        if ($this->current_balance > 0) {
            return 'credit';
        } elseif ($this->current_balance < 0) {
            return 'debit';
        }

        return 'balanced';
    }

    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function activate(): void
    {
        $this->is_active = true;
        $this->save();
    }

    public function deactivate(): void
    {
        $this->is_active = false;
        $this->save();
    }

    public function updateBalance(float $amount): void
    {
        $this->attributes['current_balance'] += $amount;

        if ($amount > 0) {
            $this->attributes['total_credit'] += $amount;
        } else {
            $this->attributes['total_debit'] += abs($amount);
        }

        $this->attributes['last_transaction_date'] = now();
        $this->save();
    }

    public function hasSufficientBalance(float $amount): bool
    {
        return $this->current_balance >= $amount;
    }

    public function getOutstandingBalance(): float
    {
        return max(0, -$this->current_balance);
    }

    public function getCreditBalance(): float
    {
        return max(0, $this->current_balance);
    }

    public function updateLastTransactionDate($date): void
    {
        $this->attributes['last_transaction_date'] = $date;
        $this->save();
    }
}
