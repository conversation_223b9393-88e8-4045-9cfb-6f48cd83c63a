<?php

namespace App\Models;

use App\Models\Traits\EstateScopes;
use App\Services\WhatsAppService;
use App\Services\WhatsAppTemplates;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;

/**
 * @mixin IdeHelperInvoice
 *
 * @property int $id
 * @property string $invoice_number
 * @property int $house_id
 * @property int $meter_reading_id
 * @property int $water_rate_id
 * @property \Illuminate\Support\Carbon $billing_period_start
 * @property \Illuminate\Support\Carbon $billing_period_end
 * @property numeric $previous_reading
 * @property numeric $current_reading
 * @property numeric $consumption
 * @property numeric $rate_per_unit
 * @property numeric $amount
 * @property numeric $fixed_charge
 * @property numeric $total_amount
 * @property \Illuminate\Support\Carbon $due_date
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $sent_at
 * @property \Illuminate\Support\Carbon|null $paid_at
 * @property string|null $payment_reference
 * @property string|null $notes
 * @property string|null $pdf_path
 * @property string|null $public_link
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $previous_balance
 * @property string $late_fee
 * @property string $tax_amount
 * @property string $total_due
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InvoiceAdjustment> $adjustments
 * @property-read int|null $adjustments_count
 * @property-read mixed $days_overdue
 * @property-read mixed $public_url
 * @property-read mixed $status_color
 * @property-read mixed $status_label
 * @property-read mixed $total_amount_with_currency
 * @property-read \App\Models\House $house
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InvoiceLineItem> $lineItems
 * @property-read int|null $line_items_count
 * @property-read \App\Models\MeterReading $meterReading
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InvoicePayment> $payments
 * @property-read int|null $payments_count
 * @property-read \App\Models\WaterRate $waterRate
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WhatsAppMessage> $whatsappMessages
 * @property-read int|null $whatsapp_messages_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice byHouse($houseId)
 * @method static \Database\Factories\InvoiceFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice forPeriod($startDate, $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice overdue()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice paid()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereBillingPeriodEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereBillingPeriodStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereConsumption($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereCurrentReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereDueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereFixedCharge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereInvoiceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereLateFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereMeterReadingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePaymentReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePdfPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePreviousBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePreviousReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePublicLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereRatePerUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereSentAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereTaxAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereTotalAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereTotalDue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereWaterRateId($value)
 *
 * @mixin \Eloquent
 */
class Invoice extends Model
{
    use EstateScopes, HasFactory;

    protected $fillable = [
        'invoice_number',
        'house_id',
        'meter_reading_id',
        'water_rate_id',
        'billing_period_start',
        'billing_period_end',
        'previous_reading',
        'current_reading',
        'consumption',
        'rate_per_unit',
        'amount',
        'fixed_charge',
        'total_amount',
        'due_date',
        'status',
        'sent_at',
        'paid_at',
        'payment_reference',
        'notes',
        'pdf_path',
        'public_link',
        'submitted_by',
        'approved_by',
        'submitted_at',
        'approved_at',
        'reminder_count',
        'last_reminder_at',
        'disconnection_scheduled',
        'previous_balance_brought_forward',
    ];

    protected $casts = [
        'billing_period_start' => 'date',
        'billing_period_end' => 'date',
        'due_date' => 'date',
        'sent_at' => 'datetime',
        'paid_at' => 'datetime',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime',
        'last_reminder_at' => 'datetime',
        'disconnection_scheduled' => 'date',
        'previous_reading' => 'decimal:2',
        'current_reading' => 'decimal:2',
        'consumption' => 'decimal:2',
        'rate_per_unit' => 'decimal:4',
        'amount' => 'decimal:2',
        'fixed_charge' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'previous_balance_brought_forward' => 'decimal:2',
    ];

    public function house(): BelongsTo
    {
        return $this->belongsTo(House::class);
    }

    public function meterReading(): BelongsTo
    {
        return $this->belongsTo(MeterReading::class);
    }

    public function waterRate(): BelongsTo
    {
        return $this->belongsTo(WaterRate::class);
    }

    public function whatsappMessages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class);
    }

    public function lineItems(): HasMany
    {
        return $this->hasMany(InvoiceLineItem::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(InvoicePayment::class);
    }

    public function adjustments(): HasMany
    {
        return $this->hasMany(InvoiceAdjustment::class);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['draft', 'sent']);
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'sent')
            ->where('due_date', '<', now());
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('billing_period_start', [$startDate, $endDate]);
    }

    public function scopeByHouse($query, $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public function generateInvoiceNumber()
    {
        $year = now()->format('Y');
        $month = now()->format('m');
        $sequence = static::whereYear('created_at', $year)->count() + 1;

        return "W{$year}{$month}".str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function markAsSent()
    {
        if (! $this->canBeSent()) {
            throw new \InvalidArgumentException('Invoice must be approved before sending');
        }

        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    public function markAsPaid($paymentReference = null)
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'payment_reference' => $paymentReference,
        ]);
    }

    public function markAsOverdue()
    {
        $this->update(['status' => 'overdue']);
    }

    public function generatePublicLink()
    {
        $token = bin2hex(random_bytes(32));
        $this->update(['public_link' => $token]);

        return $token;
    }

    public function getPublicUrlAttribute()
    {
        return $this->public_link
            ? url("/invoices/{$this->public_link}")
            : null;
    }

    public function getDaysOverdueAttribute()
    {
        if ($this->status === 'sent' && $this->due_date < now()) {
            return $this->due_date->diffInDays(now());
        }

        return 0;
    }

    public function getStatusColorAttribute()
    {
        return match ($this->status) {
            'draft' => 'gray',
            'submitted' => 'yellow',
            'approved' => 'blue',
            'sent' => 'blue',
            'paid' => 'green',
            'overdue' => 'red',
            'cancelled' => 'gray',
            default => 'gray',
        };
    }

    public function getStatusLabelAttribute()
    {
        return ucfirst($this->status);
    }

    public function canBeSent()
    {
        return $this->status === 'approved' && $this->pdf_path !== null;
    }

    public function generatePdf()
    {
        $pdfService = app(\App\Services\PdfGenerationService::class);

        return $pdfService->generateInvoicePdf($this);
    }

    public function regeneratePdf()
    {
        $pdfService = app(\App\Services\PdfGenerationService::class);

        return $pdfService->regenerateInvoicePdf($this);
    }

    public function getTotalAmountWithCurrencyAttribute()
    {
        return 'KES '.number_format($this->total_amount, 2);
    }

    public function getSubtotalAttribute()
    {
        return $this->amount + $this->fixed_charge;
    }

    public function getTotalPaidAttribute()
    {
        return $this->payments()->sum('amount');
    }

    public function getTotalAdjustmentsAttribute()
    {
        return $this->adjustments()->sum('amount');
    }

    public function getBalanceDueAttribute()
    {
        return $this->total_amount + $this->getTotalAdjustmentsAttribute() - $this->getTotalPaidAttribute();
    }

    public function getIsFullyPaidAttribute()
    {
        return $this->getBalanceDueAttribute() <= 0;
    }

    public function getIsOverdueAttribute()
    {
        return $this->status === 'sent' && $this->due_date < now() && ! $this->getIsFullyPaidAttribute();
    }

    public function recordPayment($amount, $paymentMethod, $paymentDate = null, $reference = null, $notes = null)
    {
        $payment = InvoicePayment::create([
            'invoice_id' => $this->id,
            'amount' => $amount,
            'payment_date' => $paymentDate ?? now(),
            'payment_method' => $paymentMethod,
            'reference_number' => $reference ?? InvoicePayment::generateReferenceNumber(),
            'notes' => $notes,
            'user_id' => auth()->id(),
        ]);

        // Check if invoice is fully paid
        if ($this->getBalanceDueAttribute() <= 0) {
            $this->markAsPaid($payment->reference_number);
        }

        return $payment;
    }

    public function addAdjustment($amount, $type, $description, $adjustmentDate = null, $reason = null)
    {
        $adjustmentAmount = $amount;
        if ($type === 'credit' || $type === 'refund') {
            $adjustmentAmount = -$amount;
        }

        return InvoiceAdjustment::create([
            'invoice_id' => $this->id,
            'amount' => $adjustmentAmount,
            'type' => $type,
            'description' => $description,
            'reason' => $reason,
            'adjustment_date' => $adjustmentDate ?? now(),
            'reference_number' => InvoiceAdjustment::generateReferenceNumber(),
            'user_id' => auth()->id(),
        ]);
    }

    /**
     * Approval workflow methods
     */

    /**
     * Submit invoice for approval
     */
    public function submitForApproval(User $user): void
    {
        if (! $this->canBeSubmitted()) {
            throw new \InvalidArgumentException('Invoice must be in draft status to be submitted');
        }

        $this->update([
            'status' => 'submitted',
            'submitted_by' => $user->id,
            'submitted_at' => now(),
        ]);

        // Send notifications to reviewers
        $this->notifyReviewersOfSubmission();
    }

    /**
     * Approve invoice
     */
    public function approve(User $user): void
    {
        if (! $this->canBeApproved()) {
            throw new \InvalidArgumentException('Invoice must be submitted before approval');
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);

        // Send notification to manager
        $this->notifyManagerOfApproval();
    }

    /**
     * Reject invoice
     */
    public function reject(User $user, string $rejectionReason): void
    {
        if (! $this->canBeApproved()) {
            throw new \InvalidArgumentException('Invoice must be submitted before rejection');
        }

        $this->update([
            'status' => 'draft', // Return to draft for corrections
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);

        // Send notification to manager with rejection reason
        $this->notifyManagerOfRejection($rejectionReason);
    }

    /**
     * Record reminder sent
     */
    public function recordReminder(): void
    {
        $this->increment('reminder_count');
        $this->update(['last_reminder_at' => now()]);
    }

    /**
     * Schedule disconnection
     */
    public function scheduleDisconnection(\DateTime $date): void
    {
        $this->update(['disconnection_scheduled' => $date]);
    }

    /**
     * Check if invoice can be submitted
     */
    public function canBeSubmitted(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Check if invoice can be approved
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'submitted';
    }

    /**
     * Relationships for approval workflow
     */

    /**
     * Get the user who submitted the invoice
     */
    public function submittedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitted_by');
    }

    /**
     * Get the user who approved the invoice
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Notification Methods for Approval Workflow
     */

    /**
     * Send notification to reviewers when invoice is submitted for approval
     */
    public function notifyReviewersOfSubmission(): void
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            $whatsAppTemplates = app(WhatsAppTemplates::class);

            // Get all reviewers assigned to this estate with their contacts eager loaded
            $reviewers = User::with(['contacts' => function($query) {
                $query->where('receive_notifications', true)
                      ->whereNotNull('whatsapp_number')
                      ->where('whatsapp_number', '!=', '');
            }])->where('role', \App\Enums\UserRole::REVIEWER)
                ->whereHas('assignedEstates', function ($query) {
                    $query->where('estate_id', $this->house->estate_id);
                })
                ->get();

            foreach ($reviewers as $reviewer) {
                // Use the pre-loaded contact
                $contact = $reviewer->contacts->first();

                if ($contact) {
                    $template = $whatsAppTemplates->renderTemplate('invoice_submitted_for_approval', [
                        'reviewer_name' => $reviewer->name,
                        'invoice_number' => $this->invoice_number,
                        'house_number' => $this->house->house_number,
                        'manager_name' => $this->submittedBy?->name ?? 'System',
                        'amount' => number_format($this->total_amount, 2),
                        'customer_name' => $this->house->primaryContact?->name ?? 'Unknown',
                    ]);

                    if ($template) {
                        $whatsAppService->sendText(
                            $contact->phone,
                            $template,
                            $contact,
                            $this->house,
                            $this->house->estate
                        );
                    }
                }
            }

            Log::info('Reviewer notifications sent for invoice submission', [
                'invoice_id' => $this->id,
                'reviewer_count' => $reviewers->count(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send reviewer notifications for invoice submission', [
                'invoice_id' => $this->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send notification to managers when invoice is approved
     */
    public function notifyManagerOfApproval(): void
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            $whatsAppTemplates = app(WhatsAppTemplates::class);

            // Notify the manager who submitted the invoice
            $manager = $this->submittedBy;
            if (! $manager) {
                return;
            }

            // Load manager's contact with WhatsApp capability
            $contact = Contact::where('user_id', $manager->id)
                ->where('receive_notifications', true)
                ->whereNotNull('whatsapp_number')
                ->where('whatsapp_number', '!=', '')
                ->first();

            if ($contact) {
                $template = $whatsAppTemplates->renderTemplate('invoice_approved', [
                    'manager_name' => $manager->name,
                    'invoice_number' => $this->invoice_number,
                    'house_number' => $this->house->house_number,
                    'reviewer_name' => $this->approvedBy?->name ?? 'System',
                    'amount' => number_format($this->total_amount, 2),
                    'customer_name' => $this->house->primaryContact?->name ?? 'Unknown',
                ]);

                if ($template) {
                    $whatsAppService->sendText(
                        $contact->phone,
                        $template,
                        $contact,
                        $this->house,
                        $this->house->estate
                    );
                }
            }

            Log::info('Manager notification sent for invoice approval', [
                'invoice_id' => $this->id,
                'manager_id' => $manager->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send manager notification for invoice approval', [
                'invoice_id' => $this->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send notification to managers when invoice is rejected
     */
    public function notifyManagerOfRejection(string $rejectionReason): void
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            $whatsAppTemplates = app(WhatsAppTemplates::class);

            // Notify the manager who submitted the invoice
            $manager = $this->submittedBy;
            if (! $manager) {
                return;
            }

            // Load manager's contact with WhatsApp capability
            $contact = Contact::where('user_id', $manager->id)
                ->where('receive_notifications', true)
                ->whereNotNull('whatsapp_number')
                ->where('whatsapp_number', '!=', '')
                ->first();

            if ($contact) {
                $template = $whatsAppTemplates->renderTemplate('invoice_rejected', [
                    'manager_name' => $manager->name,
                    'invoice_number' => $this->invoice_number,
                    'house_number' => $this->house->house_number,
                    'reviewer_name' => $this->approvedBy?->name ?? 'System',
                    'amount' => number_format($this->total_amount, 2),
                    'customer_name' => $this->house->primaryContact?->name ?? 'Unknown',
                    'rejection_reason' => $rejectionReason,
                ]);

                if ($template) {
                    $whatsAppService->sendText(
                        $contact->phone,
                        $template,
                        $contact,
                        $this->house,
                        $this->house->estate
                    );
                }
            }

            Log::info('Manager notification sent for invoice rejection', [
                'invoice_id' => $this->id,
                'manager_id' => $manager->id,
                'rejection_reason' => $rejectionReason,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send manager notification for invoice rejection', [
                'invoice_id' => $this->id,
                'error' => $e->getMessage(),
                'rejection_reason' => $rejectionReason,
            ]);
        }
    }
}
