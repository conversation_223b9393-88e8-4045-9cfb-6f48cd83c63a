<?php

namespace App\Models\Traits;

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

trait EstateScopes
{
    /**
     * Scope query to records accessible by the given user
     */
    public function scopeForUser(Builder $query, User $user): Builder
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return $query;
        }

        return $query->whereIn('estate_id', $user->assignedEstates()->pluck('id'));
    }

    /**
     * Scope query to records for a specific estate
     */
    public function scopeForEstate(Builder $query, $estate): Builder
    {
        if (is_numeric($estate)) {
            return $query->where('estate_id', $estate);
        }

        return $query->where('estate_id', $estate->id);
    }

    /**
     * Scope query to records for estates accessible by user
     */
    public function scopeForAccessibleEstates(Builder $query, User $user): Builder
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return $query;
        }

        return $query->whereIn('estate_id', $user->assignedEstates()->pluck('id'));
    }

    /**
     * Scope query to records for user's own data (residents)
     */
    public function scopeForOwnData(Builder $query, User $user): Builder
    {
        if (! $user->hasRole(UserRole::RESIDENT)) {
            return $query;
        }

        // For residents, scope to their own houses through contacts
        return $query->whereIn('house_id', $user->contacts()->pluck('house_id'));
    }
}
