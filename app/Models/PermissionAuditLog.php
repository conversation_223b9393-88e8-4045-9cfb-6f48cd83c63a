<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property string $action
 * @property array $details
 * @property string $target_type
 * @property string $target_id
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereTargetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereTargetType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog whereUserAgent($value)
 *
 * @mixin \Eloquent
 */
class PermissionAuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'details',
        'target_type',
        'target_id',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'details' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForAction($query, $action)
    {
        return $query->where('action', $action);
    }

    public function scopeForTarget($query, $targetType, $targetId)
    {
        return $query->where('target_type', $targetType)
            ->where('target_id', $targetId);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }
}