<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ValidationBaseline extends Model
{
    protected $fillable = [
        'estate_id',
        'house_id',
        'baseline_type',
        'average_consumption',
        'median_consumption',
        'std_deviation',
        'q1_consumption',
        'q3_consumption',
        'sample_size',
        'baseline_date',
        'monthly_averages',
    ];

    protected $casts = [
        'average_consumption' => 'decimal:2',
        'median_consumption' => 'decimal:2',
        'std_deviation' => 'decimal:2',
        'q1_consumption' => 'decimal:2',
        'q3_consumption' => 'decimal:2',
        'sample_size' => 'integer',
        'baseline_date' => 'date',
        'monthly_averages' => 'array',
    ];

    public function estate(): BelongsTo
    {
        return $this->belongsTo(Estate::class);
    }

    public function house(): BelongsTo
    {
        return $this->belongsTo(House::class);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('baseline_type', $type);
    }

    public function scopeByEstate($query, int $estateId)
    {
        return $query->where('estate_id', $estateId);
    }

    public function scopeByHouse($query, int $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('baseline_date', 'desc');
    }

    public function getIqrAttribute(): float
    {
        return $this->q3_consumption - $this->q1_consumption;
    }

    public function getLowerBoundAttribute(): float
    {
        return $this->q1_consumption - (1.5 * $this->getIqrAttribute());
    }

    public function getUpperBoundAttribute(): float
    {
        return $this->q3_consumption + (1.5 * $this->getIqrAttribute());
    }

    public function getMonthlyAverage(int $month): ?float
    {
        return $this->monthly_averages[$month] ?? null;
    }
}
