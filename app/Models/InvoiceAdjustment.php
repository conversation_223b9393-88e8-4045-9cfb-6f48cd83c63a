<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperInvoiceAdjustment
 *
 * @property int $id
 * @property int $invoice_id
 * @property string $type
 * @property numeric $amount
 * @property string $reason
 * @property string|null $description
 * @property int|null $user_id
 * @property \Illuminate\Support\Carbon $adjustment_date
 * @property string $reference_number
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $formatted_amount
 * @property-read \App\Models\Invoice $invoice
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereAdjustmentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereReferenceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereUserId($value)
 *
 * @mixin \Eloquent
 */
class InvoiceAdjustment extends Model
{
    use HasFactory;

    const TYPE_CREDIT = 'credit';

    const TYPE_DEBIT = 'debit';

    const TYPE_REFUND = 'refund';

    const TYPE_CORRECTION = 'correction';

    protected $fillable = [
        'invoice_id',
        'type',
        'amount',
        'reason',
        'description',
        'user_id',
        'adjustment_date',
        'reference_number',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'adjustment_date' => 'date',
        'metadata' => 'array',
    ];

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getFormattedAmountAttribute()
    {
        $prefix = $this->type === self::TYPE_CREDIT || $this->type === self::TYPE_REFUND ? '-' : '+';

        return $prefix.' KES '.number_format(abs($this->amount), 2);
    }

    public static function generateReferenceNumber(): string
    {
        return 'ADJ-'.now()->format('Ymd').'-'.strtoupper(substr(uniqid(), -6));
    }
}
