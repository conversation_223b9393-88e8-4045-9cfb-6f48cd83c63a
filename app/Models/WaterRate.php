<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperWaterRate
 *
 * @property int $id
 * @property int $estate_id
 * @property string $name
 * @property numeric $rate_per_unit
 * @property numeric $minimum_charge
 * @property int $minimum_units
 * @property numeric $fixed_charge
 * @property \Illuminate\Support\Carbon $effective_from
 * @property \Illuminate\Support\Carbon|null $effective_to
 * @property bool $is_active
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Estate $estate
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate active()
 * @method static \Database\Factories\WaterRateFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate forEstate($estateId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereEffectiveFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereEffectiveTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereEstateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereFixedCharge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereMinimumCharge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereMinimumUnits($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereRatePerUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class WaterRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'estate_id',
        'name',
        'rate_per_unit',
        'minimum_charge',
        'minimum_units',
        'fixed_charge',
        'effective_from',
        'effective_to',
        'is_active',
        'description',
    ];

    protected $casts = [
        'rate_per_unit' => 'decimal:4',
        'minimum_charge' => 'decimal:2',
        'fixed_charge' => 'decimal:2',
        'effective_from' => 'date',
        'effective_to' => 'date',
        'is_active' => 'boolean',
    ];

    public function estate(): BelongsTo
    {
        return $this->belongsTo(Estate::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where('effective_from', '<=', now())
            ->where(function ($q) {
                $q->whereNull('effective_to')
                    ->orWhere('effective_to', '>=', now());
            });
    }

    public function scopeForEstate($query, $estateId)
    {
        return $query->where('estate_id', $estateId);
    }

    public function calculateAmount($units)
    {
        $units = max($units, 0);

        if ($units <= $this->minimum_units) {
            return $this->minimum_charge + $this->fixed_charge;
        }

        $consumptionCharge = $units * $this->rate_per_unit;

        return $consumptionCharge + $this->fixed_charge;
    }

    public function isCurrentlyActive()
    {
        return $this->is_active
            && $this->effective_from <= now()
            && (is_null($this->effective_to) || $this->effective_to >= now());
    }
}
