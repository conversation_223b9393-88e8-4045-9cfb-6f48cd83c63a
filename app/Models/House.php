<?php

namespace App\Models;

use App\Models\Traits\EstateScopes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @mixin IdeHelperHouse
 *
 * @property int $id
 * @property int $estate_id
 * @property string $house_number
 * @property string|null $block
 * @property string|null $floor
 * @property string $type
 * @property string $meter_number
 * @property numeric $initial_reading
 * @property bool $is_active
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $bedrooms
 * @property numeric|null $size_sqft
 * @property numeric|null $monthly_rent
 * @property \Illuminate\Support\Carbon|null $occupancy_date
 * @property string|null $gps_coordinates
 * @property string|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Contact> $contacts
 * @property-read int|null $contacts_count
 * @property-read \App\Models\Estate $estate
 * @property-read mixed $current_reading
 * @property-read mixed $full_address
 * @property-read mixed $is_occupied
 * @property-read mixed $primary_contact
 * @property-read mixed $status
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\HouseContact> $houseContacts
 * @property-read int|null $house_contacts_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Invoice> $invoices
 * @property-read int|null $invoices_count
 * @property-read \App\Models\MeterReading|null $latestReading
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\MeterReading> $meterReadings
 * @property-read int|null $meter_readings_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byBlock($block)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byBlockAndFloor($block, $floor = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byEstate($estateId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byHouseNumber($houseNumber)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byMeterNumber($meterNumber)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byType($type)
 * @method static \Database\Factories\HouseFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House occupied()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House vacant()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereBedrooms($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereBlock($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereEstateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereFloor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereGpsCoordinates($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereHouseNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereInitialReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereMeterNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereMonthlyRent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereOccupancyDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereSizeSqft($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House withContactCount()
 *
 * @mixin \Eloquent
 */
class House extends Model
{
    use EstateScopes, HasFactory;

    protected $fillable = [
        'estate_id',
        'house_number',
        'block',
        'floor',
        'type',
        'bedrooms',
        'size_sqft',
        'monthly_rent',
        'meter_number',
        'initial_reading',
        'is_active',
        'occupancy_date',
        'gps_coordinates',
        'notes',
    ];

    protected $casts = [
        'initial_reading' => 'decimal:2',
        'bedrooms' => 'integer',
        'size_sqft' => 'decimal:2',
        'monthly_rent' => 'decimal:2',
        'is_active' => 'boolean',
        'occupancy_date' => 'date',
    ];

    public function estate(): BelongsTo
    {
        return $this->belongsTo(Estate::class);
    }

    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class);
    }

    public function primaryContact(): HasOne
    {
        return $this->hasOne(Contact::class)->where('is_primary', true);
    }

    public function houseContacts()
    {
        return $this->hasMany(HouseContact::class);
    }

    public function activeContacts()
    {
        return $this->contacts()->where('is_active', true);
    }

    public function owners()
    {
        return $this->contacts()->where('type', 'owner');
    }

    public function tenants()
    {
        return $this->contacts()->where('type', 'tenant');
    }

    public function caretakers()
    {
        return $this->contacts()->where('type', 'caretaker');
    }

    public function emergencyContacts()
    {
        return $this->contacts()->where('type', 'emergency');
    }

    public function meterReadings(): HasMany
    {
        return $this->hasMany(MeterReading::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function whatsappMessages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class);
    }

    public function houseAccount(): HasMany
    {
        return $this->hasMany(HouseAccount::class);
    }

    public function account(): HasOne
    {
        return $this->hasOne(HouseAccount::class)->latestOfMany();
    }

    public function latestReading()
    {
        return $this->hasOne(MeterReading::class)->latestOfMany();
    }

    public function getCurrentReadingAttribute()
    {
        $latest = $this->latestReading;

        return $latest ? $latest->current_reading : $this->initial_reading;
    }

    public function getPrimaryContactAttribute()
    {
        return $this->primaryContact ?? $this->contacts()->first();
    }

    public function getIsOccupiedAttribute()
    {
        return ! is_null($this->occupancy_date);
    }

    public function getStatusAttribute()
    {
        if (! $this->is_active) {
            return 'inactive';
        }

        return $this->is_occupied ? 'occupied' : 'vacant';
    }

    public function getFullAddressAttribute()
    {
        $address = $this->house_number;
        if ($this->block) {
            $address .= ', Block '.$this->block;
        }
        if ($this->floor) {
            $address .= ', Floor '.$this->floor;
        }
        $address .= ', '.$this->estate->name;

        return $address;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByEstate($query, $estateId)
    {
        return $query->where('estate_id', $estateId);
    }

    public function scopeByBlock($query, $block)
    {
        return $query->where('block', $block);
    }

    public function scopeVacant($query)
    {
        return $query->where('is_active', true)->whereNull('occupancy_date');
    }

    public function scopeOccupied($query)
    {
        return $query->where('is_active', true)->whereNotNull('occupancy_date');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByHouseNumber($query, $houseNumber)
    {
        return $query->where('house_number', 'like', '%'.$houseNumber.'%');
    }

    public function scopeByMeterNumber($query, $meterNumber)
    {
        return $query->where('meter_number', 'like', '%'.$meterNumber.'%');
    }

    public function scopeByBlockAndFloor($query, $block, $floor = null)
    {
        return $query->when($block, function ($q) use ($block) {
            return $q->where('block', 'like', '%'.$block.'%');
        })
            ->when($floor, function ($q) use ($floor) {
                return $q->where('floor', 'like', '%'.$floor.'%');
            });
    }

    public function scopeWithContactCount($query)
    {
        return $query->withCount('contacts');
    }

    public static function validationRules($estateId, $id = null)
    {
        return [
            'estate_id' => 'required|exists:estates,id',
            'house_number' => [
                'required',
                'string',
                'max:50',
                new \App\Rules\UniqueHouseNumber($estateId, $id),
            ],
            'block' => 'nullable|string|max:50',
            'floor' => 'nullable|string|max:50',
            'type' => 'required|in:residential,commercial',
            'bedrooms' => 'nullable|integer|min:0',
            'size_sqft' => 'nullable|numeric|min:0',
            'monthly_rent' => 'nullable|numeric|min:0',
            'meter_number' => [
                'required',
                'string',
                'max:50',
                new \App\Rules\UniqueMeterNumber($id),
            ],
            'initial_reading' => 'required|numeric|min:0',
            'is_active' => 'boolean',
            'occupancy_date' => 'nullable|date',
            'gps_coordinates' => 'nullable|string|max:100',
            'notes' => 'nullable|string',
        ];
    }
}
