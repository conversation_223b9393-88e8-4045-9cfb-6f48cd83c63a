<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AccountTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'house_account_id',
        'transaction_type',
        'reference_type',
        'reference_id',
        'amount',
        'balance_before',
        'balance_after',
        'description',
        'user_id',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($transaction) {
            $transaction->houseAccount->updateLastTransactionDate($transaction->created_at);
        });
    }

    public function houseAccount(): BelongsTo
    {
        return $this->belongsTo(HouseAccount::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2);
    }

    public function getAmountWithSignAttribute(): string
    {
        $sign = in_array($this->transaction_type, ['payment', 'credit_note']) ? '+' : '-';

        return $sign.$this->formatted_amount;
    }

    public function isCredit(): bool
    {
        return in_array($this->transaction_type, ['payment', 'credit_note']);
    }

    public function isDebit(): bool
    {
        return in_array($this->transaction_type, ['invoice', 'adjustment']);
    }

    public function isPending(): bool
    {
        return false; // No status field in current implementation
    }

    public function isProcessed(): bool
    {
        return true; // All transactions are considered processed
    }

    public function isFailed(): bool
    {
        return false; // No status field in current implementation
    }

    public function getReference(): ?Model
    {
        $referenceId = $this->getAttribute('reference_id');
        $referenceType = $this->getAttribute('reference_type');

        if ($referenceId && $referenceType) {
            $modelClass = 'App\\Models\\'.$referenceType;
            if (class_exists($modelClass)) {
                $result = $modelClass::find($referenceId);

                return $result;
            }
        }

        return null;
    }

    public function getReferenceAttribute(): ?Model
    {
        return $this->getReference();
    }

    public function scopeCredits($query)
    {
        return $query->whereIn('transaction_type', ['payment', 'credit_note']);
    }

    public function scopeDebits($query)
    {
        return $query->whereIn('transaction_type', ['invoice', 'adjustment']);
    }

    public function scopeProcessed($query)
    {
        return $query; // All transactions are processed
    }

    public function scopePending($query)
    {
        return $query->whereRaw('1=0'); // No pending transactions
    }

    public function scopeFailed($query)
    {
        return $query->whereRaw('1=0'); // No failed transactions
    }

    public function scopeForReference($query, $referenceType, $referenceId)
    {
        return $query->where('reference_type', $referenceType)
            ->where('reference_id', $referenceId);
    }
}
