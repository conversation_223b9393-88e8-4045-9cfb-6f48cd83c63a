<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property string $entity_type
 * @property string $format
 * @property array<array-key, mixed> $columns
 * @property array<array-key, mixed>|null $filters
 * @property bool $is_scheduled
 * @property string|null $schedule_frequency
 * @property \Illuminate\Support\Carbon|null $last_run_at
 * @property \Illuminate\Support\Carbon|null $next_run_at
 * @property bool $is_public
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ExportJob> $exportJobs
 * @property-read int|null $export_jobs_count
 * @property-read \App\Models\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate due()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate forEntityType($entityType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate forUser($userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate scheduled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereColumns($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereEntityType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereFilters($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereFormat($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereIsPublic($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereIsScheduled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereLastRunAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereNextRunAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereScheduleFrequency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportTemplate withoutTrashed()
 *
 * @mixin \Eloquent
 */
class ExportTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'name',
        'entity_type',
        'format',
        'columns',
        'filters',
        'is_scheduled',
        'schedule_frequency',
        'last_run_at',
        'next_run_at',
        'is_public',
    ];

    protected $casts = [
        'columns' => 'array',
        'filters' => 'array',
        'is_scheduled' => 'boolean',
        'is_public' => 'boolean',
        'last_run_at' => 'datetime',
        'next_run_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function exportJobs(): HasMany
    {
        return $this->hasMany(ExportJob::class);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where('user_id', $userId)
                ->orWhere('is_public', true);
        });
    }

    public function scopeForEntityType($query, $entityType)
    {
        return $query->where('entity_type', $entityType);
    }

    public function scopeScheduled($query)
    {
        return $query->where('is_scheduled', true);
    }

    public function scopeDue($query)
    {
        return $query->where('is_scheduled', true)
            ->where('next_run_at', '<=', now());
    }
}
