<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

/**
 * @mixin IdeHelperEstate
 *
 * @property int $id
 * @property string $name
 * @property string|null $address
 * @property string|null $city
 * @property string|null $state
 * @property string|null $postal_code
 * @property string $country
 * @property string|null $description
 * @property array<array-key, mixed>|null $settings
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $code
 * @property string|null $manager_name
 * @property string|null $manager_phone
 * @property string|null $manager_email
 * @property int $total_houses
 * @property int $occupied_houses
 * @property string|null $deleted_at
 * @property string|null $contact_email
 * @property string|null $contact_phone
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Contact> $contacts
 * @property-read int|null $contacts_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\House> $houses
 * @property-read int|null $houses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Invoice> $invoices
 * @property-read int|null $invoices_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\MeterReading> $meterReadings
 * @property-read int|null $meter_readings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WaterRate> $waterRates
 * @property-read int|null $water_rates_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate byCode($code)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate byLocation($city, $state = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate byName($name)
 * @method static \Database\Factories\EstateFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereContactEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereManagerEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereManagerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereManagerPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereOccupiedHouses($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate wherePostalCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereTotalHouses($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Estate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'description',
        'settings',
        'manager_name',
        'manager_phone',
        'manager_email',
        'contact_email',
        'contact_phone',
        'is_active',
        'total_houses',
        'occupied_houses',
    ];

    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
        'total_houses' => 'integer',
        'occupied_houses' => 'integer',
    ];

    public function houses(): HasMany
    {
        return $this->hasMany(House::class);
    }

    public function waterRates(): HasMany
    {
        return $this->hasMany(WaterRate::class);
    }

    public function meterReadings(): HasManyThrough
    {
        return $this->hasManyThrough(MeterReading::class, House::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class);
    }

    public function activeHouses()
    {
        return $this->houses()->where('is_active', true);
    }

    public function occupiedHouses()
    {
        return $this->houses()->where('is_active', true)->whereNotNull('occupancy_date');
    }

    public function vacantHouses()
    {
        return $this->houses()->where('is_active', true)->whereNull('occupancy_date');
    }

    public function activeWaterRate()
    {
        return $this->waterRates()
            ->where('is_active', true)
            ->where('effective_date', '<=', now())
            ->orderBy('effective_date', 'desc')
            ->first();
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCode($query, $code)
    {
        return $query->where('code', $code);
    }

    public function scopeByName($query, $name)
    {
        return $query->where('name', 'like', '%'.$name.'%');
    }

    public function scopeByLocation($query, $city, $state = null)
    {
        return $query->when($city, function ($q) use ($city) {
            return $q->where('city', 'like', '%'.$city.'%');
        })
            ->when($state, function ($q) use ($state) {
                return $q->where('state', 'like', '%'.$state.'%');
            });
    }

    public static function validationRules($id = null)
    {
        return [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:estates,code,'.($id ?? 'NULL'),
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'description' => 'nullable|string',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:50',
            'manager_email' => 'nullable|email|max:255',
            'is_active' => 'boolean',
        ];
    }
}
