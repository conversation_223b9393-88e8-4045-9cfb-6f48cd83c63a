<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'category',
        'requires_estate_assignment',
        'system_level_only',
        'allow_user_overrides',
        'is_active',
    ];

    protected $casts = [
        'requires_estate_assignment' => 'boolean',
        'system_level_only' => 'boolean',
        'allow_user_overrides' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function roles()
    {
        return $this->belongsToMany(UserRole::class, 'role_permissions', 'permission_id', 'role')
            ->withPivot(['assigned_by', 'assigned_at'])
            ->withTimestamps();
    }

    public function userOverrides()
    {
        return $this->belongsToMany(User::class, 'user_permission_overrides')
            ->withPivot(['action', 'granted_by', 'expires_at', 'reason'])
            ->withTimestamps();
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeSystemLevel($query)
    {
        return $query->where('system_level_only', true);
    }

    public function scopeEstateBased($query)
    {
        return $query->where('requires_estate_assignment', true);
    }
}
