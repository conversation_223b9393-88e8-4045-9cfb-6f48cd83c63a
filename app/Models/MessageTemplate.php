<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $name
 * @property string $type
 * @property string|null $subject
 * @property string $content
 * @property array<array-key, mixed>|null $variables
 * @property bool $is_active
 * @property string|null $trigger_event
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate forEvent($event)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate ofType($type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereSubject($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereTriggerEvent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate whereVariables($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MessageTemplate withoutTrashed()
 *
 * @mixin \Eloquent
 */
class MessageTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'type',
        'subject',
        'content',
        'variables',
        'is_active',
        'trigger_event',
    ];

    protected $casts = [
        'variables' => 'array',
        'is_active' => 'boolean',
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeForEvent($query, $event)
    {
        return $query->where('trigger_event', $event);
    }

    public function compileMessage(array $data = []): string
    {
        $content = $this->content;

        foreach ($data as $key => $value) {
            $content = str_replace('{'.$key.'}', $value, $content);
        }

        return $content;
    }

    public function compileSubject(array $data = []): ?string
    {
        if (! $this->subject) {
            return null;
        }

        $subject = $this->subject;

        foreach ($data as $key => $value) {
            $subject = str_replace('{'.$key.'}', $value, $subject);
        }

        return $subject;
    }
}
