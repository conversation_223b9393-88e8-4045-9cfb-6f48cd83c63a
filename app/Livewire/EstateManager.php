<?php

namespace App\Livewire;

use App\Models\Estate;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class EstateManager extends Component
{
    use WithPagination;

    public $showCreateModal = false;

    public $showEditModal = false;

    public $showDeleteModal = false;

    public $showModal = false;

    public $estateIdToEdit = null;

    public $estateIdToDelete = null;

    public $estateIdToShow = null;

    public $search = '';

    public $statusFilter = '';

    public $sortBy = 'created_at';

    public $sortDirection = 'desc';

    public $form = [
        'name' => '',
        'code' => '',
        'address' => '',
        'city' => '',
        'state' => '',
        'postal_code' => '',
        'country' => 'Kenya',
        'contact_email' => '',
        'contact_phone' => '',
    ];

    protected function rules()
    {
        $rules = [
            'form.name' => 'required|string|max:255',
            'form.code' => 'required|string|max:50|unique:estates,code',
            'form.address' => 'nullable|string|max:500',
            'form.city' => 'required|string|max:100',
            'form.state' => 'nullable|string|max:100',
            'form.postal_code' => 'nullable|string|max:20',
            'form.country' => 'nullable|string|max:100',
            'form.contact_email' => 'nullable|email|max:255',
            'form.contact_phone' => 'nullable|string|max:50',
        ];

        if ($this->estateIdToEdit) {
            $rules['form.code'] .= ','.$this->estateIdToEdit;
        }

        return $rules;
    }

    public function openCreateModal()
    {
        $this->reset('form', 'estateIdToEdit');
        $this->form['country'] = 'Kenya';
        $this->showCreateModal = true;
    }

    public function openEditModal($estateId)
    {
        $this->reset('form');
        $estate = Estate::findOrFail($estateId);

        $this->form = [
            'name' => $estate->name,
            'code' => $estate->code,
            'address' => $estate->address ?? '',
            'city' => $estate->city ?? '',
            'state' => $estate->state ?? '',
            'postal_code' => $estate->postal_code ?? '',
            'country' => $estate->country ?? 'Kenya',
            'contact_email' => $estate->contact_email ?? '',
            'contact_phone' => $estate->contact_phone ?? '',
        ];

        $this->estateIdToEdit = $estateId;
        $this->showEditModal = true;
    }

    public function confirmDelete($estateId)
    {
        $this->estateIdToDelete = $estateId;
        $this->showDeleteModal = true;
    }

    public function showEstate($estateId)
    {
        $this->estateIdToShow = $estateId;
        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        if ($this->estateIdToEdit) {
            $estate = Estate::findOrFail($this->estateIdToEdit);
            $estate->update($this->form);
            $this->dispatch('estate-saved', message: 'Estate updated successfully.');
        } else {
            Estate::create($this->form);
            $this->dispatch('estate-saved', message: 'Estate created successfully.');
        }

        $this->reset(['showCreateModal', 'showEditModal', 'form', 'estateIdToEdit']);
    }

    public function delete()
    {
        if ($this->estateIdToDelete) {
            $estate = Estate::findOrFail($this->estateIdToDelete);

            // Check if estate has houses
            if ($estate->houses()->count() > 0) {
                $this->addError('deletion', 'Cannot delete estate that has houses associated with it.');

                return;
            }

            // Check if estate has water rates
            if ($estate->waterRates()->count() > 0) {
                $this->addError('deletion', 'Cannot delete estate that has water rates associated with it.');

                return;
            }

            $estate->delete();
            $this->dispatch('estate-deleted', message: 'Estate deleted successfully.');
        }

        $this->reset(['showDeleteModal', 'estateIdToDelete']);
    }

    public function mount()
    {
        // Authorization check is handled by route middleware
    }

    public function getUserRole()
    {
        return Auth::user()->role->value;
    }

    public function getEstateShowRoute($estate)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.estates.show', $estate),
            'reviewer' => route('management.estates.show', $estate), // Reviewers use management routes for estates
            'caretaker' => route('caretaker.estates.show', $estate),
            default => route('estates.show', $estate), // Fallback to generic route
        };
    }

    public function getHousesRoute($estateId = null)
    {
        $role = $this->getUserRole();
        $params = $estateId ? ['estate' => $estateId] : [];

        return match ($role) {
            'manager' => route('management.houses', $params),
            'reviewer' => route('reviewer.houses', $params),
            'caretaker' => $estateId ? route('caretaker.houses', ['estateId' => $estateId]) : route('caretaker.houses'),
            default => route('houses', $params), // Fallback to generic route
        };
    }

    public function render()
    {
        $query = Estate::withCount(['houses', 'houses as occupied_houses_count' => function ($query) {
            $query->whereHas('contacts');
        }]);

        // Apply search filter
        if ($this->search) {
            $query->where('name', 'like', '%'.$this->search.'%')
                ->orWhere('code', 'like', '%'.$this->search.'%');
        }

        // Apply status filter
        if ($this->statusFilter) {
            $query->where('is_active', $this->statusFilter === 'active');
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        $estates = $query->paginate(10);

        return view('livewire.estate-manager', [
            'estates' => $estates,
            'totalEstates' => Estate::count(),
            'activeEstates' => Estate::where('is_active', true)->count(),
            'inactiveEstates' => Estate::where('is_active', false)->count(),
        ]);
    }
}
