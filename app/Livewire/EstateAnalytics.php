<?php

namespace App\Livewire;

use App\Models\Estate;
use Livewire\Component;
use Livewire\WithPagination;

class EstateAnalytics extends Component
{
    use WithPagination;

    public $selectedEstate = null;

    public $dateRange = '30'; // days

    public $sortBy = 'total_consumption';

    public $sortDirection = 'desc';

    public $search = '';

    protected $queryString = [
        'selectedEstate' => ['except' => ''],
        'dateRange' => ['except' => '30'],
        'sortBy' => ['except' => 'total_consumption'],
        'sortDirection' => ['except' => 'desc'],
        'search' => ['except' => ''],
    ];

    public function mount()
    {
        $user = auth()->user();
        
        if (! $user->isManager() && ! $user->isAdmin()) {
            abort(403, 'Unauthorized access');
        }
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'desc';
        }
    }

    public function getEstatesProperty()
    {
        $user = auth()->user();
        $query = Estate::withCount(['houses', 'meterReadings']);
        
        if ($user->isAdmin()) {
            // Admin can see all estates
            return $query->orderBy('name')->get();
        } else {
            // Manager can only see assigned estates
            return $user->assignedEstates()->withCount(['houses', 'meterReadings'])
                ->orderBy('name')
                ->get();
        }
    }

    public function getEstateAnalyticsProperty()
    {
        $user = auth()->user();
        
        if ($user->isAdmin()) {
            $query = Estate::with([
                'houses.meterReadings' => function ($query) {
                    $query->where('created_at', '>=', now()->subDays($this->dateRange));
                },
                'houses.invoices' => function ($query) {
                    $query->where('created_at', '>=', now()->subDays($this->dateRange));
                },
            ]);
        } else {
            // Manager can only see assigned estates
            $query = $user->assignedEstates()->with([
                'houses.meterReadings' => function ($query) {
                    $query->where('created_at', '>=', now()->subDays($this->dateRange));
                },
                'houses.invoices' => function ($query) {
                    $query->where('created_at', '>=', now()->subDays($this->dateRange));
                },
            ]);
        }

        if ($this->search) {
            $query->where('name', 'like', '%'.$this->search.'%');
        }

        if ($this->selectedEstate) {
            $query->where('id', $this->selectedEstate);
        }

        $estates = $query->get()->map(function ($estate) {
            $totalHouses = $estate->houses->count();
            $occupiedHouses = $estate->houses->where('status', 'occupied')->count();
            $totalConsumption = $estate->houses->flatMap->meterReadings->sum('consumption');
            $totalRevenue = $estate->houses->flatMap->invoices->sum('amount');
            $avgConsumption = $totalHouses > 0 ? $totalConsumption / $totalHouses : 0;
            $occupancyRate = $totalHouses > 0 ? ($occupiedHouses / $totalHouses) * 100 : 0;

            // Calculate efficiency score (revenue per liter)
            $efficiencyScore = $totalConsumption > 0 ? $totalRevenue / $totalConsumption : 0;

            // Calculate growth rate (comparing to previous period)
            $previousPeriodStart = now()->subDays($this->dateRange * 2);
            $previousPeriodEnd = now()->subDays($this->dateRange);

            $previousConsumption = $estate->houses->flatMap(function ($house) use ($previousPeriodStart, $previousPeriodEnd) {
                return $house->meterReadings()
                    ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
                    ->get();
            })->sum('consumption');

            $growthRate = $previousConsumption > 0
                ? (($totalConsumption - $previousConsumption) / $previousConsumption) * 100
                : 0;

            return [
                'estate' => $estate,
                'total_houses' => $totalHouses,
                'occupied_houses' => $occupiedHouses,
                'total_consumption' => $totalConsumption,
                'total_revenue' => $totalRevenue,
                'avg_consumption_per_house' => $avgConsumption,
                'occupancy_rate' => $occupancyRate,
                'efficiency_score' => $efficiencyScore,
                'growth_rate' => $growthRate,
                'revenue_per_house' => $totalHouses > 0 ? $totalRevenue / $totalHouses : 0,
            ];
        });

        // Sort the collection
        if ($this->sortDirection === 'asc') {
            $estates = $estates->sortBy($this->sortBy);
        } else {
            $estates = $estates->sortByDesc($this->sortBy);
        }

        return $estates;
    }

    public function getTopPerformersProperty()
    {
        return $this->estate_analytics->take(3);
    }

    public function getSummaryStatsProperty()
    {
        $analytics = $this->estate_analytics;

        return [
            'total_estates' => $analytics->count(),
            'total_houses' => $analytics->sum('total_houses'),
            'total_consumption' => $analytics->sum('total_consumption'),
            'total_revenue' => $analytics->sum('total_revenue'),
            'avg_occupancy' => $analytics->avg('occupancy_rate'),
            'avg_efficiency' => $analytics->avg('efficiency_score'),
        ];
    }

    public function getChartDataProperty()
    {
        $analytics = $this->estate_analytics->take(10);

        return [
            'consumption' => [
                'labels' => $analytics->pluck('estate.name'),
                'datasets' => [
                    [
                        'label' => 'Total Consumption (L)',
                        'data' => $analytics->pluck('total_consumption'),
                        'backgroundColor' => 'rgba(59, 130, 246, 0.8)',
                    ],
                ],
            ],
            'revenue' => [
                'labels' => $analytics->pluck('estate.name'),
                'datasets' => [
                    [
                        'label' => 'Total Revenue (KES)',
                        'data' => $analytics->pluck('total_revenue'),
                        'backgroundColor' => 'rgba(34, 197, 94, 0.8)',
                    ],
                ],
            ],
            'efficiency' => [
                'labels' => $analytics->pluck('estate.name'),
                'datasets' => [
                    [
                        'label' => 'Efficiency Score (KES/L)',
                        'data' => $analytics->pluck('efficiency_score'),
                        'backgroundColor' => 'rgba(168, 85, 247, 0.8)',
                    ],
                ],
            ],
        ];
    }

    public function render()
    {
        return view('livewire.estate-analytics', [
            'estates' => $this->estates,
            'estateAnalytics' => $this->estate_analytics,
            'topPerformers' => $this->top_performers,
            'summaryStats' => $this->summary_stats,
            'chartData' => $this->chart_data,
        ]);
    }
}
