<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\MeterReading;
use App\Services\ReadingValidationService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ValidationDashboard extends Component
{
    use WithPagination;

    public $selectedEstate = null;

    public $riskLevelFilter = 'all';

    public $statusFilter = 'all';

    public $searchTerm = '';

    public $showFilters = false;

    public $selectedReading = null;

    public $showReadingModal = false;

    public $batchAction = '';

    public $selectedReadings = [];

    protected $queryString = [
        'selectedEstate' => ['except' => ''],
        'riskLevelFilter' => ['except' => 'all'],
        'statusFilter' => ['except' => 'all'],
        'searchTerm' => ['except' => ''],
    ];

    public function mount()
    {
        if (! in_array(Auth::user()->role, ['management', 'reviewer'])) {
            abort(403, 'Unauthorized access to validation dashboard');
        }
    }

    public function getEstatesProperty()
    {
        return Estate::orderBy('name')->get();
    }

    public function getFlaggedReadingsProperty()
    {
        $query = MeterReading::with(['house.estate', 'user', 'reviewer'])
            ->where(function ($query) {
                $query->where('validation_status', '!=', 'approved')
                    ->orWhere('risk_level', '!=', 'low');
            });

        // Apply estate filter
        if ($this->selectedEstate) {
            $query->whereHas('house', function ($query) {
                $query->where('estate_id', $this->selectedEstate);
            });
        }

        // Apply risk level filter
        if ($this->riskLevelFilter !== 'all') {
            $query->where('risk_level', $this->riskLevelFilter);
        }

        // Apply status filter
        if ($this->statusFilter !== 'all') {
            $query->where('validation_status', $this->statusFilter);
        }

        // Apply search
        if ($this->searchTerm) {
            $query->where(function ($query) {
                $query->whereHas('house', function ($query) {
                    $query->where('house_number', 'like', '%'.$this->searchTerm.'%');
                })
                    ->orWhereHas('house.estate', function ($query) {
                        $query->where('name', 'like', '%'.$this->searchTerm.'%');
                    });
            });
        }

        return $query->orderBy('risk_level', 'desc')
            ->orderBy('confidence_score', 'asc')
            ->orderBy('created_at', 'desc')
            ->paginate(20);
    }

    public function getValidationStatsProperty()
    {
        $query = MeterReading::query();

        if ($this->selectedEstate) {
            $query->whereHas('house', function ($query) {
                $query->where('estate_id', $this->selectedEstate);
            });
        }

        $total = $query->count();
        $approved = (clone $query)->where('validation_status', 'approved')->count();
        $pending = (clone $query)->where('validation_status', 'pending')->count();
        $critical = (clone $query)->where('risk_level', 'critical')->count();
        $high = (clone $query)->where('risk_level', 'high')->count();
        $medium = (clone $query)->where('risk_level', 'medium')->count();

        return [
            'total' => $total,
            'approved' => $approved,
            'pending' => $pending,
            'critical' => $critical,
            'high' => $high,
            'medium' => $medium,
            'approval_rate' => $total > 0 ? round(($approved / $total) * 100, 1) : 0,
            'flag_rate' => $total > 0 ? round((($critical + $high + $medium) / $total) * 100, 1) : 0,
        ];
    }

    public function viewReading($readingId)
    {
        $this->selectedReading = MeterReading::with(['house.estate', 'user', 'reviewer'])->findOrFail($readingId);
        $this->showReadingModal = true;
    }

    public function approveReading($readingId)
    {
        $reading = MeterReading::findOrFail($readingId);

        $reading->update([
            'status' => 'approved',
            'validation_status' => 'approved',
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);

        $this->showReadingModal = false;
        $this->selectedReading = null;
        session()->flash('message', 'Reading approved successfully!');
    }

    public function rejectReading($readingId, $reason = '')
    {
        $reading = MeterReading::findOrFail($readingId);

        $reading->update([
            'status' => 'rejected',
            'validation_status' => 'rejected',
            'rejected_at' => now(),
            'rejected_by' => auth()->id(),
            'review_notes' => $reason,
        ]);

        $this->showReadingModal = false;
        $this->selectedReading = null;
        session()->flash('message', 'Reading rejected successfully!');
    }

    public function requestResubmission($readingId, $reason = '')
    {
        $reading = MeterReading::findOrFail($readingId);

        $reading->update([
            'status' => 'draft',
            'validation_status' => 'needs_resubmission',
            'review_notes' => $reason,
        ]);

        $this->showReadingModal = false;
        $this->selectedReading = null;
        session()->flash('message', 'Resubmission requested successfully!');
    }

    public function toggleReadingSelection($readingId)
    {
        if (in_array($readingId, $this->selectedReadings)) {
            $this->selectedReadings = array_diff($this->selectedReadings, [$readingId]);
        } else {
            $this->selectedReadings[] = $readingId;
        }
    }

    public function selectAllReadings()
    {
        $this->selectedReadings = $this->flaggedReadings->pluck('id')->toArray();
    }

    public function clearSelection()
    {
        $this->selectedReadings = [];
    }

    public function executeBatchAction()
    {
        if (empty($this->selectedReadings) || empty($this->batchAction)) {
            return;
        }

        $readings = MeterReading::whereIn('id', $this->selectedReadings)->get();

        foreach ($readings as $reading) {
            switch ($this->batchAction) {
                case 'approve':
                    $reading->update([
                        'status' => 'approved',
                        'validation_status' => 'approved',
                        'approved_at' => now(),
                        'approved_by' => auth()->id(),
                    ]);
                    break;
                case 'reject':
                    $reading->update([
                        'status' => 'rejected',
                        'validation_status' => 'rejected',
                        'rejected_at' => now(),
                        'rejected_by' => auth()->id(),
                    ]);
                    break;
                case 'resubmit':
                    $reading->update([
                        'status' => 'draft',
                        'validation_status' => 'needs_resubmission',
                    ]);
                    break;
            }
        }

        $this->selectedReadings = [];
        $this->batchAction = '';
        session()->flash('message', 'Batch action completed successfully!');
    }

    public function revalidateReading($readingId)
    {
        $reading = MeterReading::findOrFail($readingId);
        $validationService = new ReadingValidationService;

        $validationResults = $validationService->validateReading($reading);

        $reading->update([
            'validation_results' => $validationResults,
            'validation_status' => $validationResults['risk_level'],
            'confidence_score' => $validationResults['confidence_score'],
            'validated_at' => now(),
            'validated_by' => auth()->id(),
        ]);

        $this->showReadingModal = false;
        $this->selectedReading = null;
        session()->flash('message', 'Reading revalidated successfully!');
    }

    public function resetFilters()
    {
        $this->selectedEstate = null;
        $this->riskLevelFilter = 'all';
        $this->statusFilter = 'all';
        $this->searchTerm = '';
        $this->showFilters = false;
    }

    public function closeModal()
    {
        $this->showReadingModal = false;
        $this->selectedReading = null;
    }

    public function getRiskLevelColor($riskLevel)
    {
        return [
            'critical' => 'text-red-600 bg-red-100',
            'high' => 'text-orange-600 bg-orange-100',
            'medium' => 'text-yellow-600 bg-yellow-100',
            'low' => 'text-green-600 bg-green-100',
        ][$riskLevel] ?? 'text-gray-600 bg-gray-100';
    }

    public function getStatusColor($status)
    {
        return [
            'approved' => 'text-green-600 bg-green-100',
            'pending' => 'text-yellow-600 bg-yellow-100',
            'rejected' => 'text-red-600 bg-red-100',
            'needs_resubmission' => 'text-blue-600 bg-blue-100',
        ][$status] ?? 'text-gray-600 bg-gray-100';
    }

    public function render()
    {
        return view('livewire.validation-dashboard');
    }
}
