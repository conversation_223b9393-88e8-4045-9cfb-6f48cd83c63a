<?php

namespace App\Livewire;

use App\Models\House;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class HouseShow extends Component
{
    use WithPagination;

    public House $house;

    public string $activeTab = 'overview';

    public $showContactModal = false;

    public $showReadingModal = false;

    public $showInvoiceModal = false;

    public $contactForm = [
        'name' => '',
        'phone' => '',
        'email' => '',
        'is_primary' => false,
    ];

    public $readingForm = [
        'reading_date' => '',
        'reading_value' => '',
        'notes' => '',
    ];

    protected function rules()
    {
        return [
            'contactForm.name' => 'required|string|max:255',
            'contactForm.phone' => 'required|string|max:50',
            'contactForm.email' => 'nullable|email|max:255',
            'contactForm.is_primary' => 'boolean',
            'readingForm.reading_date' => 'required|date',
            'readingForm.reading_value' => 'required|numeric|min:0',
            'readingForm.notes' => 'nullable|string|max:500',
        ];
    }

    public function mount(House $house): void
    {
        $this->house = $house;
    }

    public function getUserRole()
    {
        return Auth::user()->role->value;
    }

    public function getHousesRoute()
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.houses'),
            'reviewer' => route('reviewer.houses'),
            'caretaker' => route('caretaker.houses'),
            default => route('houses'), // Fallback to generic route
        };
    }

    public function getHouseEditRoute($house)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.houses.edit', $house),
            'reviewer' => route('reviewer.houses.edit', $house),
            'caretaker' => route('caretaker.houses.edit', $house),
            default => route('houses.edit', $house), // Fallback to generic route
        };
    }

    public function getEstateShowRoute($estate)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.estates.show', $estate),
            'reviewer' => route('management.estates.show', $estate), // Reviewers use management routes for estates
            'caretaker' => route('caretaker.estates.show', $estate),
            default => route('estates.show', $estate), // Fallback to generic route
        };
    }

    public function getInvoiceCreateRoute($houseId = null)
    {
        $role = $this->getUserRole();
        $params = $houseId ? ['house' => $houseId] : [];

        return match ($role) {
            'manager' => route('management.invoices.create', $params),
            'reviewer' => route('reviewer.billing.create', $params),
            'caretaker' => route('caretaker.billing.create', $params),
            default => route('invoices.create', $params), // Fallback to generic route
        };
    }

    public function getInvoiceShowRoute($invoice)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.billing.show', $invoice),
            'reviewer' => route('reviewer.billing.show', $invoice),
            'caretaker' => route('caretaker.billing.show', $invoice),
            default => route('invoices.show', $invoice), // Fallback to generic route
        };
    }

    public function switchTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function openContactModal()
    {
        $this->reset('contactForm');
        $this->showContactModal = true;
    }

    public function openReadingModal()
    {
        $this->reset('readingForm');
        $this->readingForm['reading_date'] = now()->format('Y-m-d');
        $this->showReadingModal = true;
    }

    public function saveContact()
    {
        $this->validate([
            'contactForm.name' => 'required|string|max:255',
            'contactForm.phone' => 'required|string|max:50',
            'contactForm.email' => 'nullable|email|max:255',
            'contactForm.is_primary' => 'boolean',
        ]);

        $this->house->contacts()->create($this->contactForm);

        $this->dispatch('contact-saved', message: 'Contact added successfully.');
        $this->reset(['showContactModal', 'contactForm']);
    }

    public function saveReading()
    {
        $this->validate([
            'readingForm.reading_date' => 'required|date',
            'readingForm.reading_value' => 'required|numeric|min:0',
            'readingForm.notes' => 'nullable|string|max:500',
        ]);

        // Get the last reading for validation
        $lastReading = $this->house->meterReadings()->latest()->first();

        if ($lastReading && $this->readingForm['reading_value'] < $lastReading->reading_value) {
            $this->addError('readingForm.reading_value', 'Reading value cannot be less than the previous reading ('.$lastReading->reading_value.').');

            return;
        }

        $this->house->meterReadings()->create($this->readingForm);

        $this->dispatch('reading-saved', message: 'Meter reading recorded successfully.');
        $this->reset(['showReadingModal', 'readingForm']);
    }

    public function deleteContact($contactId)
    {
        $contact = $this->house->contacts()->findOrFail($contactId);
        $contact->delete();
        $this->dispatch('contact-deleted', message: 'Contact deleted successfully.');
    }

    public function deleteReading($readingId)
    {
        $reading = $this->house->meterReadings()->findOrFail($readingId);

        // Check if reading has invoices
        if ($reading->invoices()->count() > 0) {
            $this->addError('deletion', 'Cannot delete reading that has invoices associated with it.');

            return;
        }

        $reading->delete();
        $this->dispatch('reading-deleted', message: 'Reading deleted successfully.');
    }

    public function render()
    {
        $recentReadings = $this->house->meterReadings()
            ->latest()
            ->limit(5)
            ->get();

        $recentInvoices = $this->house->invoices()
            ->latest()
            ->limit(5)
            ->get();

        $contacts = $this->house->contacts()
            ->orderBy('is_primary', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        $stats = [
            'total_readings' => $this->house->meterReadings()->count(),
            'total_invoices' => $this->house->invoices()->count(),
            'paid_invoices' => $this->house->invoices()->where('status', 'paid')->count(),
            'outstanding_invoices' => $this->house->invoices()->where('status', '!=', 'paid')->count(),
            'total_billed' => $this->house->invoices()->sum('total_amount'),
            'total_paid' => $this->house->invoices()->where('status', 'paid')->sum('total_amount'),
        ];

        return view('livewire.house-show', [
            'recentReadings' => $recentReadings,
            'recentInvoices' => $recentInvoices,
            'contacts' => $contacts,
            'stats' => $stats,
        ]);
    }
}
