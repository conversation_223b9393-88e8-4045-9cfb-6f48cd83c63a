<?php

namespace App\Livewire\Admin;

use App\Exports\AgingReportExport;
use App\Services\AgingReportPdfService;
use App\Services\FinancialReportService;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class AgingReport extends Component
{
    public $estate_id = null;

    public $aging_report = [];

    public $show_report = false;

    public function mount()
    {
        $this->authorize('reports.view_all');
        $this->generateReport();
    }

    public function generateReport()
    {
        $service = new FinancialReportService;
        $this->aging_report = $service->generateAgingReport($this->estate_id);
        $this->show_report = true;
    }

    public function exportToExcel()
    {
        $service = new FinancialReportService;
        $agingReport = $service->generateAgingReport($this->estate_id);

        return Excel::download(new AgingReportExport($agingReport), 'aging-report-'.now()->format('Y-m-d').'.xlsx');
    }

    public function exportToPdf()
    {
        $service = new FinancialReportService;
        $agingReport = $service->generateAgingReport($this->estate_id);

        $pdfService = new AgingReportPdfService;
        $pdfPath = $pdfService->generateAgingReportPdf($agingReport);

        return response()->download(storage_path('app/'.$pdfPath), 'aging-report-'.now()->format('Y-m-d').'.pdf');
    }

    public function render()
    {
        return view('livewire.admin.aging-report')
            ->layout('components.layouts.app');
    }
}
