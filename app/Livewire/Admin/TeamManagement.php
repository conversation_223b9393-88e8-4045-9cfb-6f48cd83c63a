<?php

namespace App\Livewire\Admin;

use App\Enums\UserRole;
use App\Models\User;
use App\Services\ManagementHierarchyService;
use Livewire\Component;
use Livewire\WithPagination;

class TeamManagement extends Component
{
    use WithPagination;

    public $search = '';

    public $roleFilter = '';

    public $selectedManager = null;

    public $selectedSubordinates = [];

    public $relationshipType = 'manages';

    public $allManagers = [];

    public $allSubordinates = [];

    public $showTeamModal = false;

    public $showHierarchyModal = false;

    public $teamHierarchy = [];

    protected $managementHierarchyService;

    protected $rules = [
        'selectedSubordinates' => 'required|array|min:1',
        'selectedSubordinates.*' => 'exists:users,id',
        'relationshipType' => 'required|in:manages,oversees',
    ];

    public function boot(ManagementHierarchyService $managementHierarchyService)
    {
        $this->managementHierarchyService = $managementHierarchyService;
    }

    public function mount()
    {
        $this->loadAvailableUsers();
    }

    public function render()
    {
        $managers = User::query()
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%'.$this->search.'%')
                    ->orWhere('email', 'like', '%'.$this->search.'%');
            })
            ->when($this->roleFilter, function ($query) {
                $query->where('role', $this->roleFilter);
            })
            ->whereIn('role', [UserRole::ADMIN->value, UserRole::MANAGER->value, UserRole::REVIEWER->value])
            ->with(['subordinates'])
            ->paginate(10);

        return view('livewire.admin.team-management', [
            'managers' => $managers,
            'roles' => UserRole::cases(),
        ]);
    }

    public function loadAvailableUsers()
    {
        $this->allManagers = User::whereIn('role', [UserRole::ADMIN->value, UserRole::MANAGER->value, UserRole::REVIEWER->value])
            ->orderBy('name')
            ->get();

        $this->allSubordinates = User::whereIn('role', [UserRole::MANAGER->value, UserRole::REVIEWER->value, UserRole::CARETAKER->value])
            ->orderBy('name')
            ->get();
    }

    public function openTeamModal($managerId)
    {
        $this->selectedManager = User::findOrFail($managerId);
        $this->selectedSubordinates = $this->selectedManager->subordinates()->pluck('subordinate_id')->toArray();
        $this->showTeamModal = true;
    }

    public function closeTeamModal()
    {
        $this->showTeamModal = false;
        $this->selectedManager = null;
        $this->selectedSubordinates = [];
        $this->relationshipType = 'manages';
        $this->resetErrorBag();
    }

    public function saveTeamAssignments()
    {
        $this->validate();

        $this->managementHierarchyService->assignSubordinates(
            $this->selectedManager,
            $this->selectedSubordinates,
            $this->relationshipType
        );

        $this->closeTeamModal();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Team assignments updated successfully.',
        ]);
    }

    public function openHierarchyModal($managerId)
    {
        $this->selectedManager = User::findOrFail($managerId);
        $this->teamHierarchy = $this->managementHierarchyService->getAllSubordinates($this->selectedManager);
        $this->showHierarchyModal = true;
    }

    public function closeHierarchyModal()
    {
        $this->showHierarchyModal = false;
        $this->selectedManager = null;
        $this->teamHierarchy = [];
    }

    public function removeSubordinate($managerId, $subordinateId)
    {
        $manager = User::findOrFail($managerId);
        $subordinate = User::findOrFail($subordinateId);

        $this->managementHierarchyService->removeManagementRelationship($manager, $subordinate);

        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Subordinate removed from team successfully.',
        ]);
    }

    public function getAvailableSubordinates()
    {
        if (! $this->selectedManager) {
            return $this->allSubordinates;
        }

        return $this->allSubordinates->filter(function ($subordinate) {
            return $this->managementHierarchyService->canManageUser($this->selectedManager, $subordinate);
        });
    }

    public function getRelationshipLabel($relationship)
    {
        return match ($relationship) {
            'manages' => 'Manages',
            'oversees' => 'Oversees',
            default => ucfirst($relationship),
        };
    }

    public function getSubordinateName($subordinateId)
    {
        $subordinate = $this->allSubordinates->firstWhere('id', $subordinateId);

        return $subordinate ? $subordinate->name : 'Unknown';
    }

    public function getHierarchyLevel($user)
    {
        return $this->managementHierarchyService->getHierarchyLevel($user);
    }

    public function canManageUser($manager, $subordinate)
    {
        return $this->managementHierarchyService->canManageUser($manager, $subordinate);
    }
}
