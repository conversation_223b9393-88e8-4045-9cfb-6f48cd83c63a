<?php

namespace App\Livewire\Admin;

use App\Enums\UserRole;
use App\Models\Estate;
use App\Models\User;
use App\Services\EstateAssignmentService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class EstateAssignmentManager extends Component
{
    use WithPagination;

    public $search = '';

    public $roleFilter = '';

    public $selectedUser = null;

    public $selectedEstates = [];

    public $allEstates = [];

    public $showAssignmentModal = false;

    public $showHistoryModal = false;

    public $assignmentHistory = [];

    protected $estateAssignmentService;

    protected $rules = [
        'selectedEstates' => 'required|array|min:1',
        'selectedEstates.*' => 'exists:estates,id',
    ];

    public function boot(EstateAssignmentService $estateAssignmentService)
    {
        $this->estateAssignmentService = $estateAssignmentService;
    }

    public function mount()
    {
        $this->allEstates = Estate::active()->get();
    }

    public function render()
    {
        $users = User::query()
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%'.$this->search.'%')
                    ->orWhere('email', 'like', '%'.$this->search.'%');
            })
            ->when($this->roleFilter, function ($query) {
                $query->where('role', $this->roleFilter);
            })
            ->whereNotIn('role', [UserRole::ADMIN->value, UserRole::RESIDENT->value])
            ->with(['assignedEstates'])
            ->paginate(10);

        return view('livewire.admin.estate-assignment-manager', [
            'users' => $users,
            'roles' => UserRole::cases(),
        ]);
    }

    public function openAssignmentModal($userId)
    {
        $this->selectedUser = User::findOrFail($userId);
        $this->selectedEstates = $this->selectedUser->assignedEstates()->pluck('estates.id')->toArray();
        $this->showAssignmentModal = true;
    }

    public function closeAssignmentModal()
    {
        $this->showAssignmentModal = false;
        $this->selectedUser = null;
        $this->selectedEstates = [];
        $this->resetErrorBag();
    }

    public function saveAssignments()
    {
        $this->validate();

        $this->estateAssignmentService->assignUserToEstates(
            $this->selectedUser,
            $this->selectedEstates,
            auth()->user()
        );

        $this->closeAssignmentModal();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Estate assignments updated successfully.',
        ]);
    }

    public function openHistoryModal($userId)
    {
        $this->selectedUser = User::findOrFail($userId);
        $this->assignmentHistory = $this->estateAssignmentService->getUserAssignmentHistory($this->selectedUser);
        $this->showHistoryModal = true;
    }

    public function closeHistoryModal()
    {
        $this->showHistoryModal = false;
        $this->selectedUser = null;
        $this->assignmentHistory = [];
    }

    public function bulkAssignEstates()
    {
        $this->validate([
            'selectedEstates' => 'required|array|min:1',
            'selectedEstates.*' => 'exists:estates,id',
        ]);

        // This would be implemented with a bulk selection mechanism
        // For now, we'll just show a message
        $this->dispatch('notify', [
            'type' => 'info',
            'message' => 'Bulk assignment feature coming soon.',
        ]);
    }

    public function removeUserFromEstate($userId, $estateId)
    {
        $user = User::findOrFail($userId);
        $estate = Estate::findOrFail($estateId);

        $this->estateAssignmentService->removeUserFromEstate($user, $estate, auth()->user());

        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'User removed from estate successfully.',
        ]);
    }

    public function getEstateName($estateId)
    {
        $estate = $this->allEstates->firstWhere('id', $estateId);

        return $estate ? $estate->name : 'Unknown';
    }

    public function isUserAssignedToEstate($userId, $estateId)
    {
        $user = User::find($userId);

        return $user && $user->assignedEstates()->where('estates.id', $estateId)->exists();
    }
}
