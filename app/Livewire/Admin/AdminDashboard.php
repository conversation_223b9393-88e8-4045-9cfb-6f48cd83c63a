<?php

namespace App\Livewire\Admin;

use App\Models\ActivityLog;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use Livewire\Component;

class AdminDashboard extends Component
{
    public $totalUsers;

    public $totalEstates;

    public $totalHouses;

    public $recentActivity;

    public $systemStats;

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        $this->totalUsers = User::count();
        $this->totalEstates = Estate::count();
        $this->totalHouses = House::count();
        $this->recentActivity = ActivityLog::with('user')
            ->latest()
            ->take(10)
            ->get();

        $this->systemStats = [
            'activeUsers' => User::where('last_login_at', '>=', now()->subDays(7))->count(),
            'pendingApprovals' => MeterReading::where('status', 'pending')->count(),
            'overdueInvoices' => Invoice::where('due_date', '<', now())->where('status', '!=', 'paid')->count(),
            'totalInvoices' => Invoice::count(),
            'paidInvoices' => Invoice::where('status', 'paid')->count(),
            'unreadMessages' => 0, // TODO: Implement message tracking
        ];
    }

    public function render()
    {
        return view('livewire.admin.admin-dashboard');
    }
}
