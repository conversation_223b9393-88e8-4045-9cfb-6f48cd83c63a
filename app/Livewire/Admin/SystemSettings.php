<?php

namespace App\Livewire\Admin;

use App\Models\ActivityLog;
use App\Models\SystemSetting;
use Livewire\Component;

class SystemSettings extends Component
{
    public array $settings = [];

    public string $activeTab = 'general';

    public function mount(): void
    {
        $this->loadSettings();
    }

    public function loadSettings(): void
    {
        $allSettings = SystemSetting::all()->groupBy('group');

        foreach ($allSettings as $group => $groupSettings) {
            foreach ($groupSettings as $setting) {
                $this->settings[$setting->key] = $setting->typed_value;
            }
        }
    }

    public function saveSettings(): void
    {
        $oldSettings = $this->settings;

        foreach ($this->settings as $key => $value) {
            $setting = SystemSetting::where('key', $key)->first();
            if ($setting) {
                $setting->update(['value' => (string) $value]);
            }
        }

        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'updated',
            'entity_type' => 'SystemSettings',
            'description' => 'Updated system settings',
            'old_values' => $oldSettings,
            'new_values' => $this->settings,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        session()->flash('message', 'Settings saved successfully.');
    }

    public function resetToDefaults(): void
    {
        $this->loadDefaultSettings();
        session()->flash('message', 'Settings reset to defaults.');
    }

    private function loadDefaultSettings(): void
    {
        $defaults = [
            // General Settings
            'app_name' => 'Water Management System',
            'app_description' => 'Comprehensive water billing and management solution',
            'timezone' => 'UTC',
            'date_format' => 'Y-m-d',
            'currency' => 'USD',
            'currency_symbol' => '$',

            // Water Rate Settings
            'default_water_rate' => '0.50',
            'minimum_charge' => '10.00',
            'late_fee_percentage' => '5.0',
            'grace_period_days' => '30',

            // Billing Settings
            'billing_cycle' => 'monthly',
            'invoice_due_days' => '30',
            'auto_generate_invoices' => true,
            'send_invoice_reminders' => true,
            'reminder_days_before' => '7',

            // WhatsApp Settings
            'whatsapp_enabled' => false,
            'whatsapp_api_url' => '',
            'whatsapp_token' => '',
            'whatsapp_phone_number' => '',

            // Email Settings
            'email_notifications' => true,
            'admin_email' => '<EMAIL>',
            'from_email' => '<EMAIL>',
            'from_name' => 'Water Management System',

            // System Settings
            'maintenance_mode' => false,
            'allow_registration' => false,
            'session_timeout' => '120',
            'max_file_upload_size' => '10',
        ];

        foreach ($defaults as $key => $value) {
            $this->settings[$key] = $value;
        }
    }

    public function render()
    {
        $settingGroups = SystemSetting::all()->groupBy('group');

        return view('livewire.admin.system-settings', [
            'settingGroups' => $settingGroups,
        ]);
    }
}
