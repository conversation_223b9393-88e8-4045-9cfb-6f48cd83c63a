<?php

namespace App\Livewire\Resident;

use App\Models\Contact;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ContactProfile extends Component
{
    public $contactId;

    public $first_name = '';

    public $last_name = '';

    public $middle_name = '';

    public $email = '';

    public $phone = '';

    public $whatsapp_number = '';

    public $id_number = '';

    public $date_of_birth = '';

    public $occupation = '';

    public $company = '';

    public $postal_address = '';

    public $emergency_contact_name = '';

    public $emergency_contact_phone = '';

    public $notes = '';

    public $receive_invoices = true;

    public $receive_notifications = true;

    public $showEditModal = false;

    public $isEditing = false;

    protected $rules = [
        'first_name' => 'required|string|max:100',
        'last_name' => 'required|string|max:100',
        'middle_name' => 'nullable|string|max:100',
        'email' => 'nullable|email|max:255',
        'phone' => 'nullable|string|max:50',
        'whatsapp_number' => 'nullable|string|max:50',
        'id_number' => 'nullable|string|max:50',
        'date_of_birth' => 'nullable|date',
        'occupation' => 'nullable|string|max:100',
        'company' => 'nullable|string|max:255',
        'postal_address' => 'nullable|string|max:500',
        'emergency_contact_name' => 'nullable|string|max:255',
        'emergency_contact_phone' => 'nullable|string|max:50',
        'notes' => 'nullable|string',
        'receive_invoices' => 'boolean',
        'receive_notifications' => 'boolean',
    ];

    public function mount()
    {
        $this->loadContact();
    }

    public function loadContact()
    {
        $user = Auth::user();
        if (! $user) {
            return;
        }

        // Pre-populate with user's information
        $this->first_name = $user->name ? explode(' ', $user->name)[0] : '';
        $this->last_name = $user->name ? explode(' ', $user->name)[1] ?? '' : '';
        $this->email = $user->email;

        // Find the primary contact for this user's house
        $house = $user->house;
        if ($house) {
            $contact = Contact::where('house_id', $house->id)
                ->where('is_primary', true)
                ->first();

            if (! $contact) {
                // If no primary contact, get any contact for this house
                $contact = Contact::where('house_id', $house->id)->first();
            }

            if ($contact) {
                $this->contactId = $contact->id;
                // Override with contact data if it exists and is more detailed
                $this->first_name = $contact->first_name ?: $this->first_name;
                $this->last_name = $contact->last_name ?: $this->last_name;
                $this->middle_name = $contact->middle_name;
                $this->email = $contact->email ?: $this->email;
                $this->phone = $contact->phone;
                $this->whatsapp_number = $contact->whatsapp_number;
                $this->id_number = $contact->id_number;
                $this->date_of_birth = $contact->date_of_birth?->format('Y-m-d');
                $this->occupation = $contact->occupation;
                $this->company = $contact->company;
                $this->postal_address = $contact->postal_address;
                $this->emergency_contact_name = $contact->emergency_contact_name;
                $this->emergency_contact_phone = $contact->emergency_contact_phone;
                $this->notes = $contact->notes;
                $this->receive_invoices = $contact->receive_invoices;
                $this->receive_notifications = $contact->receive_notifications;
            }
        }
    }

    public function render()
    {
        $user = Auth::user();
        $userHouse = $user ? $user->house : null;
        $estate = $userHouse ? $userHouse->estate : null;

        return view('livewire.resident.contact-profile', [
            'user' => $user,
            'userHouse' => $userHouse,
            'estate' => $estate,
            'hasContact' => ! empty($this->contactId),
        ]);
    }

    public function edit()
    {
        $this->isEditing = true;
        $this->showEditModal = true;
    }

    public function save()
    {
        $this->validate();

        $user = Auth::user();
        if (! $user || ! $user->house) {
            session()->flash('error', 'You must be assigned to a house to update contact information.');

            return;
        }

        if ($this->contactId) {
            $contact = Contact::findOrFail($this->contactId);

            // Verify contact belongs to user's house
            if ($contact->house_id !== $user->house->id) {
                session()->flash('error', 'You can only edit your own contact information.');

                return;
            }

            $data = [
                'first_name' => $this->first_name,
                'last_name' => $this->last_name,
                'middle_name' => $this->middle_name,
                'name' => trim($this->first_name.' '.$this->last_name),
                'email' => $this->email,
                'phone' => $this->phone,
                'whatsapp_number' => $this->whatsapp_number,
                'id_number' => $this->id_number,
                'date_of_birth' => $this->date_of_birth,
                'occupation' => $this->occupation,
                'company' => $this->company,
                'postal_address' => $this->postal_address,
                'emergency_contact_name' => $this->emergency_contact_name,
                'emergency_contact_phone' => $this->emergency_contact_phone,
                'notes' => $this->notes,
                'receive_invoices' => $this->receive_invoices,
                'receive_notifications' => $this->receive_notifications,
            ];

            $contact->update($data);
            session()->flash('message', 'Contact information updated successfully.');
        } else {
            // Create new contact if none exists
            $data = [
                'house_id' => $user->house->id,
                'first_name' => $this->first_name,
                'last_name' => $this->last_name,
                'middle_name' => $this->middle_name,
                'name' => trim($this->first_name.' '.$this->last_name),
                'email' => $this->email,
                'phone' => $this->phone,
                'whatsapp_number' => $this->whatsapp_number,
                'type' => 'tenant', // Default to tenant for resident-created contacts
                'id_number' => $this->id_number,
                'date_of_birth' => $this->date_of_birth,
                'occupation' => $this->occupation,
                'company' => $this->company,
                'postal_address' => $this->postal_address,
                'emergency_contact_name' => $this->emergency_contact_name,
                'emergency_contact_phone' => $this->emergency_contact_phone,
                'notes' => $this->notes,
                'is_primary' => true, // Make this the primary contact
                'receive_invoices' => $this->receive_invoices,
                'receive_notifications' => $this->receive_notifications,
                'is_active' => true,
            ];

            $contact = Contact::create($data);
            $this->contactId = $contact->id;
            session()->flash('message', 'Contact information created successfully.');
        }

        $this->showEditModal = false;
        $this->isEditing = false;
    }

    public function cancel()
    {
        $this->showEditModal = false;
        $this->isEditing = false;
        // Reload original data
        $this->loadContact();
    }
}
