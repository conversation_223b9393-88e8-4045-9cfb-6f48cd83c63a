<?php

namespace App\Livewire\Resident;

use App\Models\House;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Navigation extends Component
{
    public $selectedHouseId;

    public $userHouses = [];

    public $currentHouse;

    protected $listeners = ['houseSelected' => 'selectHouse'];

    public function mount()
    {
        $this->loadUserHouses();
        $this->selectDefaultHouse();
    }

    private function loadUserHouses()
    {
        $user = Auth::user();
        $this->userHouses = $user->contacts()
            ->with('house.estate')
            ->get()
            ->map(function ($contact) {
                return [
                    'id' => $contact->house_id,
                    'house_number' => $contact->house->house_number,
                    'estate_name' => $contact->house->estate->name,
                    'address' => $contact->house->address,
                ];
            })
            ->toArray();
    }

    private function selectDefaultHouse()
    {
        $sessionHouseId = session('resident_selected_house_id');

        if ($sessionHouseId && collect($this->userHouses)->contains('id', $sessionHouseId)) {
            $this->selectedHouseId = $sessionHouseId;
        } elseif (! empty($this->userHouses)) {
            $this->selectedHouseId = $this->userHouses[0]['id'];
        }

        if ($this->selectedHouseId) {
            $this->currentHouse = House::with('estate')->find($this->selectedHouseId);
        }
    }

    public function selectHouse($houseId)
    {
        if (collect($this->userHouses)->contains('id', $houseId)) {
            $this->selectedHouseId = $houseId;
            $this->currentHouse = House::with('estate')->find($houseId);
            session()->put('resident_selected_house_id', $houseId);
            $this->dispatch('houseSelected', houseId: $houseId);
        }
    }

    public function render()
    {
        return view('livewire.resident.navigation');
    }
}
