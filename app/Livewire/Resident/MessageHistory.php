<?php

namespace App\Livewire\Resident;

use App\Models\House;
use App\Models\WhatsAppMessage;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class MessageHistory extends Component
{
    use WithPagination;

    public $selectedHouseId;

    public $userHouses = [];

    public $currentHouse;

    public $directionFilter = 'all';

    public $statusFilter = 'all';

    public $searchTerm = '';

    public $showFilters = false;

    protected $listeners = ['houseSelected' => 'selectHouse'];

    public function mount()
    {
        if (! Auth::check() || ! Auth::user()->isResident()) {
            return redirect()->route('resident.login');
        }

        $this->loadUserHouses();
        $this->selectDefaultHouse();
    }

    private function loadUserHouses()
    {
        $user = Auth::user();
        $this->userHouses = $user->contacts()
            ->with('house.estate')
            ->get()
            ->map(function ($contact) {
                return [
                    'id' => $contact->house_id,
                    'house_number' => $contact->house->house_number,
                    'estate_name' => $contact->house->estate->name,
                    'address' => $contact->house->address,
                ];
            })
            ->toArray();
    }

    private function selectDefaultHouse()
    {
        $sessionHouseId = session('resident_selected_house_id');

        if ($sessionHouseId && collect($this->userHouses)->contains('id', $sessionHouseId)) {
            $this->selectedHouseId = $sessionHouseId;
        } elseif (! empty($this->userHouses)) {
            $this->selectedHouseId = $this->userHouses[0]['id'];
        }

        if ($this->selectedHouseId) {
            $this->currentHouse = House::with('estate')->find($this->selectedHouseId);
        }
    }

    public function selectHouse($houseId)
    {
        if (collect($this->userHouses)->contains('id', $houseId)) {
            $this->selectedHouseId = $houseId;
            $this->currentHouse = House::with('estate')->find($houseId);
            session()->put('resident_selected_house_id', $houseId);
            $this->resetPage();
        }
    }

    public function updatedSelectedHouseId($houseId)
    {
        $this->selectHouse($houseId);
    }

    public function updatedDirectionFilter($value)
    {
        $this->resetPage();
    }

    public function updatedStatusFilter($value)
    {
        $this->resetPage();
    }

    public function updatedSearchTerm($value)
    {
        $this->resetPage();
    }

    public function getMessagesProperty()
    {
        $query = WhatsAppMessage::where('house_id', $this->selectedHouseId)
            ->with(['sender']);

        // Apply direction filter
        if ($this->directionFilter !== 'all') {
            if ($this->directionFilter === 'incoming') {
                $query->where('direction', 'incoming');
            } elseif ($this->directionFilter === 'outgoing') {
                $query->where('direction', 'outgoing');
            }
        }

        // Apply status filter (read/unread for incoming messages only)
        if ($this->statusFilter !== 'all') {
            // Status filter only makes sense for incoming messages
            $query->where('direction', 'incoming');

            if ($this->statusFilter === 'read') {
                $query->where('status', 'read');
            } elseif ($this->statusFilter === 'unread') {
                $query->where('status', '!=', 'read');
            }
        }

        // Apply search
        if ($this->searchTerm) {
            $query->where(function ($query) {
                $query->where('content', 'like', '%'.$this->searchTerm.'%')
                    ->orWhere('recipient', 'like', '%'.$this->searchTerm.'%');
            });
        }

        $result = $query->orderBy('created_at', 'desc')->paginate(15);

        // Debug: Log the query and results for troubleshooting
        if (app()->environment('local')) {
            \Log::info('MessageHistory Query', [
                'house_id' => $this->selectedHouseId,
                'direction_filter' => $this->directionFilter,
                'status_filter' => $this->statusFilter,
                'search_term' => $this->searchTerm,
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings(),
                'results_count' => $result->count(),
                'total_results' => $result->total(),
            ]);
        }

        return $result;
    }

    public function getMessageStatsProperty()
    {
        $query = WhatsAppMessage::where('house_id', $this->selectedHouseId);

        $totalMessages = $query->count();
        $incomingMessages = (clone $query)->incoming()->count();
        $outgoingMessages = (clone $query)->outgoing()->count();
        $unreadMessages = (clone $query)->incoming()->where('status', '!=', 'read')->count();

        return [
            'total_messages' => $totalMessages,
            'incoming_messages' => $incomingMessages,
            'outgoing_messages' => $outgoingMessages,
            'unread_messages' => $unreadMessages,
        ];
    }

    public function markAsRead($messageId)
    {
        $message = WhatsAppMessage::where('id', $messageId)
            ->where('house_id', $this->selectedHouseId)
            ->first();

        if ($message && $message->direction === 'incoming') {
            $message->update(['status' => 'read', 'read_at' => now()]);
            $this->dispatch('messageRead');
        }
    }

    public function markAllAsRead()
    {
        WhatsAppMessage::where('house_id', $this->selectedHouseId)
            ->where('direction', 'incoming')
            ->where('status', '!=', 'read')
            ->update(['status' => 'read', 'read_at' => now()]);

        $this->dispatch('allMessagesRead');
    }

    public function toggleShowFilters()
    {
        $this->showFilters = ! $this->showFilters;
    }

    public function resetFilters()
    {
        $this->directionFilter = 'all';
        $this->statusFilter = 'all';
        $this->searchTerm = '';
        $this->showFilters = false;
        $this->resetPage();
    }

    public function debugFilters()
    {
        // This method can be called from the browser to debug filter issues
        $query = WhatsAppMessage::where('house_id', $this->selectedHouseId);

        $debug = [
            'selected_house_id' => $this->selectedHouseId,
            'direction_filter' => $this->directionFilter,
            'status_filter' => $this->statusFilter,
            'search_term' => $this->searchTerm,
            'total_messages_in_house' => $query->count(),
            'incoming_messages' => (clone $query)->where('direction', 'incoming')->count(),
            'outgoing_messages' => (clone $query)->where('direction', 'outgoing')->count(),
            'read_incoming' => (clone $query)->where('direction', 'incoming')->where('status', 'read')->count(),
            'unread_incoming' => (clone $query)->where('direction', 'incoming')->where('status', '!=', 'read')->count(),
        ];

        // Log the debug info
        \Log::info('MessageHistory Debug', $debug);

        // Return the debug info to be displayed in the browser
        return $debug;
    }

    public function getDirectionColor($direction)
    {
        return [
            'incoming' => 'text-blue-600 bg-blue-100',
            'outgoing' => 'text-green-600 bg-green-100',
        ][$direction] ?? 'text-gray-600 bg-gray-100';
    }

    public function getStatusColor($status)
    {
        return [
            'read' => 'text-green-600 bg-green-100',
            'unread' => 'text-red-600 bg-red-100',
        ][$status] ?? 'text-gray-600 bg-gray-100';
    }

    public function render()
    {
        return view('livewire.resident.message-history')->layout('layouts.resident');
    }
}
