<?php

namespace App\Livewire\Resident\Auth;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class Login extends Component
{
    public $email = '';

    public $password = '';

    public $remember = false;

    public $houseNumber = '';

    public $showHouseNumberField = false;

    protected $rules = [
        'email' => 'required|email',
        'password' => 'required|min:6',
        'houseNumber' => 'required_if:showHouseNumberField,true|exists:houses,house_number',
    ];

    public function mount()
    {
        // Redirect if already logged in as resident
        if (Auth::check() && Auth::user()->isResident()) {
            return redirect()->route('resident.dashboard');
        }
    }

    public function authenticate()
    {
        $this->validate();

        // First, try to find user by email
        $user = User::where('email', $this->email)->first();

        if (! $user) {
            $this->addError('email', 'No account found with this email address.');

            return;
        }

        // Check if user is a resident
        if (! $user->isResident()) {
            $this->addError('email', 'This account is not registered as a resident. Please use the staff login.');

            return;
        }

        // Verify password
        if (! Hash::check($this->password, $user->password)) {
            $this->addError('password', 'Incorrect password.');

            return;
        }

        // Check if user has associated contacts (houses)
        $contacts = $user->contacts()->with('house')->get();
        if ($contacts->isEmpty()) {
            $this->addError('email', 'No houses associated with this resident account. Please contact management.');

            return;
        }

        // If user has multiple houses, show house number selection
        if ($contacts->count() > 1 && ! $this->showHouseNumberField) {
            $this->showHouseNumberField = true;

            return;
        }

        // If house number is provided, verify it belongs to the user
        if ($this->showHouseNumberField && $this->houseNumber) {
            $validHouse = $contacts->contains(function ($contact) {
                return $contact->house->house_number === $this->houseNumber;
            });

            if (! $validHouse) {
                $this->addError('houseNumber', 'This house number is not associated with your account.');

                return;
            }
        }

        // Log the user in
        Auth::login($user, $this->remember);

        // Store selected house in session if multiple houses
        if ($this->showHouseNumberField && $this->houseNumber) {
            $selectedContact = $contacts->first(function ($contact) {
                return $contact->house->house_number === $this->houseNumber;
            });
            session()->put('resident_selected_house_id', $selectedContact->house_id);
        } elseif ($contacts->count() === 1) {
            session()->put('resident_selected_house_id', $contacts->first()->house_id);
        }

        // Redirect to resident dashboard
        return redirect()->route('resident.dashboard');
    }

    public function getAvailableHousesProperty()
    {
        if (! $this->email) {
            return [];
        }

        $user = User::where('email', $this->email)->first();
        if (! $user || ! $user->isResident()) {
            return [];
        }

        return $user->contacts()->with('house.estate')->get()->map(function ($contact) {
            return [
                'house_number' => $contact->house->house_number,
                'estate_name' => $contact->house->estate->name,
                'address' => $contact->house->address,
            ];
        });
    }

    public function render()
    {
        return view('livewire.resident.auth.login')->layout('layouts.guest');
    }
}
