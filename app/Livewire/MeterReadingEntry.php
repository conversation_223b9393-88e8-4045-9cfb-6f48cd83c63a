<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\House;
use App\Models\MeterReading;
use App\Services\ReadingValidationService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;

class MeterReadingEntry extends Component
{
    use WithFileUploads;

    public $estateId = null;

    public $houseId = null;

    public $currentReading = '';

    public $photo = null;

    public $notes = '';

    public $latitude = null;

    public $longitude = null;

    public $accuracy = null;

    public $estates = [];

    public $houses = [];

    public $previousReading = null;

    public $houseDetails = null;

    public $editingReading = null;

    public $isEditMode = false;

    public $validationResults = null;

    public $showValidationModal = false;

    protected $rules = [
        'estateId' => 'required|exists:estates,id',
        'houseId' => 'required|exists:houses,id',
        'currentReading' => 'required|numeric|min:0',
        'photo' => 'nullable|image|max:2048',
        'notes' => 'nullable|string|max:500',
        'latitude' => 'nullable|numeric|between:-90,90',
        'longitude' => 'nullable|numeric|between:-180,180',
        'accuracy' => 'nullable|numeric|min:0',
    ];

    public function mount($reading = null)
    {
        $this->estates = Estate::whereHas('houses')->orderBy('name')->get();

        // Check if we're editing an existing reading
        if ($reading) {
            $this->editingReading = MeterReading::findOrFail($reading);
            if ($this->editingReading->user_id !== Auth::id() || $this->editingReading->status !== 'draft') {
                abort(403, 'Unauthorized to edit this reading');
            }

            $this->isEditMode = true;
            $this->estateId = $this->editingReading->house->estate_id;
            $this->houseId = $this->editingReading->house_id;
            $this->currentReading = $this->editingReading->current_reading;
            $this->notes = $this->editingReading->notes;
            $this->latitude = $this->editingReading->latitude;
            $this->longitude = $this->editingReading->longitude;
            $this->accuracy = $this->editingReading->accuracy;

            // Load houses for the selected estate
            $this->houses = House::where('estate_id', $this->estateId)
                ->where('is_active', true)
                ->orderBy('house_number')
                ->get();

            // Set house details and previous reading
            $this->houseDetails = $this->editingReading->house;
            $this->previousReading = $this->editingReading->previous_reading;
        }

        // Auto-detect location
        $this->dispatch('detect-location');
    }

    public function updatedEstateId($value)
    {
        $this->houseId = null;
        $this->houses = House::where('estate_id', $value)
            ->where('is_active', true)
            ->orderBy('house_number')
            ->get();
        $this->resetHouseDetails();
    }

    public function updatedHouseId($value)
    {
        if ($value) {
            $house = House::find($value);
            $this->houseDetails = $house;

            // Get last reading for this house
            $lastReading = MeterReading::where('house_id', $value)
                ->where('status', 'approved')
                ->latest('reading_date')
                ->first();

            $this->previousReading = $lastReading ? $lastReading->current_reading : $house->initial_reading;
        } else {
            $this->resetHouseDetails();
        }
    }

    public function setLocation($latitude, $longitude, $accuracy)
    {
        $this->latitude = $latitude;
        $this->longitude = $longitude;
        $this->accuracy = $accuracy;
    }

    public function saveReading()
    {
        $this->validate();

        // Validate reading is greater than previous
        if ($this->currentReading <= $this->previousReading) {
            $this->addError('currentReading', 'Current reading must be greater than previous reading ('.$this->previousReading.')');

            return;
        }

        if ($this->isEditMode) {
            // Update existing reading
            $photoPath = $this->editingReading->photo_path;
            if ($this->photo) {
                // Delete old photo if exists
                if ($this->editingReading->photo_path) {
                    \Storage::disk('public')->delete($this->editingReading->photo_path);
                }
                $photoPath = $this->photo->store('meter-readings', 'public');
            }

            $this->editingReading->update([
                'current_reading' => $this->currentReading,
                'consumption' => $this->currentReading - $this->previousReading,
                'photo_path' => $photoPath,
                'notes' => $this->notes,
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
                'accuracy' => $this->accuracy,
            ]);

            session()->flash('message', 'Reading updated successfully!');
        } else {
            // Check for existing draft reading
            $existingDraft = MeterReading::where('house_id', $this->houseId)
                ->where('user_id', Auth::id())
                ->where('status', 'draft')
                ->first();

            if ($existingDraft) {
                $this->addError('houseId', 'You already have a draft reading for this house. Please submit or delete it first.');

                return;
            }

            $photoPath = null;
            if ($this->photo) {
                $photoPath = $this->photo->store('meter-readings', 'public');
            }

            $reading = MeterReading::create([
                'house_id' => $this->houseId,
                'user_id' => Auth::id(),
                'reading_date' => now(),
                'previous_reading' => $this->previousReading,
                'current_reading' => $this->currentReading,
                'consumption' => $this->currentReading - $this->previousReading,
                'photo_path' => $photoPath,
                'notes' => $this->notes,
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
                'accuracy' => $this->accuracy,
                'status' => 'draft',
            ]);

            session()->flash('message', 'Reading saved as draft. You can submit it for review when ready.');
        }

        if (! $this->isEditMode) {
            $this->reset(['currentReading', 'photo', 'notes']);
            $this->resetHouseDetails();
        }
    }

    public function submitForReview($readingId)
    {
        $reading = MeterReading::findOrFail($readingId);

        if ($reading->user_id !== Auth::id()) {
            abort(403);
        }

        // Perform validation before submission
        $validationService = new ReadingValidationService;
        $validationResults = $validationService->validateReading($reading);

        // Store validation results
        $reading->validation_results = $validationResults;
        $reading->validation_status = $validationResults['risk_level'];
        $reading->confidence_score = $validationResults['confidence_score'];
        $reading->validated_at = now();
        $reading->validated_by = auth()->id();

        // Auto-approve if validation passes with high confidence
        if ($validationResults['is_valid'] && $validationResults['risk_level'] === 'low' && $validationResults['confidence_score'] >= 80) {
            $reading->status = 'approved';
            $reading->approved_at = now();
            $reading->approved_by = auth()->id();
            session()->flash('message', 'Reading submitted and auto-approved successfully!');
        } else {
            $reading->status = 'submitted';
            $reading->submitted_at = now();
            session()->flash('message', 'Reading submitted for review. Validation flags detected.');
        }

        $reading->save();

        // Show validation results if there are issues
        if (! $validationResults['is_valid'] || $validationResults['risk_level'] !== 'low') {
            $this->validationResults = $validationResults;
            $this->showValidationModal = true;
        }
    }

    public function deleteDraft($readingId)
    {
        $reading = MeterReading::findOrFail($readingId);

        if ($reading->user_id !== Auth::id() || $reading->status !== 'draft') {
            abort(403);
        }

        if ($reading->photo_path) {
            \Storage::disk('public')->delete($reading->photo_path);
        }

        $reading->delete();
        session()->flash('message', 'Draft reading deleted successfully!');
    }

    public function delete($readingId)
    {
        $reading = MeterReading::findOrFail($readingId);

        // Check if user has permission to delete this reading
        if (! Auth::user()->can('readings.delete_all') &&
            (! Auth::user()->can('readings.delete_assigned') || $reading->house->estate_id !== Auth::user()->estate_id)) {
            abort(403);
        }

        // Check if reading is used in invoices
        if ($reading->invoices()->count() > 0) {
            session()->flash('error', 'Cannot delete reading that is used in invoices.');

            return;
        }

        if ($reading->photo_path) {
            \Storage::disk('public')->delete($reading->photo_path);
        }

        $reading->delete();
        session()->flash('message', 'Reading deleted successfully!');
    }

    public function closeValidationModal()
    {
        $this->showValidationModal = false;
        $this->validationResults = null;
    }

    public function getValidationSummaryProperty()
    {
        if (! $this->validationResults) {
            return null;
        }

        return [
            'is_valid' => $this->validationResults['is_valid'],
            'risk_level' => $this->validationResults['risk_level'],
            'confidence_score' => $this->validationResults['confidence_score'],
            'anomalies_count' => count($this->validationResults['anomalies']),
            'warnings_count' => count($this->validationResults['warnings']),
            'critical_anomalies' => collect($this->validationResults['anomalies'])->where('severity', 'critical')->count(),
            'high_anomalies' => collect($this->validationResults['anomalies'])->where('severity', 'high')->count(),
            'medium_anomalies' => collect($this->validationResults['anomalies'])->where('severity', 'medium')->count(),
        ];
    }

    public function getDraftReadingsProperty()
    {
        return MeterReading::with(['house.estate'])
            ->where('user_id', Auth::id())
            ->where('status', 'draft')
            ->latest('created_at')
            ->get();
    }

    private function resetHouseDetails()
    {
        $this->houseDetails = null;
        $this->previousReading = null;
    }

    public function render()
    {
        return view('livewire.meter-reading-entry');
    }
}
