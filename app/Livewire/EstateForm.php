<?php

namespace App\Livewire;

use App\Models\Estate;
use Livewire\Component;

class EstateForm extends Component
{
    public Estate $estate;

    protected $rules = [
        'estate.name' => 'required|string|min:3',
    ];

    public function mount(Estate $estate)
    {
        $this->estate = $estate;
    }

    public function save()
    {
        $this->validate();

        $this->estate->save();

        return redirect()->to('/estates');
    }

    public function render()
    {
        return view('livewire.estate-form');
    }
}
