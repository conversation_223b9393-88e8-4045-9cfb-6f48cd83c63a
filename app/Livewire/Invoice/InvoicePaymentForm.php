<?php

namespace App\Livewire\Invoice;

use App\Models\Invoice;
use App\Models\InvoicePayment;
use Livewire\Component;

class InvoicePaymentForm extends Component
{
    public Invoice $invoice;

    public float $amount = 0.0;

    public string $payment_date = '';

    public string $description = '';

    public string $payment_method = 'cash';

    public string $reference_number = '';

    protected function rules(): array
    {
        return [
            'amount' => ['required', 'numeric', 'min:0.01', 'max:'.$this->invoice->balance_due],
            'payment_date' => ['required', 'date', 'before_or_equal:today'],
            'description' => ['required', 'string', 'max:255'],
            'payment_method' => ['required', 'in:cash,bank_transfer,mpesa,cheque'],
            'reference_number' => ['nullable', 'string', 'max:100'],
        ];
    }

    public function mount(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->payment_date = now()->format('Y-m-d');
    }

    public function savePayment()
    {
        $this->validate();

        $payment = InvoicePayment::create([
            'invoice_id' => $this->invoice->id,
            'amount' => $this->amount,
            'payment_date' => $this->payment_date,
            'description' => $this->description,
            'payment_method' => $this->payment_method,
            'reference_number' => $this->reference_number,
        ]);

        $this->invoice->refresh();
        $this->invoice->updateStatusBasedOnPayments();

        $this->reset(['amount', 'description', 'reference_number']);

        $this->dispatch('payment-added', paymentId: $payment->id);
        $this->dispatch('close-modal');

        session()->flash('success', 'Payment recorded successfully.');
    }

    public function render()
    {
        return view('livewire.invoice.invoice-payment-form');
    }
}
