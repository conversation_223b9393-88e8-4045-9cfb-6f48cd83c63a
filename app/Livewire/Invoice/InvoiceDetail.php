<?php

namespace App\Livewire\Invoice;

use App\Models\Invoice;
use App\Services\WhatsAppService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class InvoiceDetail extends Component
{
    public Invoice $invoice;

    public $showPaymentModal = false;

    public $showAdjustmentModal = false;

    public $modalState = null;

    // Payment form fields
    public $paymentAmount = '';

    public $paymentDate = '';

    public $paymentMethod = 'cash';

    public $paymentNotes = '';

    public $paymentReference = '';

    // Adjustment form fields
    public $adjustmentAmount = '';

    public $adjustmentType = 'discount';

    public $adjustmentDescription = '';

    public $adjustmentDate = '';

    public $rejectionReason = '';

    public function mount(Invoice $invoice)
    {
        $this->invoice = $invoice->load(['house.estate', 'meterReading', 'waterRate', 'lineItems', 'payments', 'adjustments']);
        $this->paymentDate = now()->format('Y-m-d');
        $this->adjustmentDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.invoice.invoice-detail');
    }

    public function downloadPdf()
    {
        try {
            $pdfService = app(\App\Services\PdfGenerationService::class);

            return $pdfService->downloadInvoicePdf($this->invoice);
        } catch (\Exception $e) {
            $this->dispatch('error', $e->getMessage());

            return;
        }
    }

    public function regeneratePdf()
    {
        try {
            $pdfService = app(\App\Services\PdfGenerationService::class);
            $pdfService->regenerateInvoicePdf($this->invoice);
            $this->invoice->refresh();
            $this->dispatch('success', 'PDF regenerated successfully');
        } catch (\Exception $e) {
            $this->dispatch('error', 'Failed to regenerate PDF: '.$e->getMessage());
        }
    }

    public function sendWhatsApp()
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            $whatsAppService->sendInvoiceNotification($this->invoice);

            $this->invoice->update(['sent_at' => now()]);
            $this->invoice->refresh();

            $this->dispatch('success', 'Invoice sent via WhatsApp successfully');
        } catch (\Exception $e) {
            $this->dispatch('error', 'Failed to send WhatsApp message: '.$e->getMessage());
        }
    }

    public function markAsPaid()
    {
        if ($this->invoice->status === 'paid') {
            $this->dispatch('error', 'Invoice is already marked as paid');

            return;
        }

        $this->invoice->markAsPaid();
        $this->invoice->refresh();

        $this->dispatch('success', 'Invoice marked as paid successfully');
    }

    public function openPaymentModal()
    {
        $this->modalState = 'payment';
        $this->paymentAmount = $this->invoice->balance_due;
        $this->paymentDate = now()->format('Y-m-d');
        $this->paymentMethod = 'cash';
        $this->paymentNotes = '';
    }

    public function openAdjustmentModal()
    {
        $this->modalState = 'adjustment';
        $this->adjustmentAmount = '';
        $this->adjustmentType = 'discount';
        $this->adjustmentDescription = '';
        $this->adjustmentDate = now()->format('Y-m-d');
    }

    public function closeModal()
    {
        $this->modalState = null;
    }

    public function savePayment()
    {
        $this->validate([
            'paymentAmount' => 'required|numeric|min:0.01|max:'.$this->invoice->getBalanceDueAttribute(),
            'paymentDate' => 'required|date',
            'paymentMethod' => 'required|string|in:cash,mpesa,bank_transfer,cheque,online',
            'paymentNotes' => 'nullable|string|max:500',
        ]);

        try {
            $this->invoice->recordPayment(
                $this->paymentAmount,
                $this->paymentMethod,
                $this->paymentDate,
                $this->paymentReference,
                $this->paymentNotes
            );

            $this->closeModal();
            $this->invoice->refresh();
            $this->dispatch('success', 'Payment recorded successfully');
        } catch (\Exception $e) {
            $this->dispatch('error', 'Failed to record payment: '.$e->getMessage());
        }
    }

    public function saveAdjustment()
    {
        $this->validate([
            'adjustmentAmount' => 'required|numeric|min:0.01',
            'adjustmentType' => 'required|string|in:discount,penalty,correction,other',
            'adjustmentDescription' => 'required|string|max:500',
            'adjustmentDate' => 'required|date',
        ]);

        try {
            $amount = $this->adjustmentAmount;
            $type = $this->adjustmentType === 'discount' ? 'credit' : 'debit';

            if ($this->adjustmentType === 'penalty') {
                $type = 'debit';
            } elseif ($this->adjustmentType === 'correction') {
                $type = 'correction';
            } else {
                $type = 'credit';
            }

            $this->invoice->addAdjustment(
                $amount,
                $type,
                $this->adjustmentDescription,
                $this->adjustmentDate,
                $this->adjustmentType
            );

            $this->closeModal();
            $this->invoice->refresh();
            $this->dispatch('success', 'Adjustment added successfully');
        } catch (\Exception $e) {
            $this->dispatch('error', 'Failed to add adjustment: '.$e->getMessage());
        }
    }

    public function submitForApproval()
    {
        if (! $this->invoice->canBeSubmitted()) {
            $this->dispatch('error', 'Invoice cannot be submitted for approval');

            return;
        }

        $this->authorize('submitForApproval', $this->invoice);

        $this->invoice->submitForApproval(Auth::user());
        $this->invoice->refresh();
        $this->dispatch('success', 'Invoice submitted for approval successfully');
    }

    public function approve()
    {
        if (! $this->invoice->canBeApproved()) {
            $this->dispatch('error', 'Invoice cannot be approved');

            return;
        }

        $this->authorize('approve', $this->invoice);

        $this->invoice->approve(Auth::user());
        $this->invoice->refresh();
        $this->dispatch('success', 'Invoice approved successfully');
    }

    public function reject()
    {
        $this->validate([
            'rejectionReason' => 'required|string|min:3',
        ]);

        if ($this->invoice->status !== 'submitted') {
            $this->dispatch('error', 'Only submitted invoices can be rejected');

            return;
        }

        $this->authorize('reject', $this->invoice);

        $this->invoice->reject(Auth::user(), $this->rejectionReason);

        // Log the rejection
        activity()
            ->performedOn($this->invoice)
            ->causedBy(Auth::user())
            ->withProperties(['reason' => $this->rejectionReason])
            ->log('Invoice rejected');

        $this->rejectionReason = '';
        $this->invoice->refresh();
        $this->dispatch('success', 'Invoice rejected successfully');
    }

    public function sendInvoice()
    {
        if (! $this->invoice->canBeSent()) {
            $this->dispatch('error', 'Invoice cannot be sent');

            return;
        }

        $this->authorize('send', $this->invoice);

        $this->invoice->markAsSent();
        $this->invoice->refresh();
        $this->dispatch('success', 'Invoice sent successfully');
    }
}
