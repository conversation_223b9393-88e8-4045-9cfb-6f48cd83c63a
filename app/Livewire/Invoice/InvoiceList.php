<?php

namespace App\Livewire\Invoice;

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class InvoiceList extends Component
{
    use WithPagination;

    public string $status = '';

    public string $estate_id = '';

    public string $house_id = '';

    public string $date_from = '';

    public string $date_to = '';

    public string $search = '';

    public array $filters = [
        'status' => '',
        'estate_id' => '',
        'house_id' => '',
        'date_from' => '',
        'date_to' => '',
    ];

    protected $queryString = ['filters'];

    public function mount()
    {
        // Sync individual properties from filters array
        $this->status = $this->filters['status'] ?? '';
        $this->estate_id = $this->filters['estate_id'] ?? '';
        $this->house_id = $this->filters['house_id'] ?? '';
        $this->date_from = $this->filters['date_from'] ?? '';
        $this->date_to = $this->filters['date_to'] ?? '';
        $this->search = $this->filters['search'] ?? '';
    }

    public function render()
    {
        $invoices = Invoice::with(['house.estate', 'meterReading'])
            ->when($this->status, function ($query) {
                return $query->where('status', $this->status);
            })
            ->when($this->estate_id, function ($query) {
                return $query->whereHas('house', function ($q) {
                    $q->where('estate_id', $this->estate_id);
                });
            })
            ->when($this->house_id, function ($query) {
                return $query->where('house_id', $this->house_id);
            })
            ->when($this->date_from, function ($query) {
                return $query->where('billing_period_start', '>=', $this->date_from);
            })
            ->when($this->date_to, function ($query) {
                return $query->where('billing_period_end', '<=', $this->date_to);
            })
            ->when($this->search, function ($query) {
                return $query->where(function ($q) {
                    $q->where('invoice_number', 'like', '%'.$this->search.'%')
                        ->orWhereHas('house', function ($q) {
                            $q->where('house_number', 'like', '%'.$this->search.'%');
                        });
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $estates = Estate::all();
        $houses = collect();

        if ($this->estate_id) {
            $houses = House::where('estate_id', $this->estate_id)->get();
        }

        return view('livewire.invoice.invoice-list', [
            'invoices' => $invoices,
            'estates' => $estates,
            'houses' => $houses,
        ]);
    }

    public function updatedEstateId($value)
    {
        $this->house_id = '';
        $this->syncFilters();
    }

    public function updatedStatus($value)
    {
        $this->syncFilters();
    }

    public function updatedHouseId($value)
    {
        $this->syncFilters();
    }

    public function updatedDateFrom($value)
    {
        $this->syncFilters();
    }

    public function updatedDateTo($value)
    {
        $this->syncFilters();
    }

    public function updatedSearch($value)
    {
        $this->syncFilters();
    }

    private function syncFilters()
    {
        $this->filters = [
            'status' => $this->status,
            'estate_id' => $this->estate_id,
            'house_id' => $this->house_id,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'search' => $this->search,
        ];
    }

    public function resetFilters()
    {
        $this->reset(['status', 'estate_id', 'house_id', 'date_from', 'date_to', 'search']);
        $this->syncFilters();
    }

    public function delete($invoiceId)
    {
        $invoice = Invoice::findOrFail($invoiceId);

        // Check if user has permission to delete this invoice
        if (! Auth::user()->can('invoices.delete_all') &&
            (! Auth::user()->can('invoices.delete_assigned') || $invoice->house->estate_id !== Auth::user()->estate_id)) {
            abort(403);
        }

        // Check if invoice has payments
        if ($invoice->payments()->count() > 0) {
            session()->flash('error', 'Cannot delete invoice that has payments associated with it.');

            return;
        }

        // Check if invoice has been sent
        if ($invoice->sent_at) {
            session()->flash('error', 'Cannot delete invoice that has already been sent.');

            return;
        }

        $invoice->delete();
        session()->flash('message', 'Invoice deleted successfully!');
    }
}
