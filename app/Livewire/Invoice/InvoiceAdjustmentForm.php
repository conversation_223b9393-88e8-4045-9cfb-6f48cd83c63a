<?php

namespace App\Livewire\Invoice;

use App\Models\Invoice;
use App\Models\InvoiceAdjustment;
use Livewire\Component;

class InvoiceAdjustmentForm extends Component
{
    public Invoice $invoice;

    public float $amount = 0.0;

    public string $adjustment_date = '';

    public string $description = '';

    public string $type = 'credit';

    public string $reason = '';

    protected function rules(): array
    {
        return [
            'amount' => ['required', 'numeric', 'min:0.01'],
            'adjustment_date' => ['required', 'date', 'before_or_equal:today'],
            'description' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:credit,debit'],
            'reason' => ['required', 'string', 'max:500'],
        ];
    }

    public function mount(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->adjustment_date = now()->format('Y-m-d');
    }

    public function saveAdjustment()
    {
        $this->validate();

        $adjustment = InvoiceAdjustment::create([
            'invoice_id' => $this->invoice->id,
            'amount' => $this->amount,
            'adjustment_date' => $this->adjustment_date,
            'description' => $this->description,
            'type' => $this->type,
            'reason' => $this->reason,
        ]);

        $this->invoice->refresh();
        $this->invoice->recalculateTotal();

        $this->reset(['amount', 'description', 'reason']);

        $this->dispatch('adjustment-added', adjustmentId: $adjustment->id);
        $this->dispatch('close-modal');

        session()->flash('success', 'Adjustment recorded successfully.');
    }

    public function render()
    {
        return view('livewire.invoice.invoice-adjustment-form');
    }
}
