<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ManagementDashboard extends Component
{
    use WithPagination;

    public $selectedEstate = null;

    public $dateRange = '30'; // days

    public $chartType = 'consumption';

    public $exportFormat = 'pdf';

    protected $queryString = [
        'selectedEstate' => ['except' => ''],
        'dateRange' => ['except' => '30'],
    ];

    public function mount()
    {
        if (! Auth::user()->isManager()) {
            abort(403, 'Unauthorized access');
        }
    }

    public function getEstatesProperty()
    {
        return Estate::withCount(['houses', 'meterReadings'])
            ->orderBy('name')
            ->get();
    }

    public function getKpiDataProperty()
    {
        $query = $this->getBaseQuery();

        return [
            'total_houses' => $this->getTotalHouses(),
            'total_estates' => $this->getTotalEstates(),
            'total_revenue' => $this->getTotalRevenue($query),
            'avg_consumption' => $this->getAverageConsumption($query),
            'pending_readings' => $this->getPendingReadingsCount(),
            'overdue_invoices' => $this->getOverdueInvoicesCount(),
        ];
    }

    public function getChartDataProperty()
    {
        return match ($this->chartType) {
            'consumption' => $this->getConsumptionChartData(),
            'revenue' => $this->getRevenueChartData(),
            'estate_comparison' => $this->getEstateComparisonData(),
            default => $this->getConsumptionChartData(),
        };
    }

    public function getRecentReadingsProperty()
    {
        return MeterReading::with(['house.estate', 'house.contacts'])
            ->when($this->selectedEstate, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->where('created_at', '>=', now()->subDays($this->dateRange))
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
    }

    public function getTopConsumersProperty()
    {
        return House::with(['estate', 'meterReadings' => function ($query) {
            $query->where('created_at', '>=', now()->subDays($this->dateRange));
        }])
            ->when($this->selectedEstate, function ($query) {
                $query->where('estate_id', $this->selectedEstate);
            })
            ->get()
            ->map(function ($house) {
                $totalConsumption = $house->meterReadings->sum('consumption');

                return [
                    'house' => $house,
                    'total_consumption' => $totalConsumption,
                    'avg_daily' => $totalConsumption / max(1, $this->dateRange),
                ];
            })
            ->sortByDesc('total_consumption')
            ->take(10);
    }

    public function getEstateAnalyticsProperty()
    {
        return Estate::with(['houses.meterReadings' => function ($query) {
            $query->where('created_at', '>=', now()->subDays($this->dateRange));
        }])
            ->when($this->selectedEstate, function ($query) {
                $query->where('id', $this->selectedEstate);
            })
            ->get()
            ->map(function ($estate) {
                $totalHouses = $estate->houses->count();
                $totalConsumption = $estate->houses->flatMap->meterReadings->sum('consumption');
                $totalRevenue = $estate->houses->flatMap->invoices
                    ->where('created_at', '>=', now()->subDays($this->dateRange))
                    ->sum('amount');

                return [
                    'estate' => $estate,
                    'total_houses' => $totalHouses,
                    'total_consumption' => $totalConsumption,
                    'total_revenue' => $totalRevenue,
                    'avg_consumption_per_house' => $totalHouses > 0 ? $totalConsumption / $totalHouses : 0,
                    'occupancy_rate' => $totalHouses > 0 ? ($estate->houses->where('status', 'occupied')->count() / $totalHouses) * 100 : 0,
                ];
            });
    }

    public function exportReport()
    {
        $reportService = new \App\Services\ReportExportService;

        $data = $reportService->generateManagementReport([
            'date_range' => $this->dateRange,
            'estate_id' => $this->selectedEstate,
        ]);

        if ($this->exportFormat === 'pdf') {
            return $this->exportPdf($data);
        } elseif ($this->exportFormat === 'excel') {
            return $this->exportExcel($data);
        }
    }

    private function getBaseQuery()
    {
        $query = MeterReading::query();

        if ($this->selectedEstate) {
            $query->whereHas('house', function ($q) {
                $q->where('estate_id', $this->selectedEstate);
            });
        }

        return $query->where('created_at', '>=', now()->subDays($this->dateRange));
    }

    private function getTotalHouses()
    {
        return $this->selectedEstate
            ? House::where('estate_id', $this->selectedEstate)->count()
            : House::count();
    }

    private function getTotalEstates()
    {
        return Estate::count();
    }

    private function getTotalRevenue($query)
    {
        return Invoice::when($this->selectedEstate, function ($query) {
            $query->whereHas('house', function ($q) {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->where('created_at', '>=', now()->subDays($this->dateRange))
            ->sum('amount');
    }

    private function getAverageConsumption($query)
    {
        return $query->avg('consumption') ?? 0;
    }

    private function getPendingReadingsCount()
    {
        return MeterReading::where('status', 'pending')
            ->when($this->selectedEstate, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->count();
    }

    private function getOverdueInvoicesCount()
    {
        return Invoice::where('status', 'overdue')
            ->when($this->selectedEstate, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->count();
    }

    private function getConsumptionChartData()
    {
        $data = $this->getBaseQuery()
            ->selectRaw('DATE(created_at) as date, SUM(consumption) as total_consumption')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date')->map(fn ($date) => Carbon::parse($date)->format('M d')),
            'datasets' => [
                [
                    'label' => 'Daily Consumption (Liters)',
                    'data' => $data->pluck('total_consumption'),
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
            ],
        ];
    }

    private function getRevenueChartData()
    {
        $data = Invoice::when($this->selectedEstate, function ($query) {
            $query->whereHas('house', function ($q) {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->selectRaw('DATE(created_at) as date, SUM(amount) as total_revenue')
            ->where('created_at', '>=', now()->subDays($this->dateRange))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date')->map(fn ($date) => Carbon::parse($date)->format('M d')),
            'datasets' => [
                [
                    'label' => 'Daily Revenue (KES)',
                    'data' => $data->pluck('total_revenue'),
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'fill' => true,
                ],
            ],
        ];
    }

    private function getEstateComparisonData()
    {
        $data = Estate::with(['houses.meterReadings' => function ($query) {
            $query->where('created_at', '>=', now()->subDays($this->dateRange));
        }])
            ->get()
            ->map(function ($estate) {
                return [
                    'name' => $estate->name,
                    'consumption' => $estate->houses->flatMap->meterReadings->sum('consumption'),
                    'houses' => $estate->houses->count(),
                ];
            })
            ->sortByDesc('consumption')
            ->take(10);

        return [
            'labels' => $data->pluck('name'),
            'datasets' => [
                [
                    'label' => 'Total Consumption by Estate',
                    'data' => $data->pluck('consumption'),
                    'backgroundColor' => [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(251, 191, 36, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(168, 85, 247, 0.8)',
                    ],
                ],
            ],
        ];
    }

    private function exportPdf($data)
    {
        // Placeholder for PDF export - create a simple response
        return response()->streamDownload(function () use ($data) {
            echo json_encode($data, JSON_PRETTY_PRINT);
        }, 'report.json', ['Content-Type' => 'application/json']);
    }

    private function exportExcel($data)
    {
        // Placeholder for Excel export - create a simple response
        return response()->streamDownload(function () use ($data) {
            echo json_encode($data, JSON_PRETTY_PRINT);
        }, 'report.json', ['Content-Type' => 'application/json']);
    }

    public function render()
    {
        return view('livewire.management-dashboard', [
            'estates' => $this->estates,
            'kpiData' => $this->kpi_data,
            'chartData' => $this->chart_data,
            'recentReadings' => $this->recent_readings,
            'topConsumers' => $this->top_consumers,
            'estateAnalytics' => $this->estate_analytics,
        ]);
    }
}
