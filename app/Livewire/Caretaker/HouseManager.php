<?php

namespace App\Livewire\Caretaker;

use App\Models\Estate;
use App\Models\House;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class HouseManager extends Component
{
    use WithPagination;

    public $estateId;

    public $estateName;

    public function mount($estateId = null)
    {
        if (! Auth::user()->isCaretaker()) {
            abort(403, 'Unauthorized access');
        }

        if ($estateId) {
            $estate = Auth::user()->assignedEstates()->findOrFail($estateId);
            $this->estateId = $estate->id;
            $this->estateName = $estate->name;
        } else {
            // If no estateId is provided, default to the first assigned estate
            $firstEstate = Auth::user()->assignedEstates()->first();
            if ($firstEstate) {
                $this->estateId = $firstEstate->id;
                $this->estateName = $firstEstate->name;
            } else {
                // Handle case where no estates are assigned
                $this->estateId = null;
                $this->estateName = 'No Estates Assigned';
            }
        }
    }

    public function updatedEstateId()
    {
        $estate = Auth::user()->assignedEstates()->find($this->estateId);
        if ($estate) {
            $this->estateName = $estate->name;
        } else {
            $this->estateName = 'No Estates Assigned';
        }
        $this->resetPage();
    }

    public function getEstatesProperty()
    {
        return Auth::user()->assignedEstates()->orderBy('name')->get();
    }

    public function getHousesProperty()
    {
        if (! $this->estateId) {
            return House::where('id', null)->paginate(10); // Return empty paginator
        }

        return House::where('estate_id', $this->estateId)
            ->whereIn('id', Auth::user()->assignedHouses->pluck('id'))
            ->paginate(10);
    }

    public function render()
    {
        return view('livewire.caretaker.house-manager', [
            'houses' => $this->houses,
            'estates' => $this->estates,
        ]);
    }
}
