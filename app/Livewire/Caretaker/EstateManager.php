<?php

namespace App\Livewire\Caretaker;

use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class EstateManager extends Component
{
    use WithPagination;

    public function getEstatesProperty()
    {
        return Auth::user()->assignedEstates()->paginate(10);
    }

    public function render()
    {
        return view('livewire.caretaker.estate-manager', [
            'estates' => $this->estates,
        ]);
    }
}
