<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\House;
use App\Models\MeterReading;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithPagination;

class CaretakerDashboard extends Component
{
    use WithPagination;

    public $selectedEstate = null;

    public $selectedStatus = 'all';

    public $search = '';

    public $dateRange = 'this_month';

    public function mount()
    {
        if (! Auth::user()->isCaretaker()) {
            abort(403, 'Unauthorized access');
        }

        // Default to first estate if only one
        if (Auth::user()->assignedEstates()->count() === 1) {
            $this->selectedEstate = Auth::user()->assignedEstates()->first()->id;
        }
    }

    public function getEstatesProperty()
    {
        return Auth::user()->assignedEstates()->orderBy('name')->get();
    }

    public function getReadingsProperty()
    {
        $query = MeterReading::with(['house.estate', 'house.contacts'])
            ->where('user_id', Auth::id())
            ->when($this->selectedEstate, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->when($this->selectedStatus !== 'all', function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->search, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('house_number', 'like', '%'.$this->search.'%')
                        ->orWhere('address', 'like', '%'.$this->search.'%');
                });
            })
            ->when($this->dateRange, function ($query) {
                switch ($this->dateRange) {
                    case 'today':
                        $query->whereDate('reading_date', today());
                        break;
                    case 'this_week':
                        $query->whereBetween('reading_date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'this_month':
                        $query->whereMonth('reading_date', now()->month);
                        break;
                    case 'last_month':
                        $query->whereMonth('reading_date', now()->subMonth()->month);
                        break;
                }
            })
            ->orderBy('reading_date', 'desc');

        return $query->paginate(10);
    }

    public function getStatsProperty()
    {
        $baseQuery = MeterReading::where('user_id', Auth::id())
            ->when($this->selectedEstate, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('estate_id', $this->selectedEstate);
                });
            });

        return [
            'total_readings' => $baseQuery->count(),
            'draft_readings' => (clone $baseQuery)->where('status', 'draft')->count(),
            'submitted_readings' => (clone $baseQuery)->where('status', 'submitted')->count(),
            'approved_readings' => (clone $baseQuery)->where('status', 'approved')->count(),
            'rejected_readings' => (clone $baseQuery)->where('status', 'rejected')->count(),
            'today_readings' => (clone $baseQuery)->whereDate('reading_date', today())->count(),
        ];
    }

    public function getPendingHousesProperty()
    {
        if (! $this->selectedEstate) {
            return collect();
        }

        return House::where('estate_id', $this->selectedEstate)
            ->where('is_active', true)
            ->whereDoesntHave('meterReadings', function ($query) {
                $query->where('user_id', Auth::id())
                    ->whereMonth('reading_date', now()->month);
            })
            ->orderBy('house_number')
            ->get();
    }

    public function deleteReading($readingId)
    {
        $reading = MeterReading::findOrFail($readingId);

        if ($reading->user_id !== Auth::id() || $reading->status !== 'draft') {
            abort(403);
        }

        if ($reading->photo_path) {
            Storage::disk('public')->delete($reading->photo_path);
        }

        $reading->delete();

        session()->flash('message', 'Reading deleted successfully!');
    }

    public function submitForReview($readingId)
    {
        $reading = MeterReading::findOrFail($readingId);

        if ($reading->user_id !== Auth::id() || $reading->status !== 'draft') {
            abort(403);
        }

        $reading->update([
            'status' => 'submitted',
            'submitted_at' => now(),
        ]);

        session()->flash('message', 'Reading submitted for review successfully!');
    }

    public function render()
    {
        return view('livewire.caretaker-dashboard', [
            'readings' => $this->readings,
            'stats' => $this->stats,
            'pendingHouses' => $this->pendingHouses,
            'estates' => $this->estates,
        ]);
    }
}
