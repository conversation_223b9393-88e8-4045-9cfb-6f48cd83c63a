<?php

namespace App\Livewire;

use App\Models\MeterReading;
use App\Services\ReadingValidationService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ReviewerDashboard extends Component
{
    use WithPagination;

    public $selectedEstate = null;

    public $selectedStatus = 'submitted';

    public $riskFilter = '';

    public $search = '';

    public $dateRange = 'this_month';

    public $showReviewModal = false;

    public $currentReading = null;

    public $reviewAction = 'approve';

    public $reviewNotes = '';

    public $anomalyThreshold = 50; // units

    public $selectAll = false;

    public $selectedReadings = [];

    public $showBatchModal = false;

    public $batchAction = 'approve';

    public $batchNotes = '';

    public $validationResults = [];

    public $showValidationModal = false;

    public $sortBy = 'submitted_at';

    public $sortDirection = 'desc';

    public function mount()
    {
        if (! Auth::user()->isReviewer()) {
            abort(403, 'Unauthorized access');
        }

        // Default to submitted readings for review
        $this->selectedStatus = 'submitted';
    }

    public function getEstatesProperty()
    {
        return Auth::user()->assignedEstates()->orderBy('name')->get();
    }

    public function getReadingsProperty()
    {
        $query = MeterReading::with(['house.estate', 'user', 'house.contacts'])
            ->when($this->selectedEstate, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->when($this->selectedStatus !== 'all', function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->search, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('house_number', 'like', '%'.$this->search.'%')
                        ->orWhere('address', 'like', '%'.$this->search.'%');
                });
            })
            ->when($this->dateRange, function ($query) {
                switch ($this->dateRange) {
                    case 'today':
                        $query->whereDate('reading_date', today());
                        break;
                    case 'this_week':
                        $query->whereBetween('reading_date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'this_month':
                        $query->whereMonth('reading_date', now()->month);
                        break;
                    case 'last_month':
                        $query->whereMonth('reading_date', now()->subMonth()->month);
                        break;
                }
            })
            ->orderBy('submitted_at', 'desc');

        return $query->paginate(10);
    }

    public function getStatsProperty()
    {
        $baseQuery = MeterReading::query()
            ->when($this->selectedEstate, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('estate_id', $this->selectedEstate);
                });
            });

        return [
            'pending_review' => (clone $baseQuery)->where('status', 'submitted')->count(),
            'approved_today' => (clone $baseQuery)->where('status', 'approved')->whereDate('approved_at', today())->count(),
            'rejected_today' => (clone $baseQuery)->where('status', 'rejected')->whereDate('reviewed_at', today())->count(),
            'this_month' => (clone $baseQuery)->whereMonth('reading_date', now()->month)->count(),
        ];
    }

    public function getAnomalousReadings()
    {
        $service = new ReadingValidationService;
        $readings = MeterReading::where('status', 'submitted')
            ->when($this->selectedEstate, function ($query) {
                $query->whereHas('house', function ($q) {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->get();

        return $readings->filter(function ($reading) use ($service) {
            $validation = $service->validateReading($reading);

            return $validation['risk_level'] !== 'low';
        });
    }

    public function openReviewModal($readingId)
    {
        $this->currentReading = MeterReading::with(['house.estate', 'user'])->findOrFail($readingId);
        $this->showReviewModal = true;
    }

    public function closeReviewModal()
    {
        $this->showReviewModal = false;
        $this->currentReading = null;
        $this->reviewAction = 'approve';
        $this->reviewNotes = '';
    }

    public function submitReview()
    {
        if (! $this->currentReading) {
            return;
        }

        $this->validate([
            'reviewAction' => 'required|in:approve,reject',
            'reviewNotes' => 'required_if:reviewAction,reject|max:500',
        ]);

        $updateData = [
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
            'status' => $this->reviewAction === 'approve' ? 'approved' : 'rejected',
        ];

        if ($this->reviewAction === 'reject') {
            $updateData['rejection_reason'] = $this->reviewNotes;
        } else {
            $updateData['approved_at'] = now();
            $updateData['approved_by'] = Auth::id();
        }

        $this->currentReading->update($updateData);

        session()->flash('message', 'Reading '.$this->reviewAction.'d successfully!');
        $this->closeReviewModal();
    }

    public function getReadingHistory($houseId)
    {
        return MeterReading::where('house_id', $houseId)
            ->where('status', 'approved')
            ->orderBy('reading_date', 'desc')
            ->limit(6)
            ->get();
    }

    public function isAnomalous($reading)
    {
        $service = new ReadingValidationService;
        $validation = $service->validateReading($reading);

        return $validation['risk_level'] !== 'low';
    }

    public function getValidationResult($reading)
    {
        $service = new ReadingValidationService;

        return $service->validateReading($reading);
    }

    public function openValidationModal($readingId)
    {
        $this->currentReading = MeterReading::with(['house.estate', 'user'])->findOrFail($readingId);
        $this->validationResults = $this->getValidationResult($this->currentReading);
        $this->showValidationModal = true;
    }

    public function closeValidationModal()
    {
        $this->showValidationModal = false;
        $this->currentReading = null;
        $this->validationResults = [];
    }

    public function openBatchModal()
    {
        if (empty($this->selectedReadings)) {
            session()->flash('error', 'Please select readings to process in batch');

            return;
        }
        $this->showBatchModal = true;
    }

    public function closeBatchModal()
    {
        $this->showBatchModal = false;
        $this->batchAction = 'approve';
        $this->batchNotes = '';
    }

    public function submitBatchReview()
    {
        if (empty($this->selectedReadings)) {
            return;
        }

        $this->validate([
            'batchAction' => 'required|in:approve,reject',
            'batchNotes' => 'required_if:batchAction,reject|max:500',
        ]);

        $readings = MeterReading::whereIn('id', $this->selectedReadings)->get();

        $updateData = [
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
            'status' => $this->batchAction === 'approve' ? 'approved' : 'rejected',
        ];

        if ($this->batchAction === 'reject') {
            $updateData['rejection_reason'] = $this->batchNotes;
        } else {
            $updateData['approved_at'] = now();
            $updateData['approved_by'] = Auth::id();
        }

        foreach ($readings as $reading) {
            $reading->update($updateData);
        }

        session()->flash('message', count($this->selectedReadings).' readings '.$this->batchAction.'d successfully!');
        $this->selectedReadings = [];
        $this->closeBatchModal();
    }

    public function getRiskLevelBadgeClass($riskLevel)
    {
        return match ($riskLevel) {
            'critical' => 'bg-red-100 text-red-800',
            'high' => 'bg-orange-100 text-orange-800',
            'medium' => 'bg-yellow-100 text-yellow-800',
            'low' => 'bg-green-100 text-green-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    public function render()
    {
        return view('livewire.reviewer-dashboard', [
            'readings' => $this->readings,
            'stats' => $this->stats,
            'anomalousReadings' => $this->getAnomalousReadings(),
            'estates' => $this->estates,
        ]);
    }
}
