<?php

namespace App\Livewire;

use App\Models\House;
use Livewire\Component;

class HouseForm extends Component
{
    public House $house;

    protected $rules = [
        'house.house_number' => 'required|string|min:1',
        'house.estate_id' => 'required|exists:estates,id',
    ];

    public function mount(House $house)
    {
        $this->house = $house;
    }

    public function save()
    {
        $this->validate();

        $this->house->save();

        return redirect()->to('/houses');
    }

    public function render()
    {
        return view('livewire.house-form');
    }
}
