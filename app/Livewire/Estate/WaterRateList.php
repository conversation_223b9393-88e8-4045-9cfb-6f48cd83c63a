<?php

namespace App\Livewire\Estate;

use App\Models\Estate;
use App\Models\WaterRate;
use Livewire\Component;
use Livewire\WithPagination;

class WaterRateList extends Component
{
    use WithPagination;

    public Estate $estate;

    public $search = '';

    public $sortBy = 'effective_from';

    public $sortDirection = 'desc';

    protected $listeners = ['waterRateCreated' => '$refresh', 'waterRateUpdated' => '$refresh', 'waterRateDeleted' => '$refresh'];

    public function mount(Estate $estate)
    {
        $this->estate = $estate;
    }

    public function render()
    {
        $query = WaterRate::forEstate($this->estate->id);

        // Apply search filter
        if ($this->search) {
            $query->where('name', 'like', '%'.$this->search.'%');
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        return view('livewire.estate.water-rate-list', [
            'waterRates' => $query->paginate(10),
        ]);
    }

    public function deleteWaterRate(WaterRate $waterRate)
    {
        // Check if the water rate is in use by any invoices
        $invoiceCount = \App\Models\Invoice::where('water_rate_id', $waterRate->id)->count();

        if ($invoiceCount > 0) {
            $this->addError('deletion', 'Cannot delete water rate that is in use by invoices');

            return;
        }

        $waterRate->delete();
        $this->dispatch('waterRateDeleted');

        session()->flash('message', 'Water rate deleted successfully.');
    }
}
