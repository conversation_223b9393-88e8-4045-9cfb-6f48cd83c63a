<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;

class HouseImport extends Component
{
    use WithFileUploads;

    public $file;

    public function import()
    {
        $this->validate([
            'file' => 'required|mimes:csv,txt',
        ]);

        // Process the import
    }

    public function render()
    {
        return view('livewire.house-import');
    }
}
