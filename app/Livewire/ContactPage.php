<?php

namespace App\Livewire;

use App\Mail\ContactFormSubmitted;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;

class ContactPage extends Component
{
    public $name = '';

    public $email = '';

    public $phone = '';

    public $company = '';

    public $role = '';

    public $message_type = '';

    public $message = '';

    public $success = false;

    protected $rules = [
        'name' => 'required|string|min:3|max:100',
        'email' => 'required|email|max:255',
        'phone' => 'required|string|min:10|max:20',
        'company' => 'nullable|string|max:100',
        'role' => 'required|string|in:estate_manager,property_caretaker,property_owner,accountant,other',
        'message_type' => 'required|string|in:demo_request,sales_inquiry,support,partnership,general',
        'message' => 'required|string|min:10|max:1000',
    ];

    public function submit()
    {
        $this->validate();

        // Send email notification
        Mail::to('<EMAIL>')->send(new ContactFormSubmitted([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'company' => $this->company,
            'role' => $this->role,
            'message_type' => $this->message_type,
            'message' => $this->message,
        ]));

        $this->success = true;
        $this->reset(['name', 'email', 'phone', 'company', 'role', 'message_type', 'message']);
    }

    public function render()
    {
        return view('livewire.contact-page')->layout('components.layouts.public');
    }
}
