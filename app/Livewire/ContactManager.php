<?php

namespace App\Livewire;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class ContactManager extends Component
{
    use WithFileUploads, WithPagination;

    public $search = '';

    public $type = '';

    public $estate_id = '';

    public $house_id = '';

    public $perPage = 15;

    // Form fields
    public $contactId = null;

    public $first_name = '';

    public $last_name = '';

    public $middle_name = '';

    public $name = '';

    public $email = '';

    public $phone = '';

    public $whatsapp_number = '';

    public $typeField = 'owner';

    public $id_number = '';

    public $date_of_birth = '';

    public $occupation = '';

    public $company = '';

    public $postal_address = '';

    public $emergency_contact_name = '';

    public $emergency_contact_phone = '';

    public $notes = '';

    public $selectedHouses = [];

    public $is_primary = false;

    public $receive_invoices = true;

    public $receive_notifications = true;

    public $is_active = true;

    // Import
    public $importFile;

    public $updateExisting = false;

    // Modals
    public $showModal = false;

    public $showImportModal = false;

    public $showDeleteModal = false;

    public $contactToDelete = null;

    // WhatsApp Messaging
    public $showSendMessageModal = false;

    public $contactToSendWhatsApp = null;

    public $whatsappMessageContent = '';

    protected $queryString = ['search', 'type', 'estate_id', 'house_id'];

    protected $rules = [
        'first_name' => 'required|string|max:100',
        'last_name' => 'required|string|max:100',
        'middle_name' => 'nullable|string|max:100',
        'email' => 'nullable|email|max:255',
        'phone' => 'nullable|string|max:50',
        'whatsapp_number' => 'nullable|string|max:50',
        'typeField' => 'required|in:owner,tenant,caretaker,emergency',
        'id_number' => 'nullable|string|max:50',
        'date_of_birth' => 'nullable|date',
        'occupation' => 'nullable|string|max:100',
        'company' => 'nullable|string|max:255',
        'postal_address' => 'nullable|string|max:500',
        'emergency_contact_name' => 'nullable|string|max:255',
        'emergency_contact_phone' => 'nullable|string|max:50',
        'notes' => 'nullable|string',
        'selectedHouses' => 'nullable|array',
        'selectedHouses.*' => 'exists:houses,id',
        'is_primary' => 'boolean',
        'receive_invoices' => 'boolean',
        'receive_notifications' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function render()
    {
        $contacts = Contact::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('first_name', 'like', "%{$this->search}%")
                        ->orWhere('last_name', 'like', "%{$this->search}%")
                        ->orWhere('name', 'like', "%{$this->search}%")
                        ->orWhere('email', 'like', "%{$this->search}%")
                        ->orWhere('phone', 'like', "%{$this->search}%")
                        ->orWhere('whatsapp_number', 'like', "%{$this->search}%");
                });
            })
            ->when($this->type, function ($query) {
                $query->where('type', $this->type);
            })
            ->when($this->estate_id, function ($query) {
                $query->whereHas('houses', function ($q) {
                    $q->where('estate_id', $this->estate_id);
                });
            })
            ->when($this->house_id, function ($query) {
                $query->whereHas('houses', function ($q) {
                    $q->where('house_id', $this->house_id);
                });
            })
            ->with(['houses.estate'])
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->paginate($this->perPage);

        $estates = Estate::orderBy('name')->get();
        $houses = collect();

        if ($this->estate_id) {
            $houses = House::where('estate_id', $this->estate_id)
                ->orderBy('house_number')
                ->get();
        }

        $canSendWhatsAppMessages = Auth::user()->can('whatsapp.send_messages');

        return view('livewire.contact-manager', [
            'contacts' => $contacts,
            'estates' => $estates,
            'houses' => $houses,
            'canSendWhatsAppMessages' => $canSendWhatsAppMessages,
        ]);
    }

    public function create()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function edit(Contact $contact)
    {
        $this->contactId = $contact->id;
        $this->first_name = $contact->first_name;
        $this->last_name = $contact->last_name;
        $this->middle_name = $contact->middle_name;
        $this->name = $contact->name;
        $this->email = $contact->email;
        $this->phone = $contact->phone;
        $this->whatsapp_number = $contact->whatsapp_number;
        $this->typeField = $contact->type;
        $this->id_number = $contact->id_number;
        $this->date_of_birth = $contact->date_of_birth?->format('Y-m-d');
        $this->occupation = $contact->occupation;
        $this->company = $contact->company;
        $this->postal_address = $contact->postal_address;
        $this->emergency_contact_name = $contact->emergency_contact_name;
        $this->emergency_contact_phone = $contact->emergency_contact_phone;
        $this->notes = $contact->notes;
        $this->is_primary = $contact->is_primary;
        $this->receive_invoices = $contact->receive_invoices;
        $this->receive_notifications = $contact->receive_notifications;
        $this->is_active = $contact->is_active;
        $this->selectedHouses = $contact->houses->pluck('id')->toArray();

        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        $data = [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'middle_name' => $this->middle_name,
            'name' => trim($this->first_name.' '.$this->last_name),
            'email' => $this->email,
            'phone' => $this->phone,
            'whatsapp_number' => $this->whatsapp_number,
            'type' => $this->typeField,
            'id_number' => $this->id_number,
            'date_of_birth' => $this->date_of_birth,
            'occupation' => $this->occupation,
            'company' => $this->company,
            'postal_address' => $this->postal_address,
            'emergency_contact_name' => $this->emergency_contact_name,
            'emergency_contact_phone' => $this->emergency_contact_phone,
            'notes' => $this->notes,
            'is_primary' => $this->is_primary,
            'receive_invoices' => $this->receive_invoices,
            'receive_notifications' => $this->receive_notifications,
            'is_active' => $this->is_active,
        ];

        if ($this->contactId) {
            $contact = Contact::findOrFail($this->contactId);
            $contact->update($data);
        } else {
            $contact = Contact::create($data);
        }

        // Sync house relationships
        if (! empty($this->selectedHouses)) {
            $syncData = [];
            foreach ($this->selectedHouses as $houseId) {
                $syncData[$houseId] = [
                    'relationship_type' => $this->typeField,
                    'is_primary' => $this->is_primary,
                    'start_date' => now(),
                    'is_active' => true,
                ];
            }
            $contact->houses()->sync($syncData);
        } else {
            $contact->houses()->sync([]);
        }

        $this->showModal = false;
        $this->resetForm();

        session()->flash('message', $this->contactId ? 'Contact updated successfully.' : 'Contact created successfully.');
    }

    public function confirmDelete(Contact $contact)
    {
        $this->contactToDelete = $contact->id;
        $this->showDeleteModal = true;
    }

    public function delete()
    {
        if ($this->contactToDelete) {
            $contact = Contact::findOrFail($this->contactToDelete);
            $contact->houses()->detach();
            $contact->delete();
            $this->showDeleteModal = false;
            $this->contactToDelete = null;
            session()->flash('message', 'Contact deleted successfully.');
        }
    }

    public function import()
    {
        $this->validate([
            'importFile' => 'required|file|mimes:csv,xlsx,xls',
            'updateExisting' => 'boolean',
        ]);

        try {
            $result = app(\App\Services\ImportExportService::class)
                ->importContacts($this->importFile, $this->updateExisting);

            $this->showImportModal = false;
            $this->importFile = null;
            $this->updateExisting = false;

            session()->flash('message', "Imported {$result['imported']} contacts successfully. ".
                ($result['updated'] ? "{$result['updated']} updated." : '').
                ($result['errors'] ? "{$result['errors']} errors." : ''));

        } catch (\Exception $e) {
            session()->flash('error', 'Import failed: '.$e->getMessage());
        }
    }

    public function export()
    {
        return app(\App\Services\ImportExportService::class)->exportContacts();
    }

    public function resetForm()
    {
        $this->reset([
            'contactId', 'first_name', 'last_name', 'middle_name', 'name', 'email',
            'phone', 'whatsapp_number', 'typeField', 'id_number', 'date_of_birth',
            'occupation', 'company', 'postal_address', 'emergency_contact_name',
            'emergency_contact_phone', 'notes', 'selectedHouses', 'is_primary',
            'receive_invoices', 'receive_notifications', 'is_active',
            'whatsappMessageContent',
        ]);
    }

    public function updatedEstateId($value)
    {
        $this->selectedHouses = [];
    }

    public function sendMessage(Contact $contact)
    {
        if (Auth::user()->cannot('whatsapp.send_messages')) {
            session()->flash('error', 'You are not authorized to send WhatsApp messages.');

            return;
        }

        $this->contactToSendWhatsApp = $contact;
        $this->whatsappMessageContent = ''; // Reset content
        $this->showSendMessageModal = true;
    }

    public function sendWhatsAppMessage()
    {
        if (Auth::user()->cannot('whatsapp.send_messages')) {
            session()->flash('error', 'You are not authorized to send WhatsApp messages.');

            return;
        }

        $this->validate([
            'whatsappMessageContent' => 'required|string|min:1',
        ]);

        try {
            $whatsAppService = app(\App\Services\WhatsAppService::class);
            $message = $whatsAppService->sendText(
                $this->contactToSendWhatsApp->whatsapp_number,
                $this->whatsappMessageContent,
                $this->contactToSendWhatsApp
            );

            if ($message && $message->status !== 'failed') {
                session()->flash('message', 'WhatsApp message sent successfully to '.$this->contactToSendWhatsApp->name);
            } else {
                $failReason = $message ? $message->failed_reason : 'Service unavailable';
                session()->flash('error', 'Failed to send WhatsApp message: '.$failReason);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Error sending WhatsApp message: '.$e->getMessage());
        }

        $this->showSendMessageModal = false;
        $this->reset(['contactToSendWhatsApp', 'whatsappMessageContent']);
    }
}
